#!/usr/bin/env python3
"""
Quick test script to verify Perplexity API is working
"""

import os
from openai import OpenAI
from dotenv import load_dotenv

load_dotenv()

def test_perplexity():
    """Test Perplexity API directly"""
    
    perplexity_key = os.getenv("PERPLEXITY_API_KEY")
    if not perplexity_key:
        print("❌ PERPLEXITY_API_KEY not found in .env file")
        return False
    
    print(f"✅ Found Perplexity API key: {perplexity_key[:10]}...")
    
    try:
        client = OpenAI(
            api_key=perplexity_key,
            base_url="https://api.perplexity.ai"
        )
        
        print("🔄 Testing Perplexity API...")
        
        response = client.chat.completions.create(
            model="sonar-pro",
            messages=[
                {"role": "user", "content": "Hello! Can you tell me what 2+2 equals?"}
            ],
            temperature=0.7,
            max_tokens=100
        )
        
        result = response.choices[0].message.content
        print(f"✅ Perplexity API working! Response: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Perplexity API error: {e}")
        return False

if __name__ == "__main__":
    test_perplexity()

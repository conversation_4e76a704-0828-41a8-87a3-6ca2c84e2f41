import{B as Ae}from"./Button-BIUaXfcG.js";import"./Index-DB1XLvMK.js";/* empty css                                              */import Ie from"./Example-C7XUkkid.js";import"./index-BQPjLIsY.js";import"./svelte/svelte.js";/* empty css                                              */const{SvelteComponent:Re,append:H,assign:Q,attr:d,check_outros:P,construct_svelte_component:T,create_component:Y,destroy_component:D,destroy_each:J,detach:z,element:N,empty:K,ensure_array_like:E,flush:q,get_spread_object:U,get_spread_update:V,group_outros:j,init:Se,insert:B,listen:S,mount_component:F,noop:Ye,null_to_empty:ne,run_all:ve,safe_not_equal:De,set_data:x,set_style:se,space:R,svg_element:ie,text:W,toggle_class:oe,transition_in:b,transition_out:v}=window.__gradio__svelte__internal;function re(s,e,t){const n=s.slice();return n[37]=e[t],n}function fe(s,e,t){const n=s.slice();return n[40]=e[t],n[42]=t,n}function _e(s,e,t){const n=s.slice();n[0]=e[t].value,n[44]=e[t].component,n[47]=t;const l=n[1][n[47]];return n[45]=l,n}function ce(s,e,t){const n=s.slice();return n[48]=e[t],n}function ue(s,e,t){const n=s.slice();return n[40]=e[t],n[42]=t,n}function Fe(s){let e,t,n,l,o,f,i,r=E(s[5]),_=[];for(let u=0;u<r.length;u+=1)_[u]=ae(ce(s,r,u));let h=E(s[20]),a=[];for(let u=0;u<h.length;u+=1)a[u]=pe(fe(s,h,u));const k=u=>v(a[u],1,1,()=>{a[u]=null});return{c(){e=N("div"),t=N("table"),n=N("thead"),l=N("tr");for(let u=0;u<_.length;u+=1)_[u].c();o=R(),f=N("tbody");for(let u=0;u<a.length;u+=1)a[u].c();d(l,"class","tr-head svelte-p5q82i"),d(t,"tabindex","0"),d(t,"role","grid"),d(t,"class","svelte-p5q82i"),d(e,"class","table-wrap svelte-p5q82i")},m(u,g){B(u,e,g),H(e,t),H(t,n),H(n,l);for(let c=0;c<_.length;c+=1)_[c]&&_[c].m(l,null);H(t,o),H(t,f);for(let c=0;c<a.length;c+=1)a[c]&&a[c].m(f,null);i=!0},p(u,g){if(g[0]&32){r=E(u[5]);let c;for(c=0;c<r.length;c+=1){const p=ce(u,r,c);_[c]?_[c].p(p,g):(_[c]=ae(p),_[c].c(),_[c].m(l,null))}for(;c<_.length;c+=1)_[c].d(1);_.length=r.length}if(g[0]&30985231){h=E(u[20]);let c;for(c=0;c<h.length;c+=1){const p=fe(u,h,c);a[c]?(a[c].p(p,g),b(a[c],1)):(a[c]=pe(p),a[c].c(),b(a[c],1),a[c].m(f,null))}for(j(),c=h.length;c<a.length;c+=1)k(c);P()}},i(u){if(!i){for(let g=0;g<h.length;g+=1)b(a[g]);i=!0}},o(u){a=a.filter(Boolean);for(let g=0;g<a.length;g+=1)v(a[g]);i=!1},d(u){u&&z(e),J(_,u),J(a,u)}}}function Ge(s){let e,t,n=E(s[17]),l=[];for(let f=0;f<n.length;f+=1)l[f]=de(ue(s,n,f));const o=f=>v(l[f],1,1,()=>{l[f]=null});return{c(){e=N("div");for(let f=0;f<l.length;f+=1)l[f].c();d(e,"class","gallery svelte-p5q82i")},m(f,i){B(f,e,i);for(let r=0;r<l.length;r+=1)l[r]&&l[r].m(e,null);t=!0},p(f,i){if(i[0]&31116367){n=E(f[17]);let r;for(r=0;r<n.length;r+=1){const _=ue(f,n,r);l[r]?(l[r].p(_,i),b(l[r],1)):(l[r]=de(_),l[r].c(),b(l[r],1),l[r].m(e,null))}for(j(),r=n.length;r<l.length;r+=1)o(r);P()}},i(f){if(!t){for(let i=0;i<n.length;i+=1)b(l[i]);t=!0}},o(f){l=l.filter(Boolean);for(let i=0;i<l.length;i+=1)v(l[i]);t=!1},d(f){f&&z(e),J(l,f)}}}function ae(s){let e,t=s[48]+"",n,l;return{c(){e=N("th"),n=W(t),l=R(),d(e,"class","svelte-p5q82i")},m(o,f){B(o,e,f),H(e,n),H(e,l)},p(o,f){f[0]&32&&t!==(t=o[48]+"")&&x(n,t)},d(o){o&&z(e)}}}function me(s){let e,t,n,l;const o=[s[2][s[47]],{value:s[0]},{samples_dir:s[22]},{type:"table"},{selected:s[19]===s[42]},{index:s[42]},{root:s[10]}];var f=s[44];function i(r,_){let h={};for(let a=0;a<o.length;a+=1)h=Q(h,o[a]);return _!==void 0&&_[0]&5768196&&(h=Q(h,V(o,[_[0]&4&&U(r[2][r[47]]),_[0]&1048576&&{value:r[0]},_[0]&4194304&&{samples_dir:r[22]},o[3],_[0]&524288&&{selected:r[19]===r[42]},o[5],_[0]&1024&&{root:r[10]}]))),{props:h}}return f&&(t=T(f,i(s))),{c(){e=N("td"),t&&Y(t.$$.fragment),se(e,"max-width",s[45]==="textbox"?"35ch":"auto"),d(e,"class",n=ne(s[45])+" svelte-p5q82i")},m(r,_){B(r,e,_),t&&F(t,e,null),l=!0},p(r,_){if(_[0]&1048576&&f!==(f=r[44])){if(t){j();const h=t;v(h.$$.fragment,1,0,()=>{D(h,1)}),P()}f?(t=T(f,i(r,_)),Y(t.$$.fragment),b(t.$$.fragment,1),F(t,e,null)):t=null}else if(f){const h=_[0]&5768196?V(o,[_[0]&4&&U(r[2][r[47]]),_[0]&1048576&&{value:r[0]},_[0]&4194304&&{samples_dir:r[22]},o[3],_[0]&524288&&{selected:r[19]===r[42]},o[5],_[0]&1024&&{root:r[10]}]):{};t.$set(h)}(!l||_[0]&2)&&se(e,"max-width",r[45]==="textbox"?"35ch":"auto"),(!l||_[0]&2&&n!==(n=ne(r[45])+" svelte-p5q82i"))&&d(e,"class",n)},i(r){l||(t&&b(t.$$.fragment,r),l=!0)},o(r){t&&v(t.$$.fragment,r),l=!1},d(r){r&&z(e),t&&D(t)}}}function he(s){let e=s[45]!==void 0&&s[3].get(s[45])!==void 0,t,n,l=e&&me(s);return{c(){l&&l.c(),t=K()},m(o,f){l&&l.m(o,f),B(o,t,f),n=!0},p(o,f){f[0]&10&&(e=o[45]!==void 0&&o[3].get(o[45])!==void 0),e?l?(l.p(o,f),f[0]&10&&b(l,1)):(l=me(o),l.c(),b(l,1),l.m(t.parentNode,t)):l&&(j(),v(l,1,1,()=>{l=null}),P())},i(o){n||(b(l),n=!0)},o(o){v(l),n=!1},d(o){o&&z(t),l&&l.d(o)}}}function pe(s){let e,t,n,l,o,f=E(s[40]),i=[];for(let a=0;a<f.length;a+=1)i[a]=he(_e(s,f,a));const r=a=>v(i[a],1,1,()=>{i[a]=null});function _(){return s[32](s[42])}function h(){return s[33](s[42])}return{c(){e=N("tr");for(let a=0;a<i.length;a+=1)i[a].c();t=R(),d(e,"class","tr-body svelte-p5q82i")},m(a,k){B(a,e,k);for(let u=0;u<i.length;u+=1)i[u]&&i[u].m(e,null);H(e,t),n=!0,l||(o=[S(e,"click",_),S(e,"mouseenter",h),S(e,"mouseleave",s[34])],l=!0)},p(a,k){if(s=a,k[0]&5768206){f=E(s[40]);let u;for(u=0;u<f.length;u+=1){const g=_e(s,f,u);i[u]?(i[u].p(g,k),b(i[u],1)):(i[u]=he(g),i[u].c(),b(i[u],1),i[u].m(e,t))}for(j(),u=f.length;u<i.length;u+=1)r(u);P()}},i(a){if(!n){for(let k=0;k<f.length;k+=1)b(i[k]);n=!0}},o(a){i=i.filter(Boolean);for(let k=0;k<i.length;k+=1)v(i[k]);n=!1},d(a){a&&z(e),J(i,a),l=!1,ve(o)}}}function ge(s){let e,t,n,l,o,f,i,r;const _=[Ke,Je],h=[];function a(g,c){return c[0]&1048586&&(t=null),g[6]?0:(t==null&&(t=!!(g[20].length&&g[3].get(g[1][0]))),t?1:-1)}~(n=a(s,[-1,-1]))&&(l=h[n]=_[n](s));function k(){return s[29](s[42],s[40])}function u(){return s[30](s[42])}return{c(){e=N("button"),l&&l.c(),o=R(),d(e,"class","gallery-item svelte-p5q82i")},m(g,c){B(g,e,c),~n&&h[n].m(e,null),H(e,o),f=!0,i||(r=[S(e,"click",k),S(e,"mouseenter",u),S(e,"mouseleave",s[31])],i=!0)},p(g,c){s=g;let p=n;n=a(s,c),n===p?~n&&h[n].p(s,c):(l&&(j(),v(h[p],1,1,()=>{h[p]=null}),P()),~n?(l=h[n],l?l.p(s,c):(l=h[n]=_[n](s),l.c()),b(l,1),l.m(e,o)):l=null)},i(g){f||(b(l),f=!0)},o(g){v(l),f=!1},d(g){g&&z(e),~n&&h[n].d(),i=!1,ve(r)}}}function Je(s){let e,t,n;const l=[s[2][0],{value:s[40][0]},{samples_dir:s[22]},{type:"gallery"},{selected:s[19]===s[42]},{index:s[42]},{root:s[10]}];var o=s[20][0][0].component;function f(i,r){let _={};for(let h=0;h<l.length;h+=1)_=Q(_,l[h]);return r!==void 0&&r[0]&4850692&&(_=Q(_,V(l,[r[0]&4&&U(i[2][0]),r[0]&131072&&{value:i[40][0]},r[0]&4194304&&{samples_dir:i[22]},l[3],r[0]&524288&&{selected:i[19]===i[42]},l[5],r[0]&1024&&{root:i[10]}]))),{props:_}}return o&&(e=T(o,f(s))),{c(){e&&Y(e.$$.fragment),t=K()},m(i,r){e&&F(e,i,r),B(i,t,r),n=!0},p(i,r){if(r[0]&1048576&&o!==(o=i[20][0][0].component)){if(e){j();const _=e;v(_.$$.fragment,1,0,()=>{D(_,1)}),P()}o?(e=T(o,f(i,r)),Y(e.$$.fragment),b(e.$$.fragment,1),F(e,t.parentNode,t)):e=null}else if(o){const _=r[0]&4850692?V(l,[r[0]&4&&U(i[2][0]),r[0]&131072&&{value:i[40][0]},r[0]&4194304&&{samples_dir:i[22]},l[3],r[0]&524288&&{selected:i[19]===i[42]},l[5],r[0]&1024&&{root:i[10]}]):{};e.$set(_)}},i(i){n||(e&&b(e.$$.fragment,i),n=!0)},o(i){e&&v(e.$$.fragment,i),n=!1},d(i){i&&z(t),e&&D(e,i)}}}function Ke(s){let e,t;return e=new Ie({props:{value:s[40][0],selected:s[19]===s[42],type:"gallery"}}),{c(){Y(e.$$.fragment)},m(n,l){F(e,n,l),t=!0},p(n,l){const o={};l[0]&131072&&(o.value=n[40][0]),l[0]&524288&&(o.selected=n[19]===n[42]),e.$set(o)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){v(e.$$.fragment,n),t=!1},d(n){D(e,n)}}}function de(s){let e,t,n=s[40][0]&&ge(s);return{c(){n&&n.c(),e=K()},m(l,o){n&&n.m(l,o),B(l,e,o),t=!0},p(l,o){l[40][0]?n?(n.p(l,o),o[0]&131072&&b(n,1)):(n=ge(l),n.c(),b(n,1),n.m(e.parentNode,e)):n&&(j(),v(n,1,1,()=>{n=null}),P())},i(l){t||(b(n),t=!0)},o(l){v(n),t=!1},d(l){l&&z(e),n&&n.d(l)}}}function be(s){let e,t,n=E(s[18]),l=[];for(let o=0;o<n.length;o+=1)l[o]=ke(re(s,n,o));return{c(){e=N("div"),t=W(`Pages:
			`);for(let o=0;o<l.length;o+=1)l[o].c();d(e,"class","paginate svelte-p5q82i")},m(o,f){B(o,e,f),H(e,t);for(let i=0;i<l.length;i+=1)l[i]&&l[i].m(e,null)},p(o,f){if(f[0]&294912){n=E(o[18]);let i;for(i=0;i<n.length;i+=1){const r=re(o,n,i);l[i]?l[i].p(r,f):(l[i]=ke(r),l[i].c(),l[i].m(e,null))}for(;i<l.length;i+=1)l[i].d(1);l.length=n.length}},d(o){o&&z(e),J(l,o)}}}function Le(s){let e,t=s[37]+1+"",n,l,o,f;function i(){return s[35](s[37])}return{c(){e=N("button"),n=W(t),l=R(),d(e,"class","svelte-p5q82i"),oe(e,"current-page",s[15]===s[37])},m(r,_){B(r,e,_),H(e,n),H(e,l),o||(f=S(e,"click",i),o=!0)},p(r,_){s=r,_[0]&262144&&t!==(t=s[37]+1+"")&&x(n,t),_[0]&294912&&oe(e,"current-page",s[15]===s[37])},d(r){r&&z(e),o=!1,f()}}}function Oe(s){let e;return{c(){e=N("div"),e.textContent="..."},m(t,n){B(t,e,n)},p:Ye,d(t){t&&z(e)}}}function ke(s){let e;function t(o,f){return o[37]===-1?Oe:Le}let n=t(s),l=n(s);return{c(){l.c(),e=K()},m(o,f){l.m(o,f),B(o,e,f)},p(o,f){n===(n=t(o))&&l?l.p(o,f):(l.d(1),l=n(o),l&&(l.c(),l.m(e.parentNode,e)))},d(o){o&&z(e),l.d(o)}}}function Qe(s){let e,t,n,l,o,f,i,r,_,h,a;const k=[Ge,Fe],u=[];function g(p,w){return p[21]?0:1}i=g(s),r=u[i]=k[i](s);let c=s[16]&&be(s);return{c(){e=N("div"),t=ie("svg"),n=ie("path"),l=R(),o=W(s[4]),f=R(),r.c(),_=R(),c&&c.c(),h=K(),d(n,"fill","currentColor"),d(n,"d","M10 6h18v2H10zm0 18h18v2H10zm0-9h18v2H10zm-6 0h2v2H4zm0-9h2v2H4zm0 18h2v2H4z"),d(t,"xmlns","http://www.w3.org/2000/svg"),d(t,"xmlns:xlink","http://www.w3.org/1999/xlink"),d(t,"aria-hidden","true"),d(t,"role","img"),d(t,"width","1em"),d(t,"height","1em"),d(t,"preserveAspectRatio","xMidYMid meet"),d(t,"viewBox","0 0 32 32"),d(t,"class","svelte-p5q82i"),d(e,"class","label svelte-p5q82i")},m(p,w){B(p,e,w),H(e,t),H(t,n),H(e,l),H(e,o),B(p,f,w),u[i].m(p,w),B(p,_,w),c&&c.m(p,w),B(p,h,w),a=!0},p(p,w){(!a||w[0]&16)&&x(o,p[4]);let M=i;i=g(p),i===M?u[i].p(p,w):(j(),v(u[M],1,1,()=>{u[M]=null}),P(),r=u[i],r?r.p(p,w):(r=u[i]=k[i](p),r.c()),b(r,1),r.m(_.parentNode,_)),p[16]?c?c.p(p,w):(c=be(p),c.c(),c.m(h.parentNode,h)):c&&(c.d(1),c=null)},i(p){a||(b(r),a=!0)},o(p){v(r),a=!1},d(p){p&&(z(e),z(f),z(_),z(h)),u[i].d(p),c&&c.d(p)}}}function Te(s){let e,t;return e=new Ae({props:{visible:s[9],padding:!1,elem_id:s[7],elem_classes:s[8],scale:s[12],min_width:s[13],allow_overflow:!1,container:!1,$$slots:{default:[Qe]},$$scope:{ctx:s}}}),{c(){Y(e.$$.fragment)},m(n,l){F(e,n,l),t=!0},p(n,l){const o={};l[0]&512&&(o.visible=n[9]),l[0]&128&&(o.elem_id=n[7]),l[0]&256&&(o.elem_classes=n[8]),l[0]&4096&&(o.scale=n[12]),l[0]&8192&&(o.min_width=n[13]),l[0]&4181119|l[1]&1048576&&(o.$$scope={dirty:l,ctx:n}),e.$set(o)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){v(e.$$.fragment,n),t=!1},d(n){D(e,n)}}}function Ue(s,e,t){let n,{components:l}=e,{component_props:o}=e,{component_map:f}=e,{label:i="Examples"}=e,{headers:r}=e,{samples:_=null}=e,h=null,{sample_labels:a=null}=e,{elem_id:k=""}=e,{elem_classes:u=[]}=e,{visible:g=!0}=e,{value:c=null}=e,{root:p}=e,{proxy_url:w}=e,{samples_per_page:M=10}=e,{scale:ee=null}=e,{min_width:le=void 0}=e,{gradio:G}=e,we=w?`/proxy=${w}file=`:`${p}/file=`,A=0,X=_?_.length>M:!1,L,O,I=[],Z=-1;function y(m){t(19,Z=m)}function $(){t(19,Z=-1)}let te=[];async function qe(m){t(20,te=await Promise.all(m&&m.map(async C=>await Promise.all(C.map(async(Pe,je)=>({value:Pe,component:(await f.get(l[je]))?.default}))))))}const ze=(m,C)=>{t(0,c=m+A*M),G.dispatch("click",c),G.dispatch("select",{index:c,value:C})},Be=m=>y(m),He=()=>$(),Ne=m=>{t(0,c=m+A*M),G.dispatch("click",c)},Me=m=>y(m),Ce=()=>$(),Ee=m=>t(15,A=m);return s.$$set=m=>{"components"in m&&t(1,l=m.components),"component_props"in m&&t(2,o=m.component_props),"component_map"in m&&t(3,f=m.component_map),"label"in m&&t(4,i=m.label),"headers"in m&&t(5,r=m.headers),"samples"in m&&t(25,_=m.samples),"sample_labels"in m&&t(6,a=m.sample_labels),"elem_id"in m&&t(7,k=m.elem_id),"elem_classes"in m&&t(8,u=m.elem_classes),"visible"in m&&t(9,g=m.visible),"value"in m&&t(0,c=m.value),"root"in m&&t(10,p=m.root),"proxy_url"in m&&t(26,w=m.proxy_url),"samples_per_page"in m&&t(11,M=m.samples_per_page),"scale"in m&&t(12,ee=m.scale),"min_width"in m&&t(13,le=m.min_width),"gradio"in m&&t(14,G=m.gradio)},s.$$.update=()=>{s.$$.dirty[0]&66&&t(21,n=l.length<2||a!==null),s.$$.dirty[0]&436570176&&(a?t(25,_=a.map(m=>[m])):_||t(25,_=[]),_!==h&&(t(15,A=0),t(27,h=_)),t(16,X=_.length>M),X?(t(18,I=[]),t(17,L=_.slice(A*M,(A+1)*M)),t(28,O=Math.ceil(_.length/M)),[0,A,O-1].forEach(m=>{for(let C=m-2;C<=m+2;C++)C>=0&&C<O&&!I.includes(C)&&(I.length>0&&C-I[I.length-1]>1&&I.push(-1),I.push(C))})):t(17,L=_.slice())),s.$$.dirty[0]&131080&&qe(L)},[c,l,o,f,i,r,a,k,u,g,p,M,ee,le,G,A,X,L,I,Z,te,n,we,y,$,_,w,h,O,ze,Be,He,Ne,Me,Ce,Ee]}class el extends Re{constructor(e){super(),Se(this,e,Ue,Te,De,{components:1,component_props:2,component_map:3,label:4,headers:5,samples:25,sample_labels:6,elem_id:7,elem_classes:8,visible:9,value:0,root:10,proxy_url:26,samples_per_page:11,scale:12,min_width:13,gradio:14},null,[-1,-1])}get components(){return this.$$.ctx[1]}set components(e){this.$$set({components:e}),q()}get component_props(){return this.$$.ctx[2]}set component_props(e){this.$$set({component_props:e}),q()}get component_map(){return this.$$.ctx[3]}set component_map(e){this.$$set({component_map:e}),q()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),q()}get headers(){return this.$$.ctx[5]}set headers(e){this.$$set({headers:e}),q()}get samples(){return this.$$.ctx[25]}set samples(e){this.$$set({samples:e}),q()}get sample_labels(){return this.$$.ctx[6]}set sample_labels(e){this.$$set({sample_labels:e}),q()}get elem_id(){return this.$$.ctx[7]}set elem_id(e){this.$$set({elem_id:e}),q()}get elem_classes(){return this.$$.ctx[8]}set elem_classes(e){this.$$set({elem_classes:e}),q()}get visible(){return this.$$.ctx[9]}set visible(e){this.$$set({visible:e}),q()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),q()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),q()}get proxy_url(){return this.$$.ctx[26]}set proxy_url(e){this.$$set({proxy_url:e}),q()}get samples_per_page(){return this.$$.ctx[11]}set samples_per_page(e){this.$$set({samples_per_page:e}),q()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),q()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),q()}get gradio(){return this.$$.ctx[14]}set gradio(e){this.$$set({gradio:e}),q()}}export{el as default};
//# sourceMappingURL=Index-BEKHNubq.js.map

{"version": 3, "file": "Example-BoMLuz1A.js", "sources": ["../../../../js/radio/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: string | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\texport let choices: [string, string | number][];\n\n\tlet name_string: string;\n\n\tif (value === null) {\n\t\tname_string = \"\";\n\t} else {\n\t\tlet name = choices.find((pair) => pair[1] === value);\n\t\tname_string = name ? name[0] : \"\";\n\t}\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{name_string}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n"], "names": ["ctx", "toggle_class", "div", "insert", "target", "anchor", "value", "$$props", "type", "selected", "choices", "name_string", "name", "pair"], "mappings": "+NAqBEA,EAAW,CAAA,CAAA,gCAJCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAW,CAAA,CAAA,OAJCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,0EAjBtB,MAAAM,CAAoB,EAAAC,GACpB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,GAChB,QAAAG,CAAoC,EAAAH,EAE3CI,EAEA,GAAAL,IAAU,KACbK,EAAc,YAEVC,EAAOF,EAAQ,KAAMG,GAASA,EAAK,CAAC,IAAMP,CAAK,EACnDK,EAAcC,EAAOA,EAAK,CAAC,EAAI"}
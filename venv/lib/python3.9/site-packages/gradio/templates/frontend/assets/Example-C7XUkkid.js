/* empty css                                              */const{SvelteComponent:y,add_iframe_resize_listener:v,add_render_callback:b,append:m,attr:p,binding_callbacks:w,detach:z,element:k,flush:c,init:S,insert:q,noop:f,safe_not_equal:C,set_data:E,text:M,toggle_class:_}=window.__gradio__svelte__internal,{onMount:P}=window.__gradio__svelte__internal;function W(t){let e,s=(t[0]?t[0]:"")+"",r,u;return{c(){e=k("div"),r=M(s),p(e,"class","svelte-1viwdyg"),b(()=>t[5].call(e)),_(e,"table",t[1]==="table"),_(e,"gallery",t[1]==="gallery"),_(e,"selected",t[2])},m(l,n){q(l,e,n),m(e,r),u=v(e,t[5].bind(e)),t[6](e)},p(l,[n]){n&1&&s!==(s=(l[0]?l[0]:"")+"")&&E(r,s),n&2&&_(e,"table",l[1]==="table"),n&2&&_(e,"gallery",l[1]==="gallery"),n&4&&_(e,"selected",l[2])},i:f,o:f,d(l){l&&z(e),u(),t[6](null)}}}function j(t,e,s){let{value:r}=e,{type:u}=e,{selected:l=!1}=e,n,a;function o(i,d){!i||!d||(a.style.setProperty("--local-text-width",`${d<150?d:200}px`),s(4,a.style.whiteSpace="unset",a))}P(()=>{o(a,n)});function g(){n=this.clientWidth,s(3,n)}function h(i){w[i?"unshift":"push"](()=>{a=i,s(4,a)})}return t.$$set=i=>{"value"in i&&s(0,r=i.value),"type"in i&&s(1,u=i.type),"selected"in i&&s(2,l=i.selected)},[r,u,l,n,a,g,h]}class B extends y{constructor(e){super(),S(this,e,j,W,C,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),c()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),c()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),c()}}export{B as default};
//# sourceMappingURL=Example-C7XUkkid.js.map

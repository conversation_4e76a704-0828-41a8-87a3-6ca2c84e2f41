import{I as j}from"./Image-CJc3fwmN.js";import"./Index-DB1XLvMK.js";import"./index-BQPjLIsY.js";/* empty css                                                   */import{V as A}from"./Video-BnSeZxqU.js";import"./file-url-SIRImsEF.js";import"./svelte/svelte.js";const{SvelteComponent:D,add_iframe_resize_listener:F,add_render_callback:G,append:v,attr:w,binding_callbacks:H,check_outros:B,create_component:C,destroy_component:E,destroy_each:J,detach:b,element:z,empty:K,ensure_array_like:$,flush:y,group_outros:M,init:L,insert:k,mount_component:N,noop:h,safe_not_equal:O,set_data:P,space:Q,src_url_equal:I,text:W,toggle_class:m,transition_in:p,transition_out:g}=window.__gradio__svelte__internal,{onMount:R}=window.__gradio__svelte__internal;function S(s,e,n){const l=s.slice();return l[8]=e[n],l}function T(s){let e=s[8].orig_name+"",n;return{c(){n=W(e)},m(l,r){k(l,n,r)},p(l,r){r&1&&e!==(e=l[8].orig_name+"")&&P(n,e)},i:h,o:h,d(l){l&&b(n)}}}function U(s){let e,n;return{c(){e=z("audio"),I(e.src,n=s[8].url)||w(e,"src",n),e.controls=!0},m(l,r){k(l,e,r)},p(l,r){r&1&&!I(e.src,n=l[8].url)&&w(e,"src",n)},i:h,o:h,d(l){l&&b(e)}}}function X(s){let e,n;return e=new A({props:{src:s[8].url,alt:"",loop:!0}}),{c(){C(e.$$.fragment)},m(l,r){N(e,l,r),n=!0},p(l,r){const c={};r&1&&(c.src=l[8].url),e.$set(c)},i(l){n||(p(e.$$.fragment,l),n=!0)},o(l){g(e.$$.fragment,l),n=!1},d(l){E(e,l)}}}function Y(s){let e,n;return e=new j({props:{src:s[8].url,alt:""}}),{c(){C(e.$$.fragment)},m(l,r){N(e,l,r),n=!0},p(l,r){const c={};r&1&&(c.src=l[8].url),e.$set(c)},i(l){n||(p(e.$$.fragment,l),n=!0)},o(l){g(e.$$.fragment,l),n=!1},d(l){E(e,l)}}}function V(s){let e,n,l,r,c,f,_;const a=[Y,X,U,T],i=[];function d(t,o){return o&1&&(e=null),o&1&&(n=null),o&1&&(l=null),e==null&&(e=!!(t[8].mime_type&&t[8].mime_type.includes("image"))),e?0:(n==null&&(n=!!(t[8].mime_type&&t[8].mime_type.includes("video"))),n?1:(l==null&&(l=!!(t[8].mime_type&&t[8].mime_type.includes("audio"))),l?2:3))}return r=d(s,-1),c=i[r]=a[r](s),{c(){c.c(),f=K()},m(t,o){i[r].m(t,o),k(t,f,o),_=!0},p(t,o){let u=r;r=d(t,o),r===u?i[r].p(t,o):(M(),g(i[u],1,1,()=>{i[u]=null}),B(),c=i[r],c?c.p(t,o):(c=i[r]=a[r](t),c.c()),p(c,1),c.m(f.parentNode,f))},i(t){_||(p(c),_=!0)},o(t){g(c),_=!1},d(t){t&&b(f),i[r].d(t)}}}function Z(s){let e,n,l=(s[0].text?s[0].text:"")+"",r,c,f,_,a=$(s[0].files),i=[];for(let t=0;t<a.length;t+=1)i[t]=V(S(s,a,t));const d=t=>g(i[t],1,1,()=>{i[t]=null});return{c(){e=z("div"),n=z("p"),r=W(l),c=Q();for(let t=0;t<i.length;t+=1)i[t].c();w(e,"class","container svelte-1cl8bqt"),G(()=>s[5].call(e)),m(e,"table",s[1]==="table"),m(e,"gallery",s[1]==="gallery"),m(e,"selected",s[2]),m(e,"border",s[0])},m(t,o){k(t,e,o),v(e,n),v(n,r),v(e,c);for(let u=0;u<i.length;u+=1)i[u]&&i[u].m(e,null);f=F(e,s[5].bind(e)),s[6](e),_=!0},p(t,[o]){if((!_||o&1)&&l!==(l=(t[0].text?t[0].text:"")+"")&&P(r,l),o&1){a=$(t[0].files);let u;for(u=0;u<a.length;u+=1){const q=S(t,a,u);i[u]?(i[u].p(q,o),p(i[u],1)):(i[u]=V(q),i[u].c(),p(i[u],1),i[u].m(e,null))}for(M(),u=a.length;u<i.length;u+=1)d(u);B()}(!_||o&2)&&m(e,"table",t[1]==="table"),(!_||o&2)&&m(e,"gallery",t[1]==="gallery"),(!_||o&4)&&m(e,"selected",t[2]),(!_||o&1)&&m(e,"border",t[0])},i(t){if(!_){for(let o=0;o<a.length;o+=1)p(i[o]);_=!0}},o(t){i=i.filter(Boolean);for(let o=0;o<i.length;o+=1)g(i[o]);_=!1},d(t){t&&b(e),J(i,t),f(),s[6](null)}}}function x(s,e,n){let{value:l={text:"",files:[]}}=e,{type:r}=e,{selected:c=!1}=e,f,_;function a(t,o){!t||!o||(_.style.setProperty("--local-text-width",`${o<150?o:200}px`),n(4,_.style.whiteSpace="unset",_))}R(()=>{a(_,f)});function i(){f=this.clientWidth,n(3,f)}function d(t){H[t?"unshift":"push"](()=>{_=t,n(4,_)})}return s.$$set=t=>{"value"in t&&n(0,l=t.value),"type"in t&&n(1,r=t.type),"selected"in t&&n(2,c=t.selected)},[l,r,c,f,_,i,d]}class se extends D{constructor(e){super(),L(this,e,x,Z,O,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),y()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),y()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),y()}}export{se as default};
//# sourceMappingURL=Example-DxaKKsjH.js.map

{"version": 3, "file": "DownloadLink-CHpWw1Ex.js", "sources": ["../../../../js/wasm/svelte/DownloadLink.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { HTMLAnchorAttributes } from \"svelte/elements\";\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\n\tinterface DownloadLinkAttributes\n\t\textends Omit<HTMLAnchorAttributes, \"target\"> {\n\t\tdownload: NonNullable<HTMLAnchorAttributes[\"download\"]>;\n\t}\n\ttype $$Props = DownloadLinkAttributes;\n\n\timport { getWorkerProxyContext } from \"./context\";\n\timport { should_proxy_wasm_src } from \"./file-url\";\n\timport { getHeaderValue } from \"../src/http\";\n\n\texport let href: DownloadLinkAttributes[\"href\"] = undefined;\n\texport let download: DownloadLinkAttributes[\"download\"];\n\n\tconst dispatch = createEventDispatcher();\n\n\tlet is_downloading = false;\n\tconst worker_proxy = getWorkerProxyContext();\n\tasync function wasm_click_handler(): Promise<void> {\n\t\tif (is_downloading) {\n\t\t\treturn;\n\t\t}\n\n\t\tdispatch(\"click\");\n\n\t\tif (href == null) {\n\t\t\tthrow new Error(\"href is not defined.\");\n\t\t}\n\t\tif (worker_proxy == null) {\n\t\t\tthrow new Error(\"Wasm worker proxy is not available.\");\n\t\t}\n\n\t\tconst url = new URL(href, window.location.href);\n\t\tconst path = url.pathname;\n\n\t\tis_downloading = true;\n\t\tworker_proxy\n\t\t\t.httpRequest({\n\t\t\t\tmethod: \"GET\",\n\t\t\t\tpath,\n\t\t\t\theaders: {},\n\t\t\t\tquery_string: \"\"\n\t\t\t})\n\t\t\t.then((response) => {\n\t\t\t\tif (response.status !== 200) {\n\t\t\t\t\tthrow new Error(`Failed to get file ${path} from the Wasm worker.`);\n\t\t\t\t}\n\t\t\t\tconst blob = new Blob([response.body], {\n\t\t\t\t\ttype: getHeaderValue(response.headers, \"content-type\")\n\t\t\t\t});\n\t\t\t\tconst blobUrl = URL.createObjectURL(blob);\n\n\t\t\t\tconst link = document.createElement(\"a\");\n\t\t\t\tlink.href = blobUrl;\n\t\t\t\tlink.download = download;\n\t\t\t\tlink.click();\n\n\t\t\t\tURL.revokeObjectURL(blobUrl);\n\t\t\t})\n\t\t\t.finally(() => {\n\t\t\t\tis_downloading = false;\n\t\t\t});\n\t}\n</script>\n\n{#if worker_proxy && should_proxy_wasm_src(href)}\n\t{#if is_downloading}\n\t\t<slot />\n\t{:else}\n\t\t<a {...$$restProps} {href} on:click|preventDefault={wasm_click_handler}>\n\t\t\t<slot />\n\t\t</a>\n\t{/if}\n{:else}\n\t<a\n\t\t{href}\n\t\ttarget={typeof window !== \"undefined\" && window.__is_colab__\n\t\t\t? \"_blank\"\n\t\t\t: null}\n\t\trel=\"noopener noreferrer\"\n\t\t{download}\n\t\t{...$$restProps}\n\t\ton:click={dispatch.bind(null, \"click\")}\n\t>\n\t\t<slot />\n\t</a>\n{/if}\n"], "names": ["onMount", "a_target_value", "ctx", "insert", "target", "a", "anchor", "dispose", "listen", "should_proxy_wasm_src", "href", "$$props", "download", "dispatch", "createEventDispatcher", "is_downloading", "worker_proxy", "getWorkerProxyContext", "wasm_click_handler", "path", "$$invalidate", "response", "blob", "getHeaderValue", "blobUrl", "link"], "mappings": "ufAE+B,QAAAA,IAAS,OAAgB,mHA6EvC,OAAAC,EAAA,OAAA,OAAW,KAAe,OAAO,aAC7C,SACA,kDAGCC,EAAW,CAAA,4FAPhBC,EAWGC,EAAAC,EAAAC,CAAA,0BAHQC,EAAAC,EAAAH,EAAA,QAAAH,EAAS,CAAA,EAAA,KAAK,KAAM,OAAO,CAAA,6LADjCA,EAAW,CAAA,qJAfXA,EAAc,CAAA,EAAA,+WAGXA,EAAW,CAAA,EAAA,CAAA,KAAAA,EAAA,CAAA,CAAA,CAAA,2FAAlBC,EAEGC,EAAAC,EAAAC,CAAA,0CAFiDJ,EAAkB,CAAA,CAAA,CAAA,oGAA/DA,EAAW,CAAA,0bAJfA,EAAY,CAAA,GAAIO,EAAsBP,EAAI,CAAA,CAAA,yYAtDnC,CAAA,KAAAQ,EAAuC,MAAS,EAAAC,GAChD,SAAAC,CAA4C,EAAAD,EAEjD,MAAAE,EAAWC,IAEb,IAAAC,EAAiB,GACf,MAAAC,EAAeC,mBACNC,GAAkB,IAC5BH,SAMA,GAFJF,EAAS,OAAO,EAEZH,GAAQ,KACD,MAAA,IAAA,MAAM,sBAAsB,EAEnC,GAAAM,GAAgB,KACT,MAAA,IAAA,MAAM,qCAAqC,QAIhDG,EADG,IAAO,IAAIT,EAAM,OAAO,SAAS,IAAI,EAC7B,SAEjBU,EAAA,EAAAL,EAAiB,EAAI,EACrBC,EACE,YAAW,CACX,OAAQ,MACR,KAAAG,EACA,QAAO,CAAA,EACP,aAAc,EAEd,CAAA,EAAA,KAAME,GAAQ,IACVA,EAAS,SAAW,IACb,MAAA,IAAA,4BAA4BF,CAAI,wBAAA,EAErC,MAAAG,EAAW,IAAA,KAAM,CAAAD,EAAS,IAAI,GACnC,KAAME,EAAeF,EAAS,QAAS,cAAc,IAEhDG,EAAU,IAAI,gBAAgBF,CAAI,EAElCG,EAAO,SAAS,cAAc,GAAG,EACvCA,EAAK,KAAOD,EACZC,EAAK,SAAWb,EAChBa,EAAK,MAAK,EAEV,IAAI,gBAAgBD,CAAO,IAE3B,QAAO,IAAA,CACPJ,EAAA,EAAAL,EAAiB,EAAK"}
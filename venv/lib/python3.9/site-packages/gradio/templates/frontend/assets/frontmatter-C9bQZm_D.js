import{s as m,t as a,f as i,c as s,p,S as l}from"./Index-CKgowMni.js";import{yaml as f}from"./yaml-DsCXHVTH.js";import"./index-BQPjLIsY.js";import"./svelte/svelte.js";import"./Check-CZUQOzJl.js";import"./Copy-B6RcHnoK.js";import"./Index-DB1XLvMK.js";import"./Button-BIUaXfcG.js";import"./Download-DVtk-Jv3.js";import"./DownloadLink-CHpWw1Ex.js";import"./file-url-SIRImsEF.js";import"./Code-DGNrTu_I.js";import"./BlockLabel-BlSr62f_.js";import"./Empty-BgF7sXBn.js";import"./Example-Wp-_4AVX.js";const n=/^---\s*$/m,N={defineNodes:[{name:"Frontmatter",block:!0},"FrontmatterMark"],props:[m({Frontmatter:[a.documentMeta,a.monospace],FrontmatterMark:a.processingInstruction}),i.add({Frontmatter:s,FrontmatterMark:()=>null})],wrap:p(t=>{const{parser:e}=l.define(f);return t.type.name==="Frontmatter"?{parser:e,overlay:[{from:t.from+4,to:t.to-4}]}:null}),parseBlock:[{name:"Frontmatter",before:"HorizontalRule",parse:(t,e)=>{let r;const o=new Array;if(t.lineStart===0&&n.test(e.text)){for(o.push(t.elt("FrontmatterMark",0,4));t.nextLine();)if(n.test(e.text)){r=t.lineStart+4;break}return r!==void 0&&(o.push(t.elt("FrontmatterMark",r-4,r)),t.addElement(t.elt("Frontmatter",0,r,o))),!0}return!1}}]};export{N as frontmatter};
//# sourceMappingURL=frontmatter-C9bQZm_D.js.map

{"version": 3, "file": "Index-BMLc4VxK.js", "sources": ["../../../../js/timer/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { onDestroy } from \"svelte\";\n\texport let gradio: Gradio<{\n\t\ttick: never;\n\t}>;\n\texport let value = 1;\n\texport let active = true;\n\tlet old_value: number;\n\tlet old_active: boolean;\n\tlet interval: NodeJS.Timeout;\n\n\t$: if (old_value !== value || active !== old_active) {\n\t\tif (interval) clearInterval(interval);\n\t\tif (active) {\n\t\t\tinterval = setInterval(() => {\n\t\t\t\tif (document.visibilityState === \"visible\") gradio.dispatch(\"tick\");\n\t\t\t}, value * 1000);\n\t\t}\n\t\told_value = value;\n\t\told_active = active;\n\t}\n\n\tonDestroy(() => {\n\t\tif (interval) clearInterval(interval);\n\t});\n</script>\n"], "names": ["onDestroy", "gradio", "$$props", "value", "active", "old_value", "old_active", "interval", "$$invalidate"], "mappings": "2FAEU,CAAA,UAAAA,CAAA,SAAyB,iDACvB,OAAAC,CAET,EAAAC,EACS,CAAA,MAAAC,EAAQ,CAAC,EAAAD,EACT,CAAA,OAAAE,EAAS,EAAI,EAAAF,EACpBG,EACAC,EACAC,EAaJ,OAAAP,EAAS,IAAA,CACJO,GAAU,cAAcA,CAAQ,0IAZ9BF,IAAcF,GAASC,IAAWE,KACpCC,GAAU,cAAcA,CAAQ,EAChCH,GACHI,EAAA,EAAAD,EAAW,iBACN,SAAS,kBAAoB,WAAWN,EAAO,SAAS,MAAM,GAChEE,EAAQ,MAEZK,EAAA,EAAAH,EAAYF,CAAK,EACjBK,EAAA,EAAAF,EAAaF,CAAM"}
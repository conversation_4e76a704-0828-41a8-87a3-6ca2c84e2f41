{"version": 3, "file": "Example-C7XUkkid.js", "sources": ["../../../../js/textbox/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\n\texport let value: string | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\n\tlet size: number;\n\tlet el: HTMLDivElement;\n\n\tfunction set_styles(element: HTMLElement, el_width: number): void {\n\t\tif (!element || !el_width) return;\n\t\tel.style.setProperty(\n\t\t\t\"--local-text-width\",\n\t\t\t`${el_width < 150 ? el_width : 200}px`\n\t\t);\n\t\tel.style.whiteSpace = \"unset\";\n\t}\n\n\tonMount(() => {\n\t\tset_styles(el, size);\n\t});\n</script>\n\n<div\n\tbind:clientWidth={size}\n\tbind:this={el}\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value ? value : \"\"}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n\n\tdiv {\n\t\toverflow: hidden;\n\t\tmin-width: var(--local-text-width);\n\n\t\twhite-space: nowrap;\n\t}\n</style>\n"], "names": ["onMount", "ctx", "toggle_class", "div", "insert", "target", "anchor", "set_data", "t", "t_value", "value", "$$props", "type", "selected", "size", "el", "set_styles", "element", "el_width", "$$invalidate", "$$value"], "mappings": "kTACU,CAAA,QAAAA,CAAA,SAAuB,kDA8B/BC,EAAK,CAAA,EAAGA,EAAK,CAAA,EAAG,IAAE,sFAJNC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAJlCG,EAQKC,EAAAF,EAAAG,CAAA,4DADHL,EAAK,CAAA,EAAGA,EAAK,CAAA,EAAG,IAAE,KAAAM,EAAAC,EAAAC,CAAA,OAJNP,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,yFAzBtB,MAAAS,CAAoB,EAAAC,GACpB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,EAEvBG,EACAC,WAEKC,EAAWC,EAAsBC,EAAgB,CACpD,CAAAD,IAAYC,IACjBH,EAAG,MAAM,YACR,qBACG,GAAAG,EAAW,IAAMA,EAAW,GAAG,IAAA,EAEnCC,EAAA,EAAAJ,EAAG,MAAM,WAAa,QAAOA,CAAA,GAG9Bf,EAAO,IAAA,CACNgB,EAAWD,EAAID,CAAI,iBAKFA,EAAI,KAAA,4DACXC,EAAEK"}
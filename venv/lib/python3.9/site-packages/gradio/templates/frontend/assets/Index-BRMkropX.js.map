{"version": 3, "file": "Index-BRMkropX.js", "sources": ["../../../../js/nativeplot/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { onMount } from \"svelte\";\n\n\timport type { TopLevelSpec as Spec } from \"vega-lite\";\n\timport vegaEmbed from \"vega-embed\";\n\timport type { View } from \"vega\";\n\timport { LineChart as LabelIcon } from \"@gradio/icons\";\n\timport { Empty } from \"@gradio/atoms\";\n\n\tinterface PlotData {\n\t\tcolumns: string[];\n\t\tdata: [string | number][];\n\t\tdatatypes: Record<string, \"quantitative\" | \"temporal\" | \"nominal\">;\n\t\tmark: \"line\" | \"point\" | \"bar\";\n\t}\n\texport let value: PlotData | null;\n\texport let x: string;\n\texport let y: string;\n\texport let color: string | null = null;\n\t$: unique_colors =\n\t\tcolor && value && value.datatypes[color] === \"nominal\"\n\t\t\t? Array.from(new Set(_data.map((d) => d[color])))\n\t\t\t: [];\n\n\texport let title: string | null = null;\n\texport let x_title: string | null = null;\n\texport let y_title: string | null = null;\n\texport let color_title: string | null = null;\n\texport let x_bin: string | number | null = null;\n\texport let y_aggregate:\n\t\t| \"sum\"\n\t\t| \"mean\"\n\t\t| \"median\"\n\t\t| \"min\"\n\t\t| \"max\"\n\t\t| undefined = undefined;\n\texport let color_map: Record<string, string> | null = null;\n\texport let x_lim: [number, number] | null = null;\n\texport let y_lim: [number, number] | null = null;\n\texport let x_label_angle: number | null = null;\n\texport let y_label_angle: number | null = null;\n\texport let caption: string | null = null;\n\texport let sort: \"x\" | \"y\" | \"-x\" | \"-y\" | string[] | null = null;\n\tfunction reformat_sort(\n\t\t_sort: typeof sort\n\t):\n\t\t| string\n\t\t| \"ascending\"\n\t\t| \"descending\"\n\t\t| { field: string; order: \"ascending\" | \"descending\" }\n\t\t| string[]\n\t\t| undefined {\n\t\tif (_sort === \"x\") {\n\t\t\treturn \"ascending\";\n\t\t} else if (_sort === \"-x\") {\n\t\t\treturn \"descending\";\n\t\t} else if (_sort === \"y\") {\n\t\t\treturn { field: y, order: \"ascending\" };\n\t\t} else if (_sort === \"-y\") {\n\t\t\treturn { field: y, order: \"descending\" };\n\t\t} else if (_sort === null) {\n\t\t\treturn undefined;\n\t\t} else if (Array.isArray(_sort)) {\n\t\t\treturn _sort;\n\t\t}\n\t}\n\t$: _sort = reformat_sort(sort);\n\texport let _selectable = false;\n\texport let target: HTMLDivElement;\n\tlet _data: {\n\t\t[x: string]: string | number;\n\t}[];\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t\tdouble_click: undefined;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\n\t$: x_temporal = value && value.datatypes[x] === \"temporal\";\n\t$: _x_lim = x_lim && x_temporal ? [x_lim[0] * 1000, x_lim[1] * 1000] : x_lim;\n\tlet _x_bin: number | undefined;\n\tlet mouse_down_on_chart = false;\n\tconst SUFFIX_DURATION: Record<string, number> = {\n\t\ts: 1,\n\t\tm: 60,\n\t\th: 60 * 60,\n\t\td: 24 * 60 * 60\n\t};\n\t$: _x_bin = x_bin\n\t\t? typeof x_bin === \"string\"\n\t\t\t? 1000 *\n\t\t\t\tparseInt(x_bin.substring(0, x_bin.length - 1)) *\n\t\t\t\tSUFFIX_DURATION[x_bin[x_bin.length - 1]]\n\t\t\t: x_bin\n\t\t: undefined;\n\tlet _y_aggregate: typeof y_aggregate;\n\tlet aggregating: boolean;\n\t$: {\n\t\tif (value) {\n\t\t\tif (value.mark === \"point\") {\n\t\t\t\taggregating = _x_bin !== undefined;\n\t\t\t\t_y_aggregate = y_aggregate || aggregating ? \"sum\" : undefined;\n\t\t\t} else {\n\t\t\t\taggregating = _x_bin !== undefined || value.datatypes[x] === \"nominal\";\n\t\t\t\t_y_aggregate = y_aggregate ? y_aggregate : \"sum\";\n\t\t\t}\n\t\t}\n\t}\n\tfunction reformat_data(data: PlotData): {\n\t\t[x: string]: string | number;\n\t}[] {\n\t\tlet x_index = data.columns.indexOf(x);\n\t\tlet y_index = data.columns.indexOf(y);\n\t\tlet color_index = color ? data.columns.indexOf(color) : null;\n\t\treturn data.data.map((row) => {\n\t\t\tconst obj = {\n\t\t\t\t[x]: row[x_index],\n\t\t\t\t[y]: row[y_index]\n\t\t\t};\n\t\t\tif (color && color_index !== null) {\n\t\t\t\tobj[color] = row[color_index];\n\t\t\t}\n\t\t\treturn obj;\n\t\t});\n\t}\n\t$: _data = value ? reformat_data(value) : [];\n\n\tconst is_browser = typeof window !== \"undefined\";\n\tlet chart_element: HTMLDivElement;\n\t$: computed_style =\n\t\ttarget && is_browser ? window.getComputedStyle(target) : null;\n\tlet view: View;\n\tlet mounted = false;\n\tlet old_width: number;\n\tlet resizeObserver: ResizeObserver;\n\n\tfunction load_chart(): void {\n\t\tif (view) {\n\t\t\tview.finalize();\n\t\t}\n\t\tif (!value || !chart_element) return;\n\t\told_width = chart_element.offsetWidth;\n\t\tconst spec = create_vega_lite_spec();\n\t\tif (!spec) return;\n\t\tresizeObserver = new ResizeObserver((el) => {\n\t\t\tif (!el[0].target || !(el[0].target instanceof HTMLElement)) return;\n\t\t\tif (\n\t\t\t\told_width === 0 &&\n\t\t\t\tchart_element.offsetWidth !== 0 &&\n\t\t\t\tvalue.datatypes[x] === \"nominal\"\n\t\t\t) {\n\t\t\t\t// a bug where when a nominal chart is first loaded, the width is 0, it doesn't resize\n\t\t\t\tload_chart();\n\t\t\t} else {\n\t\t\t\tview.signal(\"width\", el[0].target.offsetWidth).run();\n\t\t\t}\n\t\t});\n\t\tvegaEmbed(chart_element, spec, { actions: false }).then(function (result) {\n\t\t\tview = result.view;\n\n\t\t\tresizeObserver.observe(chart_element);\n\t\t\tvar debounceTimeout: NodeJS.Timeout;\n\t\t\tview.addEventListener(\"dblclick\", () => {\n\t\t\t\tgradio.dispatch(\"double_click\");\n\t\t\t});\n\t\t\t// prevent double-clicks from highlighting text\n\t\t\tchart_element.addEventListener(\n\t\t\t\t\"mousedown\",\n\t\t\t\tfunction (e) {\n\t\t\t\t\tif (e.detail > 1) {\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfalse\n\t\t\t);\n\t\t\tif (_selectable) {\n\t\t\t\tview.addSignalListener(\"brush\", function (_, value) {\n\t\t\t\t\tif (Object.keys(value).length === 0) return;\n\t\t\t\t\tclearTimeout(debounceTimeout);\n\t\t\t\t\tlet range: [number, number] = value[Object.keys(value)[0]];\n\t\t\t\t\tif (x_temporal) {\n\t\t\t\t\t\trange = [range[0] / 1000, range[1] / 1000];\n\t\t\t\t\t}\n\t\t\t\t\tlet callback = (): void => {\n\t\t\t\t\t\tgradio.dispatch(\"select\", {\n\t\t\t\t\t\t\tvalue: range,\n\t\t\t\t\t\t\tindex: range,\n\t\t\t\t\t\t\tselected: true\n\t\t\t\t\t\t});\n\t\t\t\t\t};\n\t\t\t\t\tif (mouse_down_on_chart) {\n\t\t\t\t\t\trelease_callback = callback;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdebounceTimeout = setTimeout(function () {\n\t\t\t\t\t\t\tgradio.dispatch(\"select\", {\n\t\t\t\t\t\t\t\tvalue: range,\n\t\t\t\t\t\t\t\tindex: range,\n\t\t\t\t\t\t\t\tselected: true\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}, 250);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t}\n\n\tlet release_callback: (() => void) | null = null;\n\tonMount(() => {\n\t\tmounted = true;\n\t\tchart_element.addEventListener(\"mousedown\", () => {\n\t\t\tmouse_down_on_chart = true;\n\t\t});\n\t\tchart_element.addEventListener(\"mouseup\", () => {\n\t\t\tmouse_down_on_chart = false;\n\t\t\tif (release_callback) {\n\t\t\t\trelease_callback();\n\t\t\t\trelease_callback = null;\n\t\t\t}\n\t\t});\n\n\t\treturn () => {\n\t\t\tmounted = false;\n\t\t\tif (view) {\n\t\t\t\tview.finalize();\n\t\t\t}\n\t\t\tif (resizeObserver) {\n\t\t\t\tresizeObserver.disconnect();\n\t\t\t}\n\t\t};\n\t});\n\n\t$: title,\n\t\tx_title,\n\t\ty_title,\n\t\tcolor_title,\n\t\tx,\n\t\ty,\n\t\tcolor,\n\t\tx_bin,\n\t\t_y_aggregate,\n\t\tcolor_map,\n\t\tx_lim,\n\t\ty_lim,\n\t\tcaption,\n\t\tsort,\n\t\tvalue,\n\t\tmounted,\n\t\tchart_element,\n\t\tcomputed_style && requestAnimationFrame(load_chart);\n\n\tfunction create_vega_lite_spec(): Spec | null {\n\t\tif (!value || !computed_style) return null;\n\t\tlet accent_color = computed_style.getPropertyValue(\"--color-accent\");\n\t\tlet body_text_color = computed_style.getPropertyValue(\"--body-text-color\");\n\t\tlet borderColorPrimary = computed_style.getPropertyValue(\n\t\t\t\"--border-color-primary\"\n\t\t);\n\t\tlet font_family = computed_style.fontFamily;\n\t\tlet title_weight = computed_style.getPropertyValue(\n\t\t\t\"--block-title-text-weight\"\n\t\t) as\n\t\t\t| \"bold\"\n\t\t\t| \"normal\"\n\t\t\t| 100\n\t\t\t| 200\n\t\t\t| 300\n\t\t\t| 400\n\t\t\t| 500\n\t\t\t| 600\n\t\t\t| 700\n\t\t\t| 800\n\t\t\t| 900;\n\t\tconst font_to_px_val = (font: string): number => {\n\t\t\treturn font.endsWith(\"px\") ? parseFloat(font.slice(0, -2)) : 12;\n\t\t};\n\t\tlet text_size_md = font_to_px_val(\n\t\t\tcomputed_style.getPropertyValue(\"--text-md\")\n\t\t);\n\t\tlet text_size_sm = font_to_px_val(\n\t\t\tcomputed_style.getPropertyValue(\"--text-sm\")\n\t\t);\n\n\t\t/* eslint-disable complexity */\n\t\treturn {\n\t\t\t$schema: \"https://vega.github.io/schema/vega-lite/v5.17.0.json\",\n\t\t\tbackground: \"transparent\",\n\t\t\tconfig: {\n\t\t\t\tautosize: { type: \"fit\", contains: \"padding\" },\n\t\t\t\taxis: {\n\t\t\t\t\tlabelFont: font_family,\n\t\t\t\t\tlabelColor: body_text_color,\n\t\t\t\t\ttitleFont: font_family,\n\t\t\t\t\ttitleColor: body_text_color,\n\t\t\t\t\ttitlePadding: 8,\n\t\t\t\t\ttickColor: borderColorPrimary,\n\t\t\t\t\tlabelFontSize: text_size_sm,\n\t\t\t\t\tgridColor: borderColorPrimary,\n\t\t\t\t\ttitleFontWeight: \"normal\",\n\t\t\t\t\ttitleFontSize: text_size_sm,\n\t\t\t\t\tlabelFontWeight: \"normal\",\n\t\t\t\t\tdomain: false,\n\t\t\t\t\tlabelAngle: 0\n\t\t\t\t},\n\t\t\t\tlegend: {\n\t\t\t\t\tlabelColor: body_text_color,\n\t\t\t\t\tlabelFont: font_family,\n\t\t\t\t\ttitleColor: body_text_color,\n\t\t\t\t\ttitleFont: font_family,\n\t\t\t\t\ttitleFontWeight: \"normal\",\n\t\t\t\t\ttitleFontSize: text_size_sm,\n\t\t\t\t\tlabelFontWeight: \"normal\",\n\t\t\t\t\toffset: 2\n\t\t\t\t},\n\t\t\t\ttitle: {\n\t\t\t\t\tcolor: body_text_color,\n\t\t\t\t\tfont: font_family,\n\t\t\t\t\tfontSize: text_size_md,\n\t\t\t\t\tfontWeight: title_weight,\n\t\t\t\t\tanchor: \"middle\"\n\t\t\t\t},\n\t\t\t\tview: { stroke: borderColorPrimary },\n\t\t\t\tmark: {\n\t\t\t\t\tstroke: value.mark !== \"bar\" ? accent_color : undefined,\n\t\t\t\t\tfill: value.mark === \"bar\" ? accent_color : undefined,\n\t\t\t\t\tcursor: \"crosshair\"\n\t\t\t\t}\n\t\t\t},\n\t\t\tdata: { name: \"data\" },\n\t\t\tdatasets: {\n\t\t\t\tdata: _data\n\t\t\t},\n\t\t\tlayer: [\"plot\", ...(value.mark === \"line\" ? [\"hover\"] : [])].map(\n\t\t\t\t(mode) => {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tencoding: {\n\t\t\t\t\t\t\tsize:\n\t\t\t\t\t\t\t\tvalue.mark === \"line\"\n\t\t\t\t\t\t\t\t\t? mode == \"plot\"\n\t\t\t\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t\t\t\tcondition: {\n\t\t\t\t\t\t\t\t\t\t\t\t\tempty: false,\n\t\t\t\t\t\t\t\t\t\t\t\t\tparam: \"hoverPlot\",\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue: 3\n\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\tvalue: 2\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t\t\t\t\t\tcondition: { empty: false, param: \"hover\", value: 100 },\n\t\t\t\t\t\t\t\t\t\t\t\tvalue: 0\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t: undefined,\n\t\t\t\t\t\t\topacity:\n\t\t\t\t\t\t\t\tmode === \"plot\"\n\t\t\t\t\t\t\t\t\t? undefined\n\t\t\t\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t\t\t\t\tcondition: { empty: false, param: \"hover\", value: 1 },\n\t\t\t\t\t\t\t\t\t\t\tvalue: 0\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tx: {\n\t\t\t\t\t\t\t\taxis: x_label_angle ? { labelAngle: x_label_angle } : {},\n\t\t\t\t\t\t\t\tfield: x,\n\t\t\t\t\t\t\t\ttitle: x_title || x,\n\t\t\t\t\t\t\t\ttype: value.datatypes[x],\n\t\t\t\t\t\t\t\tscale: _x_lim ? { domain: _x_lim } : undefined,\n\t\t\t\t\t\t\t\tbin: _x_bin ? { step: _x_bin } : undefined,\n\t\t\t\t\t\t\t\tsort: _sort\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\ty: {\n\t\t\t\t\t\t\t\taxis: y_label_angle ? { labelAngle: y_label_angle } : {},\n\t\t\t\t\t\t\t\tfield: y,\n\t\t\t\t\t\t\t\ttitle: y_title || y,\n\t\t\t\t\t\t\t\ttype: value.datatypes[y],\n\t\t\t\t\t\t\t\tscale: y_lim ? { domain: y_lim } : undefined,\n\t\t\t\t\t\t\t\taggregate: aggregating ? _y_aggregate : undefined\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tcolor: color\n\t\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t\tfield: color,\n\t\t\t\t\t\t\t\t\t\tlegend: { orient: \"bottom\", title: color_title },\n\t\t\t\t\t\t\t\t\t\tscale:\n\t\t\t\t\t\t\t\t\t\t\tvalue.datatypes[color] === \"nominal\"\n\t\t\t\t\t\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdomain: unique_colors,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trange: color_map\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? unique_colors.map((c) => color_map[c])\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: undefined\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trange: [\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t100, 200, 300, 400, 500, 600, 700, 800, 900\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t].map((n) =>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcomputed_style.getPropertyValue(\"--primary-\" + n)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tinterpolate: \"hsl\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\ttype: value.datatypes[color]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t: undefined,\n\t\t\t\t\t\t\ttooltip: [\n\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\tfield: y,\n\t\t\t\t\t\t\t\t\ttype: value.datatypes[y],\n\t\t\t\t\t\t\t\t\taggregate: aggregating ? _y_aggregate : undefined,\n\t\t\t\t\t\t\t\t\ttitle: y_title || y\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\tfield: x,\n\t\t\t\t\t\t\t\t\ttype: value.datatypes[x],\n\t\t\t\t\t\t\t\t\ttitle: x_title || x,\n\t\t\t\t\t\t\t\t\tformat: x_temporal ? \"%Y-%m-%d %H:%M:%S\" : undefined,\n\t\t\t\t\t\t\t\t\tbin: _x_bin ? { step: _x_bin } : undefined\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t...(color\n\t\t\t\t\t\t\t\t\t? [\n\t\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\tfield: color,\n\t\t\t\t\t\t\t\t\t\t\t\ttype: value.datatypes[color]\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t]\n\t\t\t\t\t\t\t\t\t: [])\n\t\t\t\t\t\t\t]\n\t\t\t\t\t\t},\n\t\t\t\t\t\tstrokeDash: {},\n\t\t\t\t\t\tmark: { clip: true, type: mode === \"hover\" ? \"point\" : value.mark },\n\t\t\t\t\t\tname: mode\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t),\n\t\t\t// @ts-ignore\n\t\t\tparams: [\n\t\t\t\t...(value.mark === \"line\"\n\t\t\t\t\t? [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: \"hoverPlot\",\n\t\t\t\t\t\t\t\tselect: {\n\t\t\t\t\t\t\t\t\tclear: \"mouseout\",\n\t\t\t\t\t\t\t\t\tfields: color ? [color] : [],\n\t\t\t\t\t\t\t\t\tnearest: true,\n\t\t\t\t\t\t\t\t\ton: \"mouseover\",\n\t\t\t\t\t\t\t\t\ttype: \"point\" as \"point\"\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tviews: [\"hover\"]\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: \"hover\",\n\t\t\t\t\t\t\t\tselect: {\n\t\t\t\t\t\t\t\t\tclear: \"mouseout\",\n\t\t\t\t\t\t\t\t\tnearest: true,\n\t\t\t\t\t\t\t\t\ton: \"mouseover\",\n\t\t\t\t\t\t\t\t\ttype: \"point\" as \"point\"\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tviews: [\"hover\"]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t: []),\n\t\t\t\t...(_selectable\n\t\t\t\t\t? [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: \"brush\",\n\t\t\t\t\t\t\t\tselect: {\n\t\t\t\t\t\t\t\t\tencodings: [\"x\"],\n\t\t\t\t\t\t\t\t\tmark: { fill: \"gray\", fillOpacity: 0.3, stroke: \"none\" },\n\t\t\t\t\t\t\t\t\ttype: \"interval\" as \"interval\"\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tviews: [\"plot\"]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t: [])\n\t\t\t],\n\t\t\twidth: chart_element.offsetWidth,\n\t\t\ttitle: title || undefined\n\t\t};\n\t\t/* eslint-enable complexity */\n\t}\n\n\texport let label = \"Textbox\";\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let show_label: boolean;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let height: number | undefined = undefined;\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\tpadding={true}\n\t{height}\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\t<BlockTitle {show_label} info={undefined}>{label}</BlockTitle>\n\t{#if value && is_browser}\n\t\t<div bind:this={chart_element}></div>\n\n\t\t{#if caption}\n\t\t\t<p class=\"caption\">{caption}</p>\n\t\t{/if}\n\t{:else}\n\t\t<Empty unpadded_box={true}><LabelIcon /></Empty>\n\t{/if}\n</Block>\n\n<style>\n\tdiv {\n\t\twidth: 100%;\n\t}\n\t:global(#vg-tooltip-element) {\n\t\tfont-family: var(--font) !important;\n\t\tfont-size: var(--text-xs) !important;\n\t\tbox-shadow: none !important;\n\t\tbackground-color: var(--block-background-fill) !important;\n\t\tborder: 1px solid var(--border-color-primary) !important;\n\t\tcolor: var(--body-text-color) !important;\n\t}\n\t:global(#vg-tooltip-element .key) {\n\t\tcolor: var(--body-text-color-subdued) !important;\n\t}\n\t.caption {\n\t\tpadding: 0 4px;\n\t\tmargin: 0;\n\t\ttext-align: center;\n\t}\n</style>\n"], "names": ["onMount", "ctx", "dirty", "create_if_block_1", "insert", "target", "div", "anchor", "p", "create_if_block_2", "value", "$$props", "x", "y", "color", "title", "x_title", "y_title", "color_title", "x_bin", "y_aggregate", "color_map", "x_lim", "y_lim", "x_label_angle", "y_label_angle", "caption", "sort", "reformat_sort", "_sort", "_selectable", "_data", "gradio", "_x_bin", "mouse_down_on_chart", "SUFFIX_DURATION", "_y_aggregate", "aggregating", "reformat_data", "data", "x_index", "y_index", "color_index", "row", "obj", "is_browser", "chart_element", "view", "mounted", "old_width", "resizeObserver", "load_chart", "spec", "create_vega_lite_spec", "el", "vegaEmbed", "result", "debounceTimeout", "e", "_", "range", "x_temporal", "release_callback", "$$invalidate", "computed_style", "accent_color", "body_text_color", "borderColorPrimary", "font_family", "title_weight", "font_to_px_val", "font", "text_size_md", "text_size_sm", "mode", "_x_lim", "unique_colors", "c", "n", "label", "elem_id", "elem_classes", "visible", "show_label", "scale", "min_width", "loading_status", "height", "clear_status_handler", "$$value", "d"], "mappings": "svBAMU,CAAA,QAAAA,WAAuB,2DAiflB,CAAA,WAAAC,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,wLAFNC,EAAA,CAAA,EAAA,GAAA,CAAA,WAAAD,KAAO,UAAU,EACvBC,EAAA,CAAA,EAAA,GAAA,CAAA,KAAAD,KAAO,IAAI,gBACbA,EAAc,EAAA,CAAA,sIAIuBA,EAAK,CAAA,CAAA,wCAALA,EAAK,CAAA,CAAA,8EAQ1B,2QAJhBA,EAAO,CAAA,GAAAE,GAAAF,CAAA,uFAFZG,EAAoCC,EAAAC,EAAAC,CAAA,iDAE/BN,EAAO,CAAA,wVACSA,EAAO,CAAA,CAAA,iDAA3BG,EAA+BC,EAAAG,EAAAD,CAAA,+BAAXN,EAAO,CAAA,CAAA,qDAbxBA,EAAc,EAAA,GAAAQ,GAAAR,CAAA,wCAQY,sFAC1B,OAAAA,MAASA,EAAU,EAAA,EAAA,4KATnBA,EAAc,EAAA,knBAJH,WACP,8cA9dE,MAAAS,CAAsB,EAAAC,GACtB,EAAAC,CAAS,EAAAD,GACT,EAAAE,CAAS,EAAAF,EACT,CAAA,MAAAG,EAAuB,IAAI,EAAAH,EAM3B,CAAA,MAAAI,EAAuB,IAAI,EAAAJ,EAC3B,CAAA,QAAAK,EAAyB,IAAI,EAAAL,EAC7B,CAAA,QAAAM,EAAyB,IAAI,EAAAN,EAC7B,CAAA,YAAAO,EAA6B,IAAI,EAAAP,EACjC,CAAA,MAAAQ,EAAgC,IAAI,EAAAR,EACpC,CAAA,YAAAS,EAMI,MAAS,EAAAT,EACb,CAAA,UAAAU,EAA2C,IAAI,EAAAV,EAC/C,CAAA,MAAAW,EAAiC,IAAI,EAAAX,EACrC,CAAA,MAAAY,EAAiC,IAAI,EAAAZ,EACrC,CAAA,cAAAa,EAA+B,IAAI,EAAAb,EACnC,CAAA,cAAAc,EAA+B,IAAI,EAAAd,EACnC,CAAA,QAAAe,GAAyB,IAAI,EAAAf,EAC7B,CAAA,KAAAgB,EAAkD,IAAI,EAAAhB,EACxD,SAAAiB,GACRC,EAAkB,CAQd,GAAAA,IAAU,UACN,YACG,GAAAA,IAAU,WACb,aACG,GAAAA,IAAU,IACX,MAAA,CAAA,MAAOhB,EAAG,MAAO,WAAW,EAC3B,GAAAgB,IAAU,KACX,MAAA,CAAA,MAAOhB,EAAG,MAAO,YAAY,EAC5B,GAAAgB,IAAU,eAEV,MAAM,QAAQA,CAAK,SACtBA,EAIE,GAAA,CAAA,YAAAC,EAAc,EAAK,EAAAnB,GACnB,OAAAN,CAAsB,EAAAM,EAC7BoB,GAGO,OAAAC,CAIT,EAAArB,EAIEsB,EACAC,EAAsB,SACpBC,GAAe,CACpB,EAAG,EACH,EAAG,GACH,EAAG,GAAK,GACR,EAAG,GAAK,GAAK,EAAA,MASVC,EACAC,EAYK,SAAAC,GAAcC,EAAc,CAGhC,IAAAC,EAAUD,EAAK,QAAQ,QAAQ3B,CAAC,EAChC6B,EAAUF,EAAK,QAAQ,QAAQ1B,CAAC,EAChC6B,EAAc5B,EAAQyB,EAAK,QAAQ,QAAQzB,CAAK,EAAI,KACjD,OAAAyB,EAAK,KAAK,IAAKI,GAAG,OAClBC,EAAG,CAAA,CACPhC,CAAC,EAAG+B,EAAIH,CAAO,EAAA,CACf3B,CAAC,EAAG8B,EAAIF,CAAO,UAEb3B,GAAS4B,IAAgB,OAC5BE,EAAI9B,CAAK,EAAI6B,EAAID,CAAW,GAEtBE,UAKHC,GAAU,OAAU,OAAW,QACjCC,EAGAC,EACAC,EAAU,GACVC,GACAC,WAEKC,IAAU,CAIb,GAHDJ,GACHA,EAAK,SAAQ,EAET,CAAArC,IAAUoC,EAAa,OAC5BG,GAAYH,EAAc,YACpB,MAAAM,EAAOC,KACRD,IACLF,EAAc,IAAO,eAAgBI,GAAE,EACjCA,EAAG,CAAC,EAAE,QAAM,EAAMA,EAAG,CAAC,EAAE,kBAAkB,eAE9CL,KAAc,GACdH,EAAc,cAAgB,GAC9BpC,EAAM,UAAUE,CAAC,IAAM,UAGvBuC,KAEAJ,EAAK,OAAO,QAASO,EAAG,CAAC,EAAE,OAAO,WAAW,EAAE,SAGjDC,GAAUT,EAAeM,EAAI,CAAI,QAAS,GAAK,EAAI,KAAI,SAAWI,EAAM,CACvET,EAAOS,EAAO,KAEdN,EAAe,QAAQJ,CAAa,MAChCW,EACJV,EAAK,iBAAiB,WAAU,IAAA,CAC/Bf,EAAO,SAAS,cAAc,IAG/Bc,EAAc,iBACb,qBACUY,EAAC,CACNA,EAAE,OAAS,GACdA,EAAE,eAAc,GAGlB,IAEG5B,GACHiB,EAAK,kBAAkB,QAAmB,SAAAY,EAAGjD,EAAK,CAC7C,GAAA,OAAO,KAAKA,CAAK,EAAE,SAAW,EAAC,OACnC,aAAa+C,CAAe,MACxBG,EAA0BlD,EAAM,OAAO,KAAKA,CAAK,EAAE,CAAC,CAAA,EACpDmD,IACHD,EAAK,CAAIA,EAAM,CAAC,EAAI,IAAMA,EAAM,CAAC,EAAI,GAAI,GAStC1B,EACH4B,EARW,IAAA,CACX9B,EAAO,SAAS,SAAQ,CACvB,MAAO4B,EACP,MAAOA,EACP,SAAU,MAMXH,EAAkB,sBACjBzB,EAAO,SAAS,SAAQ,CACvB,MAAO4B,EACP,MAAOA,EACP,SAAU,MAET,UAOJ,IAAAE,EAAwC,KAC5C9D,GAAO,KACN+D,EAAA,GAAAf,EAAU,EAAI,EACdF,EAAc,iBAAiB,YAAW,IAAA,CACzCZ,EAAsB,KAEvBY,EAAc,iBAAiB,UAAS,IAAA,CACvCZ,EAAsB,GAClB4B,IACHA,IACAA,EAAmB,aAKpBC,EAAA,GAAAf,EAAU,EAAK,EACXD,GACHA,EAAK,SAAQ,EAEVG,GACHA,EAAe,WAAU,cAwBnBG,IAAqB,KACxB3C,GAAK,CAAKsD,EAAc,OAAS,KAClC,IAAAC,EAAeD,EAAe,iBAAiB,gBAAgB,EAC/DE,EAAkBF,EAAe,iBAAiB,mBAAmB,EACrEG,EAAqBH,EAAe,iBACvC,wBAAwB,EAErBI,EAAcJ,EAAe,WAC7BK,EAAeL,EAAe,iBACjC,2BAA2B,EAatB,MAAAM,EAAkBC,GAChBA,EAAK,SAAS,IAAI,EAAI,WAAWA,EAAK,MAAM,EAAI,EAAC,GAAK,GAE1D,IAAAC,GAAeF,EAClBN,EAAe,iBAAiB,WAAW,CAAA,EAExCS,EAAeH,EAClBN,EAAe,iBAAiB,WAAW,CAAA,SAK3C,QAAS,uDACT,WAAY,cACZ,OAAM,CACL,UAAY,KAAM,MAAO,SAAU,SAAS,EAC5C,KAAI,CACH,UAAWI,EACX,WAAYF,EACZ,UAAWE,EACX,WAAYF,EACZ,aAAc,EACd,UAAWC,EACX,cAAeM,EACf,UAAWN,EACX,gBAAiB,SACjB,cAAeM,EACf,gBAAiB,SACjB,OAAQ,GACR,WAAY,GAEb,OAAM,CACL,WAAYP,EACZ,UAAWE,EACX,WAAYF,EACZ,UAAWE,EACX,gBAAiB,SACjB,cAAeK,EACf,gBAAiB,SACjB,OAAQ,GAET,MAAK,CACJ,MAAOP,EACP,KAAME,EACN,SAAUI,GACV,WAAYH,EACZ,OAAQ,UAET,KAAI,CAAI,OAAQF,CAAkB,EAClC,KAAI,CACH,OAAQzD,EAAM,OAAS,MAAQuD,EAAe,OAC9C,KAAMvD,EAAM,OAAS,MAAQuD,EAAe,OAC5C,OAAQ,cAGV,KAAI,CAAI,KAAM,MAAM,EACpB,SAAQ,CACP,KAAMlC,CAAA,EAEP,MAAQ,CAAA,OAAY,GAAArB,EAAM,OAAS,OAAU,CAAA,OAAO,EAAS,CAAA,CAAA,EAAA,IAC3DgE,KAEC,SAAQ,CACP,KACChE,EAAM,OAAS,OACZgE,GAAQ,QAEP,UAAS,CACR,MAAO,GACP,MAAO,YACP,MAAO,GAER,MAAO,IAGP,UAAS,CAAI,MAAO,GAAO,MAAO,QAAS,MAAO,GAAG,EACrD,MAAO,GAER,OACJ,QACCA,IAAS,OACN,QAEA,UAAS,CAAI,MAAO,GAAO,MAAO,QAAS,MAAO,CAAC,EACnD,MAAO,GAEX,EAAC,CACA,KAAMlD,EAAkB,CAAA,WAAYA,CAAa,EAAA,CAAA,EACjD,MAAOZ,EACP,MAAOI,GAAWJ,EAClB,KAAMF,EAAM,UAAUE,CAAC,EACvB,MAAO+D,EAAM,CAAK,OAAQA,GAAW,OACrC,IAAK1C,EAAM,CAAK,KAAMA,GAAW,OACjC,KAAMJ,GAEP,EAAC,CACA,KAAMJ,EAAkB,CAAA,WAAYA,CAAa,EAAA,CAAA,EACjD,MAAOZ,EACP,MAAOI,GAAWJ,EAClB,KAAMH,EAAM,UAAUG,CAAC,EACvB,MAAOU,EAAK,CAAK,OAAQA,GAAU,OACnC,UAAWc,EAAcD,EAAe,QAEzC,MAAOtB,GAEJ,MAAOA,EACP,QAAU,OAAQ,SAAU,MAAOI,CAAW,EAC9C,MACCR,EAAM,UAAUI,CAAK,IAAM,WAExB,OAAQ8D,EACR,MAAOvD,EACJuD,EAAc,IAAKC,IAAMxD,EAAUwD,EAAC,CAAA,EACpC,SAGH,MACC,CAAA,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAA,EACvC,IAAKC,IACNd,EAAe,iBAAiB,aAAec,EAAC,CAAA,EAEjD,YAAa,OAEjB,KAAMpE,EAAM,UAAUI,CAAK,GAE3B,OACH,QAAO,EAEL,MAAOD,EACP,KAAMH,EAAM,UAAUG,CAAC,EACvB,UAAWwB,EAAcD,EAAe,OACxC,MAAOnB,GAAWJ,IAGlB,MAAOD,EACP,KAAMF,EAAM,UAAUE,CAAC,EACvB,MAAOI,GAAWJ,EAClB,OAAQiD,EAAa,oBAAsB,OAC3C,IAAK5B,EAAM,CAAK,KAAMA,CAAW,EAAA,QAE9B,GAAAnB,IAGA,MAAOA,EACP,KAAMJ,EAAM,UAAUI,CAAK,SAMjC,WAAU,CAAA,EACV,KAAI,CAAI,KAAM,GAAM,KAAM4D,IAAS,QAAU,QAAUhE,EAAM,MAC7D,KAAMgE,KAKT,OAAM,IACDhE,EAAM,OAAS,SAGf,KAAM,YACN,OAAM,CACL,MAAO,WACP,OAAQI,EAAK,CAAIA,CAAK,EAAA,CAAA,EACtB,QAAS,GACT,GAAI,YACJ,KAAM,SAEP,OAAQ,OAAO,IAGf,KAAM,QACN,OAAM,CACL,MAAO,WACP,QAAS,GACT,GAAI,YACJ,KAAM,SAEP,OAAQ,OAAO,OAIf,GAAAgB,IAGA,KAAM,QACN,OAAM,CACL,WAAY,GAAG,EACf,KAAI,CAAI,KAAM,OAAQ,YAAa,GAAK,OAAQ,QAChD,KAAM,YAEP,OAAQ,MAAM,QAKnB,MAAOgB,EAAc,YACrB,MAAO/B,GAAS,QAKP,GAAA,CAAA,MAAAgE,GAAQ,SAAS,EAAApE,EACjB,CAAA,QAAAqE,GAAU,EAAE,EAAArE,GACZ,aAAAsE,GAAY,EAAA,EAAAtE,EACZ,CAAA,QAAAuE,GAAU,EAAI,EAAAvE,GACd,WAAAwE,EAAmB,EAAAxE,EACnB,CAAA,MAAAyE,GAAuB,IAAI,EAAAzE,EAC3B,CAAA,UAAA0E,GAAgC,MAAS,EAAA1E,EACzC,CAAA,eAAA2E,EAA4C,MAAS,EAAA3E,EACrD,CAAA,OAAA4E,GAA6B,MAAS,EAAA5E,EAkBxB,MAAA6E,GAAA,IAAAxD,EAAO,SAAS,eAAgBsD,CAAc,6CAKtDxC,EAAa2C,qhCA7X3B1D,EAAQrB,EAAQ4B,GAAc5B,CAAK,EAAA,CAAA,CAAA,wCA1GnCkE,EACF9D,GAASJ,GAASA,EAAM,UAAUI,CAAK,IAAM,UAC1C,MAAM,KAAS,IAAA,IAAIiB,EAAM,IAAK2D,GAAMA,EAAE5E,CAAK,CAAA,CAAA,CAAA,gCA6C5Ce,EAAQD,GAAcD,CAAI,wBAY5BoC,EAAA,GAAEF,EAAanD,GAASA,EAAM,UAAUE,CAAC,IAAM,UAAU,4CACvD+D,EAASrD,GAASuC,GAAcvC,EAAM,CAAC,EAAI,IAAMA,EAAM,CAAC,EAAI,GAAI,EAAIA,0BAStEyC,EAAA,GAAE9B,EAASd,EACF,OAAAA,GAAU,SAChB,IACD,SAASA,EAAM,UAAU,EAAGA,EAAM,OAAS,CAAC,CAC5C,EAAAgB,GAAgBhB,EAAMA,EAAM,OAAS,CAAC,CAAA,EACrCA,EACD,MAAS,0CAIPT,IACCA,EAAM,OAAS,cAClB2B,EAAcJ,IAAW,MAAS,EAClC8B,EAAA,GAAA3B,EAAehB,GAAeiB,EAAc,MAAQ,MAAS,SAE7DA,EAAcJ,IAAW,QAAavB,EAAM,UAAUE,CAAC,IAAM,SAAS,EACtEmD,EAAA,GAAA3B,EAAehB,GAA4B,KAAK,8BAyBhD2C,EAAA,GAAAC,EACF3D,GAAUwC,GAAa,OAAO,iBAAiBxC,CAAM,EAAI,IAAI,4CAsH7D2D,GAAkB,sBAAsBb,EAAU"}
{"version": 3, "file": "Example-CZ-iEz1g.js", "sources": ["../../../../js/checkbox/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: boolean | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value !== null ? value.toLocaleString() : \"\"}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n"], "names": ["t_value", "ctx", "toggle_class", "div", "insert", "target", "anchor", "dirty", "set_data", "t", "value", "$$props", "type", "selected"], "mappings": "mMAWEA,GAAAC,OAAU,KAAOA,KAAM,eAAc,EAAK,IAAE,gEAJhCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,mBADHC,EAAA,GAAAP,KAAAA,GAAAC,OAAU,KAAOA,KAAM,eAAc,EAAK,IAAE,KAAAO,EAAAC,EAAAT,CAAA,OAJhCE,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,0EAPtB,MAAAS,CAAqB,EAAAC,GACrB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF"}
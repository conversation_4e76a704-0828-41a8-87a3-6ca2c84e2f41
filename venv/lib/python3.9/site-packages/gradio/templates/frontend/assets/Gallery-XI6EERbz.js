import{I as le,C as Ye}from"./Index-DB1XLvMK.js";import{B as Ze}from"./BlockLabel-BlSr62f_.js";import{E as $e}from"./Empty-BgF7sXBn.js";import{S as xe}from"./ShareButton-DRfMJDgB.js";import{D as et}from"./Download-DVtk-Jv3.js";import{a as oe,M as ce}from"./Minimize-X5PPawdt.js";import{I as Te}from"./Image-Bsh8Umrh.js";import"./index-BQPjLIsY.js";/* empty css                                                   */import{M as tt}from"./ModifyUpload-CEEIIKhx.js";import{I as fe}from"./Image-CJc3fwmN.js";/* empty css                                                   */import{u as lt}from"./Blocks-CyfcXtBq.js";import"./svelte/svelte.js";import"./Undo-CpmTQw3B.js";import"./DownloadLink-CHpWw1Ex.js";import"./file-url-SIRImsEF.js";import"./Button-BIUaXfcG.js";var _e=Object.prototype.hasOwnProperty;function me(l,e,t){for(t of l.keys())if(Y(t,e))return t}function Y(l,e){var t,n,i;if(l===e)return!0;if(l&&e&&(t=l.constructor)===e.constructor){if(t===Date)return l.getTime()===e.getTime();if(t===RegExp)return l.toString()===e.toString();if(t===Array){if((n=l.length)===e.length)for(;n--&&Y(l[n],e[n]););return n===-1}if(t===Set){if(l.size!==e.size)return!1;for(n of l)if(i=n,i&&typeof i=="object"&&(i=me(e,i),!i)||!e.has(i))return!1;return!0}if(t===Map){if(l.size!==e.size)return!1;for(n of l)if(i=n[0],i&&typeof i=="object"&&(i=me(e,i),!i)||!Y(n[1],e.get(i)))return!1;return!0}if(t===ArrayBuffer)l=new Uint8Array(l),e=new Uint8Array(e);else if(t===DataView){if((n=l.byteLength)===e.byteLength)for(;n--&&l.getInt8(n)===e.getInt8(n););return n===-1}if(ArrayBuffer.isView(l)){if((n=l.byteLength)===e.byteLength)for(;n--&&l[n]===e[n];);return n===-1}if(!t||typeof l=="object"){n=0;for(t in l)if(_e.call(l,t)&&++n&&!_e.call(e,t)||!(t in e)||!Y(l[t],e[t]))return!1;return Object.keys(e).length===n}}return l!==l&&e!==e}async function nt(l){return l?`<div style="display: flex; flex-wrap: wrap; gap: 16px">${(await Promise.all(l.map(async([t,n])=>t===null||!t.url?"":await lt(t.url)))).map(t=>`<img src="${t}" style="height: 400px" />`).join("")}</div>`:""}const{SvelteComponent:it,add_render_callback:rt,append:E,attr:L,binding_callbacks:re,bubble:he,check_outros:q,create_component:U,destroy_component:C,destroy_each:De,detach:N,element:M,empty:ot,ensure_array_like:te,flush:T,globals:ft,group_outros:F,init:st,insert:P,listen:Z,mount_component:S,noop:Re,run_all:ut,safe_not_equal:at,set_data:Ae,set_style:H,space:A,text:Me,toggle_class:D,transition_in:h,transition_out:p}=window.__gradio__svelte__internal,{window:Ue}=ft,{createEventDispatcher:ct,onMount:_t}=window.__gradio__svelte__internal,{tick:mt}=window.__gradio__svelte__internal;function ge(l,e,t){const n=l.slice();return n[47]=e[t],n[49]=t,n}function be(l,e,t){const n=l.slice();return n[50]=e[t],n[51]=e,n[49]=t,n}function de(l){let e,t;return e=new Ze({props:{show_label:l[2],Icon:Te,label:l[3]||"Gallery"}}),{c(){U(e.$$.fragment)},m(n,i){S(e,n,i),t=!0},p(n,i){const s={};i[0]&4&&(s.show_label=n[2]),i[0]&8&&(s.label=n[3]||"Gallery"),e.$set(s)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function ht(l){let e,t,n,i,s,w,d,f=l[21]&&l[7]&&we(l),m=l[12]&&ze(l),b=l[9]&&Ee(l),k=te(l[15]),r=[];for(let u=0;u<k.length;u+=1)r[u]=Be(ge(l,k,u));const j=u=>p(r[u],1,1,()=>{r[u]=null});return{c(){e=M("div"),f&&f.c(),t=A(),n=M("div"),i=M("div"),m&&m.c(),s=A(),b&&b.c(),w=A();for(let u=0;u<r.length;u+=1)r[u].c();L(i,"class","grid-container svelte-1glfvaj"),H(i,"--grid-cols",l[4]),H(i,"--grid-rows",l[5]),H(i,"--object-fit",l[8]),H(i,"height",l[6]),D(i,"pt-6",l[2]),L(n,"class","grid-wrap svelte-1glfvaj"),D(n,"minimal",l[13]==="minimal"),D(n,"fixed-height",l[13]!=="minimal"&&(!l[6]||l[6]=="auto")),D(n,"hidden",l[16]),L(e,"class","gallery-container")},m(u,c){P(u,e,c),f&&f.m(e,null),E(e,t),E(e,n),E(n,i),m&&m.m(i,null),E(i,s),b&&b.m(i,null),E(i,w);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(i,null);l[42](e),d=!0},p(u,c){if(u[21]&&u[7]?f?(f.p(u,c),c[0]&2097280&&h(f,1)):(f=we(u),f.c(),h(f,1),f.m(e,t)):f&&(F(),p(f,1,1,()=>{f=null}),q()),u[12]?m?(m.p(u,c),c[0]&4096&&h(m,1)):(m=ze(u),m.c(),h(m,1),m.m(i,s)):m&&(F(),p(m,1,1,()=>{m=null}),q()),u[9]?b?(b.p(u,c),c[0]&512&&h(b,1)):(b=Ee(u),b.c(),h(b,1),b.m(i,w)):b&&(F(),p(b,1,1,()=>{b=null}),q()),c[0]&32770){k=te(u[15]);let g;for(g=0;g<k.length;g+=1){const I=ge(u,k,g);r[g]?(r[g].p(I,c),h(r[g],1)):(r[g]=Be(I),r[g].c(),h(r[g],1),r[g].m(i,null))}for(F(),g=k.length;g<r.length;g+=1)j(g);q()}(!d||c[0]&16)&&H(i,"--grid-cols",u[4]),(!d||c[0]&32)&&H(i,"--grid-rows",u[5]),(!d||c[0]&256)&&H(i,"--object-fit",u[8]),(!d||c[0]&64)&&H(i,"height",u[6]),(!d||c[0]&4)&&D(i,"pt-6",u[2]),(!d||c[0]&8192)&&D(n,"minimal",u[13]==="minimal"),(!d||c[0]&8256)&&D(n,"fixed-height",u[13]!=="minimal"&&(!u[6]||u[6]=="auto")),(!d||c[0]&65536)&&D(n,"hidden",u[16])},i(u){if(!d){h(f),h(m),h(b);for(let c=0;c<k.length;c+=1)h(r[c]);d=!0}},o(u){p(f),p(m),p(b),r=r.filter(Boolean);for(let c=0;c<r.length;c+=1)p(r[c]);d=!1},d(u){u&&N(e),f&&f.d(),m&&m.d(),b&&b.d(),De(r,u),l[42](null)}}}function gt(l){let e,t;return e=new $e({props:{unpadded_box:!0,size:"large",$$slots:{default:[bt]},$$scope:{ctx:l}}}),{c(){U(e.$$.fragment)},m(n,i){S(e,n,i),t=!0},p(n,i){const s={};i[1]&2097152&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function we(l){let e,t,n,i,s,w,d,f,m,b,k,r,j,u,c=l[10]&&pe(l),g=l[14]&&!l[16]&&ke(l),I=l[14]&&l[16]&&ve(l),_=!l[16]&&je(l);f=new fe({props:{"data-testid":"detailed-image",src:l[21].image.url,alt:l[21].caption||"",title:l[21].caption||null,class:l[21].caption&&"with-caption",loading:"lazy"}});let B=l[21]?.caption&&ye(l),O=te(l[15]),y=[];for(let a=0;a<O.length;a+=1)y[a]=Ie(be(l,O,a));const $=a=>p(y[a],1,1,()=>{y[a]=null});return{c(){e=M("button"),t=M("div"),c&&c.c(),n=A(),g&&g.c(),i=A(),I&&I.c(),s=A(),_&&_.c(),w=A(),d=M("button"),U(f.$$.fragment),m=A(),B&&B.c(),b=A(),k=M("div");for(let a=0;a<y.length;a+=1)y[a].c();L(t,"class","icon-buttons svelte-1glfvaj"),L(d,"class","image-button svelte-1glfvaj"),H(d,"height","calc(100% - "+(l[21].caption?"80px":"60px")+")"),L(d,"aria-label","detailed view of selected image"),L(k,"class","thumbnails scroll-hide svelte-1glfvaj"),L(k,"data-testid","container_el"),L(e,"class","preview svelte-1glfvaj"),D(e,"minimal",l[13]==="minimal")},m(a,v){P(a,e,v),E(e,t),c&&c.m(t,null),E(t,n),g&&g.m(t,null),E(t,i),I&&I.m(t,null),E(t,s),_&&_.m(t,null),E(e,w),E(e,d),S(f,d,null),E(e,m),B&&B.m(e,null),E(e,b),E(e,k);for(let R=0;R<y.length;R+=1)y[R]&&y[R].m(k,null);l[37](k),r=!0,j||(u=[Z(d,"click",l[34]),Z(e,"keydown",l[23])],j=!0)},p(a,v){a[10]?c?(c.p(a,v),v[0]&1024&&h(c,1)):(c=pe(a),c.c(),h(c,1),c.m(t,n)):c&&(F(),p(c,1,1,()=>{c=null}),q()),a[14]&&!a[16]?g?(g.p(a,v),v[0]&81920&&h(g,1)):(g=ke(a),g.c(),h(g,1),g.m(t,i)):g&&(F(),p(g,1,1,()=>{g=null}),q()),a[14]&&a[16]?I?(I.p(a,v),v[0]&81920&&h(I,1)):(I=ve(a),I.c(),h(I,1),I.m(t,s)):I&&(F(),p(I,1,1,()=>{I=null}),q()),a[16]?_&&(F(),p(_,1,1,()=>{_=null}),q()):_?(_.p(a,v),v[0]&65536&&h(_,1)):(_=je(a),_.c(),h(_,1),_.m(t,null));const R={};if(v[0]&2097152&&(R.src=a[21].image.url),v[0]&2097152&&(R.alt=a[21].caption||""),v[0]&2097152&&(R.title=a[21].caption||null),v[0]&2097152&&(R.class=a[21].caption&&"with-caption"),f.$set(R),(!r||v[0]&2097152)&&H(d,"height","calc(100% - "+(a[21].caption?"80px":"60px")+")"),a[21]?.caption?B?B.p(a,v):(B=ye(a),B.c(),B.m(e,b)):B&&(B.d(1),B=null),v[0]&303106){O=te(a[15]);let z;for(z=0;z<O.length;z+=1){const V=be(a,O,z);y[z]?(y[z].p(V,v),h(y[z],1)):(y[z]=Ie(V),y[z].c(),h(y[z],1),y[z].m(k,null))}for(F(),z=O.length;z<y.length;z+=1)$(z);q()}(!r||v[0]&8192)&&D(e,"minimal",a[13]==="minimal")},i(a){if(!r){h(c),h(g),h(I),h(_),h(f.$$.fragment,a);for(let v=0;v<O.length;v+=1)h(y[v]);r=!0}},o(a){p(c),p(g),p(I),p(_),p(f.$$.fragment,a),y=y.filter(Boolean);for(let v=0;v<y.length;v+=1)p(y[v]);r=!1},d(a){a&&N(e),c&&c.d(),g&&g.d(),I&&I.d(),_&&_.d(),C(f),B&&B.d(),De(y,a),l[37](null),j=!1,ut(u)}}}function pe(l){let e,t;return e=new le({props:{Icon:et,label:l[11]("common.download")}}),e.$on("click",l[32]),{c(){U(e.$$.fragment)},m(n,i){S(e,n,i),t=!0},p(n,i){const s={};i[0]&2048&&(s.label=n[11]("common.download")),e.$set(s)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function ke(l){let e,t;return e=new le({props:{Icon:l[16]?oe:ce,label:l[16]?"Exit full screen":"View in full screen"}}),e.$on("click",l[25]),{c(){U(e.$$.fragment)},m(n,i){S(e,n,i),t=!0},p(n,i){const s={};i[0]&65536&&(s.Icon=n[16]?oe:ce),i[0]&65536&&(s.label=n[16]?"Exit full screen":"View in full screen"),e.$set(s)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function ve(l){let e,t;return e=new le({props:{Icon:oe,label:"Exit full screen"}}),e.$on("click",l[25]),{c(){U(e.$$.fragment)},m(n,i){S(e,n,i),t=!0},p:Re,i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function je(l){let e,t;return e=new le({props:{Icon:Ye,label:"Close"}}),e.$on("click",l[33]),{c(){U(e.$$.fragment)},m(n,i){S(e,n,i),t=!0},p:Re,i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function ye(l){let e,t=l[21].caption+"",n;return{c(){e=M("caption"),n=Me(t),L(e,"class","caption svelte-1glfvaj")},m(i,s){P(i,e,s),E(e,n)},p(i,s){s[0]&2097152&&t!==(t=i[21].caption+"")&&Ae(n,t)},d(i){i&&N(e)}}}function Ie(l){let e,t,n,i,s=l[49],w,d,f;t=new fe({props:{src:l[50].image.url,title:l[50].caption||null,"data-testid":"thumbnail "+(l[49]+1),alt:"",loading:"lazy"}});const m=()=>l[35](e,s),b=()=>l[35](null,s);function k(){return l[36](l[49])}return{c(){e=M("button"),U(t.$$.fragment),n=A(),L(e,"class","thumbnail-item thumbnail-small svelte-1glfvaj"),L(e,"aria-label",i="Thumbnail "+(l[49]+1)+" of "+l[15].length),D(e,"selected",l[1]===l[49]&&l[13]!=="minimal")},m(r,j){P(r,e,j),S(t,e,null),E(e,n),m(),w=!0,d||(f=Z(e,"click",k),d=!0)},p(r,j){l=r;const u={};j[0]&32768&&(u.src=l[50].image.url),j[0]&32768&&(u.title=l[50].caption||null),t.$set(u),(!w||j[0]&32768&&i!==(i="Thumbnail "+(l[49]+1)+" of "+l[15].length))&&L(e,"aria-label",i),s!==l[49]&&(b(),s=l[49],m()),(!w||j[0]&8194)&&D(e,"selected",l[1]===l[49]&&l[13]!=="minimal")},i(r){w||(h(t.$$.fragment,r),w=!0)},o(r){p(t.$$.fragment,r),w=!1},d(r){r&&N(e),C(t),b(),d=!1,f()}}}function ze(l){let e,t,n;return t=new tt({props:{i18n:l[11],absolute:!1}}),t.$on("clear",l[38]),{c(){e=M("div"),U(t.$$.fragment),L(e,"class","icon-button svelte-1glfvaj")},m(i,s){P(i,e,s),S(t,e,null),n=!0},p(i,s){const w={};s[0]&2048&&(w.i18n=i[11]),t.$set(w)},i(i){n||(h(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&N(e),C(t)}}}function Ee(l){let e,t,n;return t=new xe({props:{i18n:l[11],value:l[15],formatter:nt}}),t.$on("share",l[39]),t.$on("error",l[40]),{c(){e=M("div"),U(t.$$.fragment),L(e,"class","icon-button svelte-1glfvaj")},m(i,s){P(i,e,s),S(t,e,null),n=!0},p(i,s){const w={};s[0]&2048&&(w.i18n=i[11]),s[0]&32768&&(w.value=i[15]),t.$set(w)},i(i){n||(h(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&N(e),C(t)}}}function Le(l){let e,t=l[47].caption+"",n;return{c(){e=M("div"),n=Me(t),L(e,"class","caption-label svelte-1glfvaj")},m(i,s){P(i,e,s),E(e,n)},p(i,s){s[0]&32768&&t!==(t=i[47].caption+"")&&Ae(n,t)},d(i){i&&N(e)}}}function Be(l){let e,t,n,i,s,w,d,f;t=new fe({props:{alt:l[47].caption||"",src:typeof l[47].image=="string"?l[47].image:l[47].image.url,loading:"lazy"}});let m=l[47].caption&&Le(l);function b(){return l[41](l[49])}return{c(){e=M("button"),U(t.$$.fragment),n=A(),m&&m.c(),i=A(),L(e,"class","thumbnail-item thumbnail-lg svelte-1glfvaj"),L(e,"aria-label",s="Thumbnail "+(l[49]+1)+" of "+l[15].length),D(e,"selected",l[1]===l[49])},m(k,r){P(k,e,r),S(t,e,null),E(e,n),m&&m.m(e,null),E(e,i),w=!0,d||(f=Z(e,"click",b),d=!0)},p(k,r){l=k;const j={};r[0]&32768&&(j.alt=l[47].caption||""),r[0]&32768&&(j.src=typeof l[47].image=="string"?l[47].image:l[47].image.url),t.$set(j),l[47].caption?m?m.p(l,r):(m=Le(l),m.c(),m.m(e,i)):m&&(m.d(1),m=null),(!w||r[0]&32768&&s!==(s="Thumbnail "+(l[49]+1)+" of "+l[15].length))&&L(e,"aria-label",s),(!w||r[0]&2)&&D(e,"selected",l[1]===l[49])},i(k){w||(h(t.$$.fragment,k),w=!0)},o(k){p(t.$$.fragment,k),w=!1},d(k){k&&N(e),C(t),m&&m.d(),d=!1,f()}}}function bt(l){let e,t;return e=new Te({}),{c(){U(e.$$.fragment)},m(n,i){S(e,n,i),t=!0},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function dt(l){let e,t,n,i,s,w,d;rt(l[31]);let f=l[2]&&de(l);const m=[gt,ht],b=[];function k(r,j){return r[0]==null||r[15]==null||r[15].length===0?0:1}return t=k(l),n=b[t]=m[t](l),{c(){f&&f.c(),e=A(),n.c(),i=ot()},m(r,j){f&&f.m(r,j),P(r,e,j),b[t].m(r,j),P(r,i,j),s=!0,w||(d=Z(Ue,"resize",l[31]),w=!0)},p(r,j){r[2]?f?(f.p(r,j),j[0]&4&&h(f,1)):(f=de(r),f.c(),h(f,1),f.m(e.parentNode,e)):f&&(F(),p(f,1,1,()=>{f=null}),q());let u=t;t=k(r),t===u?b[t].p(r,j):(F(),p(b[u],1,1,()=>{b[u]=null}),q(),n=b[t],n?n.p(r,j):(n=b[t]=m[t](r),n.c()),h(n,1),n.m(i.parentNode,i))},i(r){s||(h(f),h(n),s=!0)},o(r){p(f),p(n),s=!1},d(r){r&&(N(e),N(i)),f&&f.d(r),b[t].d(r),w=!1,d()}}}function wt(l,e,t){let n,i,s,{show_label:w=!0}=e,{label:d}=e,{value:f=null}=e,{columns:m=[2]}=e,{rows:b=void 0}=e,{height:k="auto"}=e,{preview:r}=e,{allow_preview:j=!0}=e,{object_fit:u="cover"}=e,{show_share_button:c=!1}=e,{show_download_button:g=!1}=e,{i18n:I}=e,{selected_index:_=null}=e,{interactive:B}=e,{_fetch:O}=e,{mode:y="normal"}=e,{show_fullscreen_button:$=!0}=e,a=!1,v;const R=ct();let z=!0,V=null,ne=f;_==null&&r&&f?.length&&(_=0);let ie=_;function se(o){const G=o.target,W=o.offsetX,K=G.offsetWidth/2;W<K?t(1,_=n):t(1,_=i)}function Ce(o){switch(o.code){case"Escape":o.preventDefault(),t(1,_=null);break;case"ArrowLeft":o.preventDefault(),t(1,_=n);break;case"ArrowRight":o.preventDefault(),t(1,_=i);break}}let J=[],X;async function Se(o){if(typeof o!="number"||(await mt(),J[o]===void 0))return;J[o]?.focus();const{left:G,width:W}=X.getBoundingClientRect(),{left:x,width:K}=J[o].getBoundingClientRect(),Q=x-G+K/2-W/2+X.scrollLeft;X&&typeof X.scrollTo=="function"&&X.scrollTo({left:Q<0?0:Q,behavior:"smooth"})}let ue=0;async function ae(o,G){let W;try{W=await O(o)}catch(Q){if(Q instanceof TypeError){window.open(o,"_blank","noreferrer");return}throw Q}const x=await W.blob(),K=URL.createObjectURL(x),ee=document.createElement("a");ee.href=K,ee.download=G,ee.click(),URL.revokeObjectURL(K)}_t(()=>{document.addEventListener("fullscreenchange",()=>{t(16,a=!!document.fullscreenElement)})});const Oe=async()=>{a?await document.exitFullscreen():await v.requestFullscreen()};function Ve(){t(20,ue=Ue.innerHeight)}const qe=()=>{const o=s?.image;if(o==null)return;const{url:G,orig_name:W}=o;G&&ae(G,W??"image")},Fe=()=>t(1,_=null),Ge=o=>se(o);function He(o,G){re[o?"unshift":"push"](()=>{J[G]=o,t(18,J)})}const Ne=o=>t(1,_=o);function Pe(o){re[o?"unshift":"push"](()=>{X=o,t(19,X)})}const Xe=()=>t(0,f=null);function We(o){he.call(this,l,o)}function Je(o){he.call(this,l,o)}const Ke=o=>t(1,_=o);function Qe(o){re[o?"unshift":"push"](()=>{v=o,t(17,v)})}return l.$$set=o=>{"show_label"in o&&t(2,w=o.show_label),"label"in o&&t(3,d=o.label),"value"in o&&t(0,f=o.value),"columns"in o&&t(4,m=o.columns),"rows"in o&&t(5,b=o.rows),"height"in o&&t(6,k=o.height),"preview"in o&&t(26,r=o.preview),"allow_preview"in o&&t(7,j=o.allow_preview),"object_fit"in o&&t(8,u=o.object_fit),"show_share_button"in o&&t(9,c=o.show_share_button),"show_download_button"in o&&t(10,g=o.show_download_button),"i18n"in o&&t(11,I=o.i18n),"selected_index"in o&&t(1,_=o.selected_index),"interactive"in o&&t(12,B=o.interactive),"_fetch"in o&&t(27,O=o._fetch),"mode"in o&&t(13,y=o.mode),"show_fullscreen_button"in o&&t(14,$=o.show_fullscreen_button)},l.$$.update=()=>{l.$$.dirty[0]&268435457&&t(28,z=f==null||f.length===0?!0:z),l.$$.dirty[0]&1&&t(15,V=f==null?null:f.map(o=>({image:o.image,caption:o.caption}))),l.$$.dirty[0]&872415235&&(Y(ne,f)||(z?(t(1,_=r&&f?.length?0:null),t(28,z=!1)):t(1,_=_!=null&&f!=null&&_<f.length?_:null),R("change"),t(29,ne=f))),l.$$.dirty[0]&32770&&(n=((_??0)+(V?.length??0)-1)%(V?.length??0)),l.$$.dirty[0]&32770&&(i=((_??0)+1)%(V?.length??0)),l.$$.dirty[0]&1073774594&&_!==ie&&(t(30,ie=_),_!==null&&R("select",{index:_,value:V?.[_]})),l.$$.dirty[0]&130&&j&&Se(_),l.$$.dirty[0]&32770&&t(21,s=_!=null&&V!=null?V[_]:null)},[f,_,w,d,m,b,k,j,u,c,g,I,B,y,$,V,a,v,J,X,ue,s,se,Ce,ae,Oe,r,O,z,ne,ie,Ve,qe,Fe,Ge,He,Ne,Pe,Xe,We,Je,Ke,Qe]}class Ot extends it{constructor(e){super(),st(this,e,wt,dt,at,{show_label:2,label:3,value:0,columns:4,rows:5,height:6,preview:26,allow_preview:7,object_fit:8,show_share_button:9,show_download_button:10,i18n:11,selected_index:1,interactive:12,_fetch:27,mode:13,show_fullscreen_button:14},null,[-1,-1])}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),T()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),T()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),T()}get columns(){return this.$$.ctx[4]}set columns(e){this.$$set({columns:e}),T()}get rows(){return this.$$.ctx[5]}set rows(e){this.$$set({rows:e}),T()}get height(){return this.$$.ctx[6]}set height(e){this.$$set({height:e}),T()}get preview(){return this.$$.ctx[26]}set preview(e){this.$$set({preview:e}),T()}get allow_preview(){return this.$$.ctx[7]}set allow_preview(e){this.$$set({allow_preview:e}),T()}get object_fit(){return this.$$.ctx[8]}set object_fit(e){this.$$set({object_fit:e}),T()}get show_share_button(){return this.$$.ctx[9]}set show_share_button(e){this.$$set({show_share_button:e}),T()}get show_download_button(){return this.$$.ctx[10]}set show_download_button(e){this.$$set({show_download_button:e}),T()}get i18n(){return this.$$.ctx[11]}set i18n(e){this.$$set({i18n:e}),T()}get selected_index(){return this.$$.ctx[1]}set selected_index(e){this.$$set({selected_index:e}),T()}get interactive(){return this.$$.ctx[12]}set interactive(e){this.$$set({interactive:e}),T()}get _fetch(){return this.$$.ctx[27]}set _fetch(e){this.$$set({_fetch:e}),T()}get mode(){return this.$$.ctx[13]}set mode(e){this.$$set({mode:e}),T()}get show_fullscreen_button(){return this.$$.ctx[14]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),T()}}export{Ot as default};
//# sourceMappingURL=Gallery-XI6EERbz.js.map

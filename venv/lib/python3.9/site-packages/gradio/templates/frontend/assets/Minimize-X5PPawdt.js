const{SvelteComponent:h,append:_,attr:t,detach:d,init:m,insert:v,noop:i,safe_not_equal:u,svg_element:c}=window.__gradio__svelte__internal;function w(r){let e,o;return{c(){e=c("svg"),o=c("path"),t(o,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","2"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-maximize")},m(a,s){v(a,e,s),_(e,o)},p:i,i,o:i,d(a){a&&d(e)}}}class z extends h{constructor(e){super(),m(this,e,null,w,u,{})}}const{SvelteComponent:f,append:g,attr:n,detach:x,init:$,insert:k,noop:l,safe_not_equal:M,svg_element:p}=window.__gradio__svelte__internal;function C(r){let e,o;return{c(){e=p("svg"),o=p("path"),n(o,"d","M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"viewBox","0 0 24 24"),n(e,"fill","none"),n(e,"stroke","currentColor"),n(e,"stroke-width","2"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round"),n(e,"class","feather feather-minimize")},m(a,s){k(a,e,s),g(e,o)},p:l,i:l,o:l,d(a){a&&x(e)}}}class q extends f{constructor(e){super(),$(this,e,null,C,M,{})}}export{z as M,q as a};
//# sourceMappingURL=Minimize-X5PPawdt.js.map

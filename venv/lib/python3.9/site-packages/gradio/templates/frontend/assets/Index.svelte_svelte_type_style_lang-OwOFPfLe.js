import"./Index-DB1XLvMK.js";import{c as I}from"./Blocks-CyfcXtBq.js";import{C as J}from"./Check-CZUQOzJl.js";import{C as K}from"./Copy-B6RcHnoK.js";import{M as L}from"./Example.svelte_svelte_type_style_lang-DXGdiQV3.js";import{f as O}from"./Button-BIUaXfcG.js";const{SvelteComponent:P,action_destroyer:Q,add_render_callback:R,append:U,attr:h,check_outros:N,create_component:z,create_in_transition:V,destroy_component:M,detach:v,element:T,empty:W,flush:d,group_outros:S,init:X,insert:y,listen:Y,mount_component:j,noop:A,safe_not_equal:Z,space:x,toggle_class:w,transition_in:b,transition_out:k}=window.__gradio__svelte__internal,{createEventDispatcher:p}=window.__gradio__svelte__internal;function E(s){let e,t,r,_;const a=[ee,$],n=[];function c(u,m){return u[12]?0:1}return e=c(s),t=n[e]=a[e](s),{c(){t.c(),r=W()},m(u,m){n[e].m(u,m),y(u,r,m),_=!0},p(u,m){let o=e;e=c(u),e===o?n[e].p(u,m):(S(),k(n[o],1,1,()=>{n[o]=null}),N(),t=n[e],t?t.p(u,m):(t=n[e]=a[e](u),t.c()),b(t,1),t.m(r.parentNode,r))},i(u){_||(b(t),_=!0)},o(u){k(t),_=!1},d(u){u&&v(r),n[e].d(u)}}}function $(s){let e,t,r,_,a;return t=new K({}),{c(){e=T("button"),z(t.$$.fragment),h(e,"aria-label","Copy"),h(e,"aria-roledescription","Copy text"),h(e,"class","svelte-gq7qsu")},m(n,c){y(n,e,c),j(t,e,null),r=!0,_||(a=Y(e,"click",s[14]),_=!0)},p:A,i(n){r||(b(t.$$.fragment,n),r=!0)},o(n){k(t.$$.fragment,n),r=!1},d(n){n&&v(e),M(t),_=!1,a()}}}function ee(s){let e,t,r,_;return t=new J({}),{c(){e=T("button"),z(t.$$.fragment),h(e,"aria-label","Copied"),h(e,"aria-roledescription","Text copied"),h(e,"class","svelte-gq7qsu")},m(a,n){y(a,e,n),j(t,e,null),_=!0},p:A,i(a){_||(b(t.$$.fragment,a),a&&(r||R(()=>{r=V(e,O,{duration:300}),r.start()})),_=!0)},o(a){k(t.$$.fragment,a),_=!1},d(a){a&&v(e),M(t)}}}function te(s){let e,t,r,_,a,n,c,u,m,o=s[10]&&E(s);return r=new L({props:{message:s[2],latex_delimiters:s[7],sanitize_html:s[5],line_breaks:s[6],chatbot:!1,header_links:s[8],root:s[11]}}),{c(){e=T("div"),o&&o.c(),t=x(),z(r.$$.fragment),h(e,"class",_="prose "+s[0].join(" ")+" svelte-gq7qsu"),h(e,"data-testid","markdown"),h(e,"dir",a=s[4]?"rtl":"ltr"),h(e,"style",n=s[9]?`max-height: ${s[13](s[9])}; overflow-y: auto;`:""),w(e,"min",s[3]),w(e,"hide",!s[1])},m(l,f){y(l,e,f),o&&o.m(e,null),U(e,t),j(r,e,null),c=!0,u||(m=Q(I.call(null,e)),u=!0)},p(l,[f]){l[10]?o?(o.p(l,f),f&1024&&b(o,1)):(o=E(l),o.c(),b(o,1),o.m(e,t)):o&&(S(),k(o,1,1,()=>{o=null}),N());const g={};f&4&&(g.message=l[2]),f&128&&(g.latex_delimiters=l[7]),f&32&&(g.sanitize_html=l[5]),f&64&&(g.line_breaks=l[6]),f&256&&(g.header_links=l[8]),f&2048&&(g.root=l[11]),r.$set(g),(!c||f&1&&_!==(_="prose "+l[0].join(" ")+" svelte-gq7qsu"))&&h(e,"class",_),(!c||f&16&&a!==(a=l[4]?"rtl":"ltr"))&&h(e,"dir",a),(!c||f&512&&n!==(n=l[9]?`max-height: ${l[13](l[9])}; overflow-y: auto;`:""))&&h(e,"style",n),(!c||f&9)&&w(e,"min",l[3]),(!c||f&3)&&w(e,"hide",!l[1])},i(l){c||(b(o),b(r.$$.fragment,l),c=!0)},o(l){k(o),k(r.$$.fragment,l),c=!1},d(l){l&&v(e),o&&o.d(),M(r),u=!1,m()}}}function ie(s,e,t){let{elem_classes:r=[]}=e,{visible:_=!0}=e,{value:a}=e,{min_height:n=!1}=e,{rtl:c=!1}=e,{sanitize_html:u=!0}=e,{line_breaks:m=!1}=e,{latex_delimiters:o}=e,{header_links:l=!1}=e,{height:f=void 0}=e,{show_copy_button:g=!1}=e,{root:D}=e,q=!1,C;const B=p(),F=i=>typeof i=="number"?i+"px":i;async function G(){"clipboard"in navigator&&(await navigator.clipboard.writeText(a),H())}function H(){t(12,q=!0),C&&clearTimeout(C),C=setTimeout(()=>{t(12,q=!1)},1e3)}return s.$$set=i=>{"elem_classes"in i&&t(0,r=i.elem_classes),"visible"in i&&t(1,_=i.visible),"value"in i&&t(2,a=i.value),"min_height"in i&&t(3,n=i.min_height),"rtl"in i&&t(4,c=i.rtl),"sanitize_html"in i&&t(5,u=i.sanitize_html),"line_breaks"in i&&t(6,m=i.line_breaks),"latex_delimiters"in i&&t(7,o=i.latex_delimiters),"header_links"in i&&t(8,l=i.header_links),"height"in i&&t(9,f=i.height),"show_copy_button"in i&&t(10,g=i.show_copy_button),"root"in i&&t(11,D=i.root)},s.$$.update=()=>{s.$$.dirty&4&&B("change")},[r,_,a,n,c,u,m,o,l,f,g,D,q,F,G]}class le extends P{constructor(e){super(),X(this,e,ie,te,Z,{elem_classes:0,visible:1,value:2,min_height:3,rtl:4,sanitize_html:5,line_breaks:6,latex_delimiters:7,header_links:8,height:9,show_copy_button:10,root:11})}get elem_classes(){return this.$$.ctx[0]}set elem_classes(e){this.$$set({elem_classes:e}),d()}get visible(){return this.$$.ctx[1]}set visible(e){this.$$set({visible:e}),d()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),d()}get min_height(){return this.$$.ctx[3]}set min_height(e){this.$$set({min_height:e}),d()}get rtl(){return this.$$.ctx[4]}set rtl(e){this.$$set({rtl:e}),d()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),d()}get line_breaks(){return this.$$.ctx[6]}set line_breaks(e){this.$$set({line_breaks:e}),d()}get latex_delimiters(){return this.$$.ctx[7]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),d()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),d()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),d()}get show_copy_button(){return this.$$.ctx[10]}set show_copy_button(e){this.$$set({show_copy_button:e}),d()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),d()}}const ue=le;export{ue as M};
//# sourceMappingURL=Index.svelte_svelte_type_style_lang-OwOFPfLe.js.map

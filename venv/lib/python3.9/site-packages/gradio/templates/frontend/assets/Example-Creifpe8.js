const{SvelteComponent:m,detach:c,empty:x,flush:u,init:y,insert:_,noop:d,safe_not_equal:a,set_data:o,text:r}=window.__gradio__svelte__internal;function b(s){let t,n,l;return{c(){t=r(s[1]),n=r(" x "),l=r(s[2])},m(e,i){_(e,t,i),_(e,n,i),_(e,l,i)},p(e,i){i&2&&o(t,e[1]),i&4&&o(l,e[2])},d(e){e&&(c(t),c(n),c(l))}}}function h(s){let t;return{c(){t=r(s[0])},m(n,l){_(n,t,l)},p(n,l){l&1&&o(t,n[0])},d(n){n&&c(t)}}}function k(s){let t;function n(i,f){return i[0]?h:b}let l=n(s),e=l(s);return{c(){e.c(),t=x()},m(i,f){e.m(i,f),_(i,t,f)},p(i,[f]){l===(l=n(i))&&e?e.p(i,f):(e.d(1),e=l(i),e&&(e.c(),e.m(t.parentNode,t)))},i:d,o:d,d(i){i&&c(t),e.d(i)}}}function w(s,t,n){let{title:l}=t,{x:e}=t,{y:i}=t;return s.$$set=f=>{"title"in f&&n(0,l=f.title),"x"in f&&n(1,e=f.x),"y"in f&&n(2,i=f.y)},[l,e,i]}class g extends m{constructor(t){super(),y(this,t,w,k,a,{title:0,x:1,y:2})}get title(){return this.$$.ctx[0]}set title(t){this.$$set({title:t}),u()}get x(){return this.$$.ctx[1]}set x(t){this.$$set({x:t}),u()}get y(){return this.$$.ctx[2]}set y(t){this.$$set({y:t}),u()}}export{g as default};
//# sourceMappingURL=Example-Creifpe8.js.map

const{SvelteComponent:g,append:_,attr:o,detach:v,element:y,flush:c,init:m,insert:b,noop:r,safe_not_equal:x,set_data:q,text:w,toggle_class:a}=window.__gradio__svelte__internal;function k(n){let e,i;return{c(){e=y("div"),i=w(n[2]),o(e,"class","svelte-1ayixqk"),a(e,"table",n[0]==="table"),a(e,"gallery",n[0]==="gallery"),a(e,"selected",n[1])},m(t,s){b(t,e,s),_(e,i)},p(t,[s]){s&4&&q(i,t[2]),s&1&&a(e,"table",t[0]==="table"),s&1&&a(e,"gallery",t[0]==="gallery"),s&2&&a(e,"selected",t[1])},i:r,o:r,d(t){t&&v(e)}}}function C(n,e,i){let{value:t}=e,{type:s}=e,{selected:h=!1}=e,{choices:u}=e,f;if(t===null)f="";else{let l=u.find(d=>d[1]===t);f=l?l[0]:""}return n.$$set=l=>{"value"in l&&i(3,t=l.value),"type"in l&&i(0,s=l.type),"selected"in l&&i(1,h=l.selected),"choices"in l&&i(4,u=l.choices)},[s,h,f,t,u]}class E extends g{constructor(e){super(),m(this,e,C,k,x,{value:3,type:0,selected:1,choices:4})}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),c()}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),c()}get selected(){return this.$$.ctx[1]}set selected(e){this.$$set({selected:e}),c()}get choices(){return this.$$.ctx[4]}set choices(e){this.$$set({choices:e}),c()}}export{E as default};
//# sourceMappingURL=Example-BoMLuz1A.js.map

{"version": 3, "file": "Example-BaLyJYAe.js", "sources": ["../../../../js/colorpicker/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: string | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tstyle=\"background-color: {value ? value : 'black'}\"\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n/>\n\n<style>\n\tdiv {\n\t\twidth: var(--size-10);\n\t\theight: var(--size-10);\n\t}\n\t.table {\n\t\tmargin: 0 auto;\n\t}\n</style>\n"], "names": ["ctx", "toggle_class", "div", "insert", "target", "anchor", "value", "$$props", "type", "selected"], "mappings": "iOAO2BA,EAAK,CAAA,EAAGA,EAAK,CAAA,EAAG,OAAO,+BACpCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAHlCG,EAKCC,EAAAF,EAAAG,CAAA,wCAJ0BL,EAAK,CAAA,EAAGA,EAAK,CAAA,EAAG,OAAO,OACpCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,0EARtB,MAAAM,CAAoB,EAAAC,GACpB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF"}
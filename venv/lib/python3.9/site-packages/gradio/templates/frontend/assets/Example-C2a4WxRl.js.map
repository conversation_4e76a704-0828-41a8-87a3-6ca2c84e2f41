{"version": 3, "file": "Example-C2a4WxRl.js", "sources": ["../../../../js/html/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n\tclass=\"prose\"\n>\n\t{@html value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-2);\n\t}\n</style>\n"], "names": ["toggle_class", "div", "ctx", "insert", "target", "anchor", "value", "$$props", "type", "selected"], "mappings": "iOAOcA,EAAAC,EAAA,QAAAC,OAAS,OAAO,EACdF,EAAAC,EAAA,UAAAC,OAAS,SAAS,+BAFlCC,EAOKC,EAAAH,EAAAI,CAAA,cADGH,EAAK,CAAA,8BAALA,EAAK,CAAA,QALCF,EAAAC,EAAA,QAAAC,OAAS,OAAO,OACdF,EAAAC,EAAA,UAAAC,OAAS,SAAS,0EAPtB,MAAAI,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF"}
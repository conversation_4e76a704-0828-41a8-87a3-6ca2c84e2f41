const{SvelteComponent:j,attr:h,detach:x,element:B,flush:g,init:C,insert:S,noop:u,safe_not_equal:D}=window.__gradio__svelte__internal,{onDestroy:P,createEventDispatcher:$}=window.__gradio__svelte__internal;function q(s){let e;return{c(){e=B("div"),h(e,"data-testid","bokeh"),h(e,"id",s[0]),h(e,"class","gradio-bokeh svelte-1rhu6ax")},m(n,r){S(n,e,r)},p:u,i:u,o:u,d(n){n&&x(e)}}}function I(s,e,n){let r,{value:c}=e,{bokeh_version:o}=e;const a=`bokehDiv-${Math.random().toString(5).substring(2)}`,v=$();async function y(t){if(document&&document.getElementById(a)&&(document.getElementById(a).innerHTML=""),window.Bokeh){b();let l=JSON.parse(t);(await window.Bokeh.embed.embed_item(l,a))._roots.forEach(async i=>{await i.ready,v("load")})}}const _=`https://cdn.bokeh.org/bokeh/release/bokeh-${o}.min.js`,w=[`https://cdn.pydata.org/bokeh/release/bokeh-widgets-${o}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-tables-${o}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-gl-${o}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-api-${o}.min.js`];let d=!1;async function E(){await Promise.all(w.map((t,l)=>new Promise(f=>{const i=document.createElement("script");return i.onload=f,i.src=t,document.head.appendChild(i),i}))),n(3,d=!0)}let m=[];function p(){m=E()}function b(){const t=document.createElement("script");return t.onload=p,t.src=_,document.head.querySelector(`script[src="${_}"]`)?p():document.head.appendChild(t),t}const k=o?b():null;return P(()=>{k in document.children&&(document.removeChild(k),m.forEach(t=>document.removeChild(t)))}),s.$$set=t=>{"value"in t&&n(1,c=t.value),"bokeh_version"in t&&n(2,o=t.bokeh_version)},s.$$.update=()=>{s.$$.dirty&2&&n(4,r=c?.plot),s.$$.dirty&24&&d&&y(r)},[a,c,o,d,r]}class M extends j{constructor(e){super(),C(this,e,I,q,D,{value:1,bokeh_version:2})}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),g()}get bokeh_version(){return this.$$.ctx[2]}set bokeh_version(e){this.$$set({bokeh_version:e}),g()}}export{M as default};
//# sourceMappingURL=BokehPlot-CHWxUyLl.js.map

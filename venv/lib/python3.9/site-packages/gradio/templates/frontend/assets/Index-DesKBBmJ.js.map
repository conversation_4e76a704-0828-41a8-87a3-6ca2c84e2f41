{"version": 3, "file": "Index-DesKBBmJ.js", "sources": ["../../../../js/accordion/shared/Accordion.svelte", "../../../../js/accordion/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let open = true;\n\texport let label = \"\";\n</script>\n\n<button on:click={() => (open = !open)} class=\"label-wrap\" class:open>\n\t<span>{label}</span>\n\t<span style:transform={open ? \"rotate(0)\" : \"rotate(90deg)\"} class=\"icon\">\n\t\t▼\n\t</span>\n</button>\n<div style:display={open ? \"block\" : \"none\"}>\n\t<slot />\n</div>\n\n<style>\n\tspan {\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--section-header-text-size);\n\t}\n\t.label-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tcursor: pointer;\n\t\twidth: var(--size-full);\n\t\tcolor: var(--accordion-text-color);\n\t}\n\t.label-wrap.open {\n\t\tmargin-bottom: var(--size-2);\n\t}\n\n\t.icon {\n\t\ttransition: 150ms;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport Accordion from \"./shared/Accordion.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\timport Column from \"@gradio/column\";\n\timport type { Gradio } from \"@gradio/utils\";\n\n\texport let label: string;\n\texport let elem_id: string;\n\texport let elem_classes: string[];\n\texport let visible = true;\n\texport let open = true;\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio;\n</script>\n\n<Block {elem_id} {elem_classes} {visible}>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t/>\n\n\t<Accordion {label} bind:open>\n\t\t<Column>\n\t\t\t<slot />\n\t\t</Column>\n\t</Accordion>\n</Block>\n"], "names": ["ctx", "insert", "target", "button", "anchor", "append", "span0", "span1", "div", "open", "$$props", "label", "click_handler", "$$invalidate", "dirty", "elem_id", "elem_classes", "visible", "loading_status", "gradio"], "mappings": "ulBAMQA,EAAK,CAAA,CAAA,iJACWA,EAAI,CAAA,EAAG,YAAc,eAAe,0EAIxCA,EAAI,CAAA,EAAG,QAAU,MAAM,UAN3CC,EAKQC,EAAAC,EAAAC,CAAA,EAJPC,EAAmBF,EAAAG,CAAA,gBACnBD,EAEMF,EAAAI,CAAA,WAEPN,EAEKC,EAAAM,EAAAJ,CAAA,6EAPGJ,EAAK,CAAA,CAAA,uBACWA,EAAI,CAAA,EAAG,YAAc,eAAe,kHAIxCA,EAAI,CAAA,EAAG,QAAU,MAAM,0IAV/B,CAAA,KAAAS,EAAO,EAAI,EAAAC,EACX,CAAA,MAAAC,EAAQ,EAAE,EAAAD,EAGG,MAAAE,EAAA,IAAAC,EAAA,EAAAJ,GAAQA,CAAI,wwCCevB,CAAA,WAAAT,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,0VAFNc,EAAA,IAAA,CAAA,WAAAd,KAAO,UAAU,EACvBc,EAAA,IAAA,CAAA,KAAAd,KAAO,IAAI,WACbA,EAAc,CAAA,CAAA,usBAbR,MAAAW,CAAa,EAAAD,GACb,QAAAK,CAAe,EAAAL,GACf,aAAAM,CAAsB,EAAAN,EACtB,CAAA,QAAAO,EAAU,EAAI,EAAAP,EACd,CAAA,KAAAD,EAAO,EAAI,EAAAC,GACX,eAAAQ,CAA6B,EAAAR,GAC7B,OAAAS,CAAc,EAAAT"}
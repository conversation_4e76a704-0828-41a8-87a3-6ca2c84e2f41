{"version": 3, "mappings": ";mmCAAAA,EAoBKC,EAAAC,EAAAC,CAAA,EATJC,EAAkDF,EAAAG,CAAA,EAClDD,EAAkDF,EAAAI,CAAA,EAClDF,EAAmDF,EAAAK,CAAA,EACnDH,EAAkDF,EAAAM,CAAA,EAClDJ,EAAmDF,EAAAO,CAAA,EACnDL,EAGCF,EAAAQ,CAAA,gbCyCoB,qRAfdC,EAAa,qVAAbA,EAAa,oxBAFhB,OAAAA,MAASA,EAAa,kUApCf,MAAAC,CAAK,EAAAC,GACL,OAAAZ,CAAmB,EAAAY,GACnB,OAAAC,EAAM,IAAAD,GACN,WAAAE,CAAqB,EAAAF,GACrB,QAAAG,CAAe,EAAAH,GACf,cAAAI,CAA4B,EAAAJ,GAC5B,oBAAAK,CAAyB,EAAAL,GACzB,OAAAM,CAET,EAAAN,EACS,OAAAO,EAAiC,IAAI,EAAAP,GACrC,YAAAQ,CAAoB,EAAAR,EAE3BS,EAAqB,KACrBC,EAAQX,GAAO,WAEbY,EAAe,CACpB,wBAAqB,0BAAgC,4CACrD,uBAAoB,yBAA+B,0CACnD,wBAAqB,0BAAgC,0DACrD,4BAAyB,8BAAoC,ogBAG7D,KACIC,EAAOb,GAAO,KACda,IAASF,GACZG,EAAA,GAAAJ,EAAgB,IAAI,EAEjBG,GAAQA,KAAQD,GACnBA,EAAgBC,CAAI,IAAI,KAAME,GAAM,MACnCL,EAAgBK,EAAO,OAAO", "names": ["insert", "target", "svg", "anchor", "append", "circle0", "circle1", "circle2", "circle3", "circle4", "path", "ctx", "value", "$$props", "colors", "theme_mode", "caption", "bokeh_version", "show_actions_button", "gradio", "x_lim", "_selectable", "PlotComponent", "_type", "plotTypeMapping", "type", "$$invalidate", "module"], "ignoreList": [], "sources": ["../../../../js/icons/src/Plot.svelte", "../../../../js/plot/shared/Plot.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<circle cx=\"20\" cy=\"4\" r=\"2\" fill=\"currentColor\" />\n\t<circle cx=\"8\" cy=\"16\" r=\"2\" fill=\"currentColor\" />\n\t<circle cx=\"28\" cy=\"12\" r=\"2\" fill=\"currentColor\" />\n\t<circle cx=\"11\" cy=\"7\" r=\"2\" fill=\"currentColor\" />\n\t<circle cx=\"16\" cy=\"24\" r=\"2\" fill=\"currentColor\" />\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M30 3.413L28.586 2L4 26.585V2H2v26a2 2 0 0 0 2 2h26v-2H5.413Z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\t//@ts-nocheck\n\timport { Plot as PlotIcon } from \"@gradio/icons\";\n\timport { Empty } from \"@gradio/atoms\";\n\timport type { ThemeMode } from \"js/core/src/components/types\";\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\n\texport let value;\n\texport let target: HTMLElement;\n\texport let colors: string[] = [];\n\texport let theme_mode: ThemeMode;\n\texport let caption: string;\n\texport let bokeh_version: string | null;\n\texport let show_actions_button: bool;\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t}>;\n\texport let x_lim: [number, number] | null = null;\n\texport let _selectable: boolean;\n\n\tlet PlotComponent: any = null;\n\tlet _type = value?.type;\n\n\tconst plotTypeMapping = {\n\t\tplotly: () => import(\"./plot_types/PlotlyPlot.svelte\"),\n\t\tbokeh: () => import(\"./plot_types/BokehPlot.svelte\"),\n\t\taltair: () => import(\"./plot_types/AltairPlot.svelte\"),\n\t\tmatplotlib: () => import(\"./plot_types/MatplotlibPlot.svelte\")\n\t};\n\n\t$: {\n\t\tlet type = value?.type;\n\t\tif (type !== _type) {\n\t\t\tPlotComponent = null;\n\t\t}\n\t\tif (type && type in plotTypeMapping) {\n\t\t\tplotTypeMapping[type]().then((module) => {\n\t\t\t\tPlotComponent = module.default;\n\t\t\t});\n\t\t}\n\t}\n</script>\n\n{#if value && PlotComponent}\n\t<svelte:component\n\t\tthis={PlotComponent}\n\t\t{value}\n\t\t{target}\n\t\t{colors}\n\t\t{theme_mode}\n\t\t{caption}\n\t\t{bokeh_version}\n\t\t{show_actions_button}\n\t\t{gradio}\n\t\t{_selectable}\n\t\t{x_lim}\n\t\ton:load\n\t\ton:select\n\t/>\n{:else}\n\t<Empty unpadded_box={true} size=\"large\"><PlotIcon /></Empty>\n{/if}\n"], "file": "assets/Plot-Ce8WWIhc.js"}
const __vite__fileDeps=["./Index-DB1XLvMK.js","./Index-BEyjvDG_.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import*as Ue from"./svelte/svelte.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))o(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&o(s)}).observe(document,{childList:!0,subtree:!0});function r(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(n){if(n.ep)return;n.ep=!0;const i=r(n);fetch(n.href,i)}})();const Jn="modulepreload",Zn=function(e,t){return new URL(e,t).href},vt={},Te=function(t,r,o){let n=Promise.resolve();if(r&&r.length>0){const i=document.getElementsByTagName("link"),s=document.querySelector("meta[property=csp-nonce]"),a=s?.nonce||s?.getAttribute("nonce");n=Promise.all(r.map(u=>{if(u=Zn(u,o),u in vt)return;vt[u]=!0;const l=u.endsWith(".css"),c=l?'[rel="stylesheet"]':"";if(!!o)for(let f=i.length-1;f>=0;f--){const p=i[f];if(p.href===u&&(!l||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${u}"]${c}`))return;const d=document.createElement("link");if(d.rel=l?"stylesheet":Jn,l||(d.as="script",d.crossOrigin=""),d.href=u,a&&d.setAttribute("nonce",a),document.head.appendChild(d),l)return new Promise((f,p)=>{d.addEventListener("load",f),d.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${u}`)))})}))}return n.then(()=>t()).catch(i=>{const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=i,window.dispatchEvent(s),!s.defaultPrevented)throw i})};var Ge=new Intl.Collator(0,{numeric:1}).compare;function zt(e,t,r){return e=e.split("."),t=t.split("."),Ge(e[0],t[0])||Ge(e[1],t[1])||(t[2]=t.slice(2).join("."),r=/[.-]/.test(e[2]=e.slice(2).join(".")),r==/[.-]/.test(t[2])?Ge(e[2],t[2]):r?-1:1)}const Yn="host",wt="upload",Qn="login",Kn="config",ei="info",ti="runtime",ri="sleeptime",oi="https://gradio-space-api-fetcher-v2.hf.space/api",qt="This application is currently busy. Please try again. ",Z="Connection errored out. ",re="Could not resolve app config. ",ni="Could not get space status. ",ii="Could not get API info. ",lt="Space metadata could not be loaded. ",si="Invalid URL. A full URL path is required.",ai="Not authorized to access this space. ",Vt="Invalid credentials. Could not login. ",li="Login credentials are required to access this space.",ui="File system access is only available in Node.js environments",Xt="Root URL not found in client config",ci="Error uploading file";function Wt(e,t,r){return t.startsWith("http://")||t.startsWith("https://")?r?e:t:e+t}async function xt(e,t,r){try{return(await(await fetch(`https://huggingface.co/api/spaces/${e}/jwt`,{headers:{Authorization:`Bearer ${t}`,...r?{Cookie:r}:{}}})).json()).token||!1}catch{return!1}}function hi(e){let t={};return e.forEach(({api_name:r,id:o})=>{r&&(t[r]=o)}),t}async function di(e){const t=this.options.hf_token?{Authorization:`Bearer ${this.options.hf_token}`}:{};if(t["Content-Type"]="application/json",typeof window<"u"&&window.gradio_config&&location.origin!=="http://localhost:9876"&&!window.gradio_config.dev_mode){const r=window.gradio_config.root,o=window.gradio_config;let n=Wt(e,o.root,!1);return o.root=n,{...o,path:r}}else if(e){const r=Yt(e,Kn),o=await this.fetch(r,{headers:t,credentials:"include"});if(o?.status===401&&!this.options.auth)throw new Error(li);if(o?.status===401&&this.options.auth)throw new Error(Vt);if(o?.status===200){let n=await o.json();return n.path=n.path??"",n.root=e,n.dependencies?.forEach((i,s)=>{i.id===void 0&&(i.id=s)}),n}else if(o?.status===401)throw new Error(ai);throw new Error(re)}throw new Error(re)}async function pi(){const{http_protocol:e,host:t}=await Be(this.app_reference,this.options.hf_token);try{if(this.options.auth){const r=await Jt(e,t,this.options.auth,this.fetch,this.options.hf_token);r&&this.set_cookies(r)}}catch(r){throw Error(r.message)}}async function Jt(e,t,r,o,n){const i=new FormData;i.append("username",r?.[0]),i.append("password",r?.[1]);let s={};n&&(s.Authorization=`Bearer ${n}`);const a=await o(`${e}//${t}/${Qn}`,{headers:s,method:"POST",body:i,credentials:"include"});if(a.status===200)return a.headers.get("set-cookie");throw a.status===401?new Error(Vt):new Error(lt)}function Fe(e){if(e.startsWith("http")){const{protocol:t,host:r,pathname:o}=new URL(e);return r.endsWith("hf.space")?{ws_protocol:"wss",host:r,http_protocol:t}:{ws_protocol:t==="https:"?"wss":"ws",http_protocol:t,host:r+(o!=="/"?o:"")}}else if(e.startsWith("file:"))return{ws_protocol:"ws",http_protocol:"http:",host:"lite.local"};return{ws_protocol:"wss",http_protocol:"https:",host:e}}const Zt=e=>{let t=[];return e.split(/,(?=\s*[^\s=;]+=[^\s=;]+)/).forEach(o=>{const[n,i]=o.split(";")[0].split("=");n&&i&&t.push(`${n.trim()}=${i.trim()}`)}),t},ut=/^[a-zA-Z0-9_\-\.]+\/[a-zA-Z0-9_\-\.]+$/,fi=/.*hf\.space\/{0,1}$/;async function Be(e,t){const r={};t&&(r.Authorization=`Bearer ${t}`);const o=e.trim().replace(/\/$/,"");if(ut.test(o))try{const i=(await(await fetch(`https://huggingface.co/api/spaces/${o}/${Yn}`,{headers:r})).json()).host;return{space_id:e,...Fe(i)}}catch{throw new Error(lt)}if(fi.test(o)){const{ws_protocol:n,http_protocol:i,host:s}=Fe(o);return{space_id:s.replace(".hf.space",""),ws_protocol:n,http_protocol:i,host:s}}return{space_id:!1,...Fe(o)}}const Yt=(...e)=>{try{return e.reduce((t,r)=>(t=t.replace(/\/+$/,""),r=r.replace(/^\/+/,""),new URL(r,t+"/").toString()))}catch{throw new Error(si)}};function _i(e,t,r){const o={named_endpoints:{},unnamed_endpoints:{}};return Object.keys(e).forEach(n=>{(n==="named_endpoints"||n==="unnamed_endpoints")&&(o[n]={},Object.entries(e[n]).forEach(([i,{parameters:s,returns:a}])=>{const u=t.dependencies.find(h=>h.api_name===i||h.api_name===i.replace("/",""))?.id||r[i.replace("/","")]||-1,l=u!==-1?t.dependencies.find(h=>h.id==u)?.types:{generator:!1,cancel:!1};if(u!==-1&&t.dependencies.find(h=>h.id==u)?.inputs?.length!==s.length){const h=t.dependencies.find(d=>d.id==u).inputs.map(d=>t.components.find(f=>f.id===d)?.type);try{h.forEach((d,f)=>{if(d==="state"){const p={component:"state",example:null,parameter_default:null,parameter_has_default:!0,parameter_name:null,hidden:!0};s.splice(f,0,p)}})}catch(d){console.error(d)}}const c=(h,d,f,p)=>({...h,description:gi(h?.type,f),type:mi(h?.type,d,f,p)||""});o[n][i]={parameters:s.map(h=>c(h,h?.component,h?.serializer,"parameter")),returns:a.map(h=>c(h,h?.component,h?.serializer,"return")),type:l}}))}),o}function mi(e,t,r,o){switch(e?.type){case"string":return"string";case"boolean":return"boolean";case"number":return"number"}if(r==="JSONSerializable"||r==="StringSerializable")return"any";if(r==="ListStringSerializable")return"string[]";if(t==="Image")return o==="parameter"?"Blob | File | Buffer":"string";if(r==="FileSerializable")return e?.type==="array"?o==="parameter"?"(Blob | File | Buffer)[]":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}[]":o==="parameter"?"Blob | File | Buffer":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}";if(r==="GallerySerializable")return o==="parameter"?"[(Blob | File | Buffer), (string | null)][]":"[{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}, (string | null))][]"}function gi(e,t){return t==="GallerySerializable"?"array of [file, label] tuples":t==="ListStringSerializable"?"array of strings":t==="FileSerializable"?"array of files or single file":e?.description}function ze(e,t){switch(e.msg){case"send_data":return{type:"data"};case"send_hash":return{type:"hash"};case"queue_full":return{type:"update",status:{queue:!0,message:qt,stage:"error",code:e.code,success:e.success}};case"heartbeat":return{type:"heartbeat"};case"unexpected_error":return{type:"unexpected_error",status:{queue:!0,message:e.message,stage:"error",success:!1}};case"estimation":return{type:"update",status:{queue:!0,stage:t||"pending",code:e.code,size:e.queue_size,position:e.rank,eta:e.rank_eta,success:e.success}};case"progress":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,progress_data:e.progress_data,success:e.success}};case"log":return{type:"log",data:e};case"process_generating":return{type:"generating",status:{queue:!0,message:e.success?null:e.output.error,stage:e.success?"generating":"error",code:e.code,progress_data:e.progress_data,eta:e.average_duration},data:e.success?e.output:null};case"process_completed":return"error"in e.output?{type:"update",status:{queue:!0,message:e.output.error,visible:e.output.visible,duration:e.output.duration,stage:"error",code:e.code,success:e.success}}:{type:"complete",status:{queue:!0,message:e.success?void 0:e.output.error,stage:e.success?"complete":"error",code:e.code,progress_data:e.progress_data,changed_state_ids:e.success?e.output.changed_state_ids:void 0},data:e.success?e.output:null};case"process_starts":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,size:e.rank,position:0,success:e.success,eta:e.eta}}}return{type:"none",status:{stage:"error",queue:!0}}}const bi=(e=[],t)=>{const r=t?t.parameters:[];if(Array.isArray(e))return e.length>r.length&&console.warn("Too many arguments provided for the endpoint."),e;const o=[],n=Object.keys(e);return r.forEach((i,s)=>{if(e.hasOwnProperty(i.parameter_name))o[s]=e[i.parameter_name];else if(i.parameter_has_default)o[s]=i.parameter_default;else throw new Error(`No value provided for required parameter: ${i.parameter_name}`)}),n.forEach(i=>{if(!r.some(s=>s.parameter_name===i))throw new Error(`Parameter \`${i}\` is not a valid keyword argument. Please refer to the API for usage.`)}),o.forEach((i,s)=>{if(i===void 0&&!r[s].parameter_has_default)throw new Error(`No value provided for required parameter: ${r[s].parameter_name}`)}),o};async function yi(){if(this.api_info)return this.api_info;const{hf_token:e}=this.options,{config:t}=this,r={"Content-Type":"application/json"};if(e&&(r.Authorization=`Bearer ${e}`),!!t)try{let o,n;if(typeof window<"u"&&window.gradio_api_info)n=window.gradio_api_info;else{if(zt(t?.version||"2.0.0","3.30")<0)o=await this.fetch(oi,{method:"POST",body:JSON.stringify({serialize:!1,config:JSON.stringify(t)}),headers:r,credentials:"include"});else{const i=Yt(t.root,ei);o=await this.fetch(i,{headers:r,credentials:"include"})}if(!o.ok)throw new Error(Z);n=await o.json()}return"api"in n&&(n=n.api),n.named_endpoints["/predict"]&&!n.unnamed_endpoints[0]&&(n.unnamed_endpoints[0]=n.named_endpoints["/predict"]),_i(n,t,this.api_map)}catch(o){""+o.message}}async function vi(e,t,r){const o={};this?.options?.hf_token&&(o.Authorization=`Bearer ${this.options.hf_token}`);const n=1e3,i=[];let s;for(let a=0;a<t.length;a+=n){const u=t.slice(a,a+n),l=new FormData;u.forEach(h=>{l.append("files",h)});try{const h=r?`${e}/${wt}?upload_id=${r}`:`${e}/${wt}`;s=await this.fetch(h,{method:"POST",body:l,headers:o,credentials:"include"})}catch(h){throw new Error(Z+h.message)}if(!s.ok){const h=await s.text();return{error:`HTTP ${s.status}: ${h}`}}const c=await s.json();c&&i.push(...c)}return{files:i}}async function wi(e,t,r,o){let n=(Array.isArray(e)?e:[e]).map(s=>s.blob);const i=n.filter(s=>s.size>(o??1/0));if(i.length)throw new Error(`File size exceeds the maximum allowed size of ${o} bytes: ${i.map(s=>s.name).join(", ")}`);return await Promise.all(await this.upload_files(t,n,r).then(async s=>{if(s.error)throw new Error(s.error);return s.files?s.files.map((a,u)=>new Ne({...e[u],path:a,url:t+"/file="+a})):[]}))}async function Fl(e,t){return e.map(r=>new Ne({path:r.name,orig_name:r.name,blob:r,size:r.size,mime_type:r.type,is_stream:t}))}class Ne{path;url;orig_name;size;blob;is_stream;mime_type;alt_text;meta={_type:"gradio.FileData"};constructor({path:t,url:r,orig_name:o,size:n,blob:i,is_stream:s,mime_type:a,alt_text:u}){this.path=t,this.url=r,this.orig_name=o,this.size=n,this.blob=r?void 0:i,this.is_stream=s,this.mime_type=a,this.alt_text=u}}class xi{type;command;meta;fileData;constructor(t,r){this.type="command",this.command=t,this.meta=r}}typeof process<"u"&&process.versions&&process.versions.node;function Et(e,t,r){for(;r.length>1;){const n=r.shift();if(typeof n=="string"||typeof n=="number")e=e[n];else throw new Error("Invalid key type")}const o=r.shift();if(typeof o=="string"||typeof o=="number")e[o]=t;else throw new Error("Invalid key type")}async function Ye(e,t=void 0,r=[],o=!1,n=void 0){if(Array.isArray(e)){let i=[];return await Promise.all(e.map(async(s,a)=>{let u=r.slice();u.push(String(a));const l=await Ye(e[a],o?n?.parameters[a]?.component||void 0:t,u,!1,n);i=i.concat(l)})),i}else{if(globalThis.Buffer&&e instanceof globalThis.Buffer||e instanceof Blob)return[{path:r,blob:new Blob([e]),type:t}];if(typeof e=="object"&&e!==null){let i=[];for(const s of Object.keys(e)){const a=[...r,s],u=e[s];i=i.concat(await Ye(u,void 0,a,!1,n))}return i}}return[]}function Ei(e,t){let r=t?.dependencies?.find(o=>o.id==e)?.queue;return r!=null?!r:!t.enable_queue}function Si(e,t){return new Promise((r,o)=>{const n=new MessageChannel;n.port1.onmessage=({data:i})=>{n.port1.close(),r(i)},window.parent.postMessage(e,t,[n.port2])})}function fe(e,t,r,o,n=!1){if(o==="input"&&!n)throw new Error("Invalid code path. Cannot skip state inputs for input.");if(o==="output"&&n)return e;let i=[],s=0;const a=o==="input"?t.inputs:t.outputs;for(let u=0;u<a.length;u++){const l=a[u];if(r.find(h=>h.id===l)?.type==="state"){if(n)if(e.length===a.length){const h=e[s];i.push(h),s++}else i.push(null);else{s++;continue}continue}else{const h=e[s];i.push(h),s++}}return i}async function Ti(e,t,r){const o=this;await Oi(o,t);const n=await Ye(t,void 0,[],!0,r);return(await Promise.all(n.map(async({path:s,blob:a,type:u})=>{if(!a)return{path:s,type:u};const l=await o.upload_files(e,[a]),c=l.files&&l.files[0];return{path:s,file_url:c,type:u,name:typeof File<"u"&&a instanceof File?a?.name:void 0}}))).forEach(({path:s,file_url:a,type:u,name:l})=>{if(u==="Gallery")Et(t,a,s);else if(a){const c=new Ne({path:a,orig_name:l});Et(t,c,s)}}),t}async function Oi(e,t){if(!(e.config?.root||e.config?.root_url))throw new Error(Xt);await Qt(e,t)}async function Qt(e,t,r=[]){for(const o in t)t[o]instanceof xi?await Pi(e,t,o):typeof t[o]=="object"&&t[o]!==null&&await Qt(e,t[o],[...r,o])}async function Pi(e,t,r){let o=t[r];const n=e.config?.root||e.config?.root_url;if(!n)throw new Error(Xt);try{let i,s;if(typeof process<"u"&&process.versions&&process.versions.node){const c=await Te(()=>import("./__vite-browser-external-D7Ct-6yo.js").then(d=>d._),[],import.meta.url);s=(await Te(()=>import("./__vite-browser-external-D7Ct-6yo.js").then(d=>d._),[],import.meta.url)).resolve(process.cwd(),o.meta.path),i=await c.readFile(s)}else throw new Error(ui);const a=new Blob([i],{type:"application/octet-stream"}),u=await e.upload_files(n,[a]),l=u.files&&u.files[0];if(l){const c=new Ne({path:l,orig_name:o.meta.name||""});t[r]=c}}catch(i){console.error(ci,i)}}async function Ai(e,t,r){const o={"Content-Type":"application/json"};this.options.hf_token&&(o.Authorization=`Bearer ${this.options.hf_token}`);try{var n=await this.fetch(e,{method:"POST",body:JSON.stringify(t),headers:{...o,...r},credentials:"include"})}catch{return[{error:Z},500]}let i,s;try{i=await n.json(),s=n.status}catch(a){i={error:`Could not parse server response: ${a}`},s=500}return[i,s]}async function Ci(e,t={}){let r=!1,o=!1;if(!this.config)throw new Error("Could not resolve app config");if(typeof e=="number")this.config.dependencies.find(n=>n.id==e);else{const n=e.replace(/^\//,"");this.config.dependencies.find(i=>i.id==this.api_map[n])}return new Promise(async(n,i)=>{const s=this.submit(e,t,null,null,!0);let a;for await(const u of s)u.type==="data"&&(o&&n(a),r=!0,a=u),u.type==="status"&&(u.stage==="error"&&i(u),u.stage==="complete"&&(o=!0,r&&n(a)))})}async function ge(e,t,r){let o=t==="subdomain"?`https://huggingface.co/api/spaces/by-subdomain/${e}`:`https://huggingface.co/api/spaces/${e}`,n,i;try{if(n=await fetch(o),i=n.status,i!==200)throw new Error;n=await n.json()}catch{r({status:"error",load_status:"error",message:ni,detail:"NOT_FOUND"});return}if(!n||i!==200)return;const{runtime:{stage:s},id:a}=n;switch(s){case"STOPPED":case"SLEEPING":r({status:"sleeping",load_status:"pending",message:"Space is asleep. Waking it up...",detail:s}),setTimeout(()=>{ge(e,t,r)},1e3);break;case"PAUSED":r({status:"paused",load_status:"error",message:"This space has been paused by the author. If you would like to try this demo, consider duplicating the space.",detail:s,discussions_enabled:await St(a)});break;case"RUNNING":case"RUNNING_BUILDING":r({status:"running",load_status:"complete",message:"Space is running.",detail:s});break;case"BUILDING":r({status:"building",load_status:"pending",message:"Space is building...",detail:s}),setTimeout(()=>{ge(e,t,r)},1e3);break;case"APP_STARTING":r({status:"starting",load_status:"pending",message:"Space is starting...",detail:s}),setTimeout(()=>{ge(e,t,r)},1e3);break;default:r({status:"space_error",load_status:"error",message:"This space is experiencing an issue.",detail:s,discussions_enabled:await St(a)});break}}const Kt=async(e,t)=>{let r=0;const o=12,n=5e3;return new Promise(i=>{ge(e,ut.test(e)?"space_name":"subdomain",s=>{t(s),s.status==="running"||s.status==="error"||s.status==="paused"||s.status==="space_error"?i():(s.status==="sleeping"||s.status==="building")&&(r<o?(r++,setTimeout(()=>{Kt(e,t).then(i)},n)):i())})})},Hi=/^(?=[^]*\b[dD]iscussions{0,1}\b)(?=[^]*\b[dD]isabled\b)[^]*$/;async function St(e){try{const t=await fetch(`https://huggingface.co/api/spaces/${e}/discussions`,{method:"HEAD"}),r=t.headers.get("x-error-message");return!(!t.ok||r&&Hi.test(r))}catch{return!1}}async function Bi(e,t){const r={};t&&(r.Authorization=`Bearer ${t}`);try{const o=await fetch(`https://huggingface.co/api/spaces/${e}/${ti}`,{headers:r});if(o.status!==200)throw new Error("Space hardware could not be obtained.");const{hardware:n}=await o.json();return n.current}catch(o){throw new Error(o.message)}}async function Ni(e,t,r){const o={};r&&(o.Authorization=`Bearer ${r}`);const n={seconds:t};try{const i=await fetch(`https://huggingface.co/api/spaces/${e}/${ri}`,{method:"POST",headers:{"Content-Type":"application/json",...o},body:JSON.stringify(n)});if(i.status!==200)throw new Error("Could not set sleep timeout on duplicated Space. Please visit *ADD HF LINK TO SETTINGS* to set a timeout manually to reduce billing charges.");return await i.json()}catch(i){throw new Error(i.message)}}const Tt=["cpu-basic","cpu-upgrade","cpu-xl","t4-small","t4-medium","a10g-small","a10g-large","a10g-largex2","a10g-largex4","a100-large","zero-a10g","h100","h100x8"];async function Ii(e,t){const{hf_token:r,private:o,hardware:n,timeout:i,auth:s}=t;if(n&&!Tt.includes(n))throw new Error(`Invalid hardware type provided. Valid types are: ${Tt.map(w=>`"${w}"`).join(",")}.`);const{http_protocol:a,host:u}=await Be(e,r);let l=null;if(s){const w=await Jt(a,u,s,fetch);w&&(l=Zt(w))}const c={Authorization:`Bearer ${r}`,"Content-Type":"application/json",...l?{Cookie:l.join("; ")}:{}},h=(await(await fetch("https://huggingface.co/api/whoami-v2",{headers:c})).json()).name,d=e.split("/")[1],f={repository:`${h}/${d}`};o&&(f.private=!0);let p;try{n||(p=await Bi(e,r))}catch(w){throw Error(lt+w.message)}const v=n||p||"cpu-basic";f.hardware=v;try{const w=await fetch(`https://huggingface.co/api/spaces/${e}/duplicate`,{method:"POST",headers:c,body:JSON.stringify(f)});if(w.status===409)try{return await Pe.connect(`${h}/${d}`,t)}catch(T){throw console.error("Failed to connect Client instance:",T),T}else if(w.status!==200)throw new Error(w.statusText);const L=await w.json();return await Ni(`${h}/${d}`,i||300,r),await Pe.connect(Li(L.url),t)}catch(w){throw new Error(w)}}function Li(e){const t=/https:\/\/huggingface.co\/spaces\/([^/]+\/[^/]+)/,r=e.match(t);if(r)return r[1]}class Ri extends TransformStream{#e="";constructor(t={allowCR:!1}){super({transform:(r,o)=>{for(r=this.#e+r;;){const n=r.indexOf(`
`),i=t.allowCR?r.indexOf("\r"):-1;if(i!==-1&&i!==r.length-1&&(n===-1||n-1>i)){o.enqueue(r.slice(0,i)),r=r.slice(i+1);continue}if(n===-1)break;const s=r[n-1]==="\r"?n-1:n;o.enqueue(r.slice(0,s)),r=r.slice(n+1)}this.#e=r},flush:r=>{if(this.#e==="")return;const o=t.allowCR&&this.#e.endsWith("\r")?this.#e.slice(0,-1):this.#e;r.enqueue(o)}})}}function ki(e){let t=new TextDecoderStream,r=new Ri({allowCR:!0});return e.pipeThrough(t).pipeThrough(r)}function Mi(e){let r=/[:]\s*/.exec(e),o=r&&r.index;if(o)return[e.substring(0,o),e.substring(o+r[0].length)]}function Ot(e,t,r){e.get(t)||e.set(t,r)}async function*Di(e,t){if(!e.body)return;let r=ki(e.body),o,n=r.getReader(),i;for(;;){if(t&&t.aborted)return n.cancel();if(o=await n.read(),o.done)return;if(!o.value){i&&(yield i),i=void 0;continue}let[s,a]=Mi(o.value)||[];s&&(s==="data"?(i||={},i[s]=i[s]?i[s]+`
`+a:a):s==="event"?(i||={},i[s]=a):s==="id"?(i||={},i[s]=+a||a):s==="retry"&&(i||={},i[s]=+a||void 0))}}async function ji(e,t){let r=new Request(e,t);Ot(r.headers,"Accept","text/event-stream"),Ot(r.headers,"Content-Type","application/json");let o=await fetch(r);if(!o.ok)throw o;return Di(o,r.signal)}async function $i(){let{event_callbacks:e,unclosed_events:t,pending_stream_messages:r,stream_status:o,config:n,jwt:i}=this;const s=this;if(!n)throw new Error("Could not resolve app config");o.open=!0;let a=null,u=new URLSearchParams({session_hash:this.session_hash}).toString(),l=new URL(`${n.root}/queue/data?${u}`);if(i&&l.searchParams.set("__sign",i),a=this.stream(l),!a){console.warn("Cannot connect to SSE endpoint: "+l.toString());return}a.onmessage=async function(c){let h=JSON.parse(c.data);if(h.msg==="close_stream"){Oe(o,s.abort_controller);return}const d=h.event_id;if(!d)await Promise.all(Object.keys(e).map(f=>e[f](h)));else if(e[d]&&n){h.msg==="process_completed"&&["sse","sse_v1","sse_v2","sse_v2.1","sse_v3"].includes(n.protocol)&&t.delete(d);let f=e[d];typeof window<"u"&&typeof document<"u"?setTimeout(f,0,h):f(h)}else r[d]||(r[d]=[]),r[d].push(h)},a.onerror=async function(){await Promise.all(Object.keys(e).map(c=>e[c]({msg:"unexpected_error",message:Z})))}}function Oe(e,t){e&&(e.open=!1,t?.abort())}function Ui(e,t,r){!e[t]?(e[t]=[],r.data.forEach((n,i)=>{e[t][i]=n})):r.data.forEach((n,i)=>{let s=Gi(e[t][i],n);e[t][i]=s,r.data[i]=s})}function Gi(e,t){return t.forEach(([r,o,n])=>{e=Fi(e,o,r,n)}),e}function Fi(e,t,r,o){if(t.length===0){if(r==="replace")return o;if(r==="append")return e+o;throw new Error(`Unsupported action: ${r}`)}let n=e;for(let s=0;s<t.length-1;s++)n=n[t[s]];const i=t[t.length-1];switch(r){case"replace":n[i]=o;break;case"append":n[i]+=o;break;case"add":Array.isArray(n)?n.splice(Number(i),0,o):n[i]=o;break;case"delete":Array.isArray(n)?n.splice(Number(i),1):delete n[i];break;default:throw new Error(`Unknown action: ${r}`)}return e}function zi(e,t={}){const r={close:()=>{console.warn("Method not implemented.")},onerror:null,onmessage:null,onopen:null,readyState:0,url:e.toString(),withCredentials:!1,CONNECTING:0,OPEN:1,CLOSED:2,addEventListener:()=>{throw new Error("Method not implemented.")},dispatchEvent:()=>{throw new Error("Method not implemented.")},removeEventListener:()=>{throw new Error("Method not implemented.")}};return ji(e,t).then(async o=>{r.readyState=r.OPEN;try{for await(const n of o)r.onmessage&&r.onmessage(n);r.readyState=r.CLOSED}catch(n){r.onerror&&r.onerror(n),r.readyState=r.CLOSED}}).catch(o=>{console.error(o),r.onerror&&r.onerror(o),r.readyState=r.CLOSED}),r}function qi(e,t={},r,o,n){try{let i=function(x){(n||Vn[x.type])&&l(x)},s=function(){for(De=!0;pe.length>0;)pe.shift()({value:void 0,done:!0})},a=function(x){De||(pe.length>0?pe.shift()(x):je.push(x))},u=function(x){a(Vi(x)),s()},l=function(x){a({value:x,done:!1})},c=function(){return je.length>0?Promise.resolve(je.shift()):De?Promise.resolve({value:void 0,done:!0}):new Promise(x=>pe.push(x))};const{hf_token:h}=this.options,{fetch:d,app_reference:f,config:p,session_hash:v,api_info:w,api_map:L,stream_status:T,pending_stream_messages:A,pending_diff_streams:U,event_callbacks:R,unclosed_events:Y,post_data:Q,options:he}=this,ft=this;if(!w)throw new Error("No API found");if(!p)throw new Error("Could not resolve app config");let{fn_index:_,endpoint_info:_t,dependency:K}=Xi(w,e,L,p),qn=bi(t,_t),B,ee,G=p.protocol??"ws";const g=typeof e=="number"?"/predict":e;let de,O=null,N=!1,Me={},W=typeof window<"u"&&typeof document<"u"?new URLSearchParams(window.location.search).toString():"";const Vn=he?.events?.reduce((x,F)=>(x[F]=!0,x),{})||{};async function Xn(){const x={stage:"complete",queue:!1,time:new Date};N=x,i({...x,type:"status",endpoint:g,fn_index:_});let F={},J={};G==="ws"?(B&&B.readyState===0?B.addEventListener("open",()=>{B.close()}):B.close(),F={fn_index:_,session_hash:v}):(Oe(T,ft.abort_controller),s(),F={event_id:O},J={event_id:O,session_hash:v,fn_index:_});try{if(!p)throw new Error("Could not resolve app config");"event_id"in J&&await d(`${p.root}/cancel`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(J)}),await d(`${p.root}/reset`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(F)})}catch{console.warn("The `/reset` endpoint could not be called. Subsequent endpoint results may be unreliable.")}}const Wn=async x=>{await this._resolve_hearbeat(x)};async function mt(x){if(!p)return;let F=x.render_id;p.components=[...p.components.filter(C=>C.props.rendered_in!==F),...x.components],p.dependencies=[...p.dependencies.filter(C=>C.rendered_in!==F),...x.dependencies];const J=p.components.some(C=>C.type==="state"),S=p.dependencies.some(C=>C.targets.some(k=>k[1]==="unload"));p.connect_heartbeat=J||S,await Wn(p),i({type:"render",data:x,endpoint:g,fn_index:_})}this.handle_blob(p.root,qn,_t).then(async x=>{if(de={data:fe(x,K,p.components,"input",!0)||[],event_data:r,fn_index:_,trigger_id:o},Ei(_,p))i({type:"status",endpoint:g,stage:"pending",queue:!1,fn_index:_,time:new Date}),Q(`${p.root}/run${g.startsWith("/")?g:`/${g}`}${W?"?"+W:""}`,{...de,session_hash:v}).then(([S,C])=>{const k=S.data;C==200?(i({type:"data",endpoint:g,fn_index:_,data:fe(k,K,p.components,"output",he.with_null_state),time:new Date,event_data:r,trigger_id:o}),S.render_config&&mt(S.render_config),i({type:"status",endpoint:g,fn_index:_,stage:"complete",eta:S.average_duration,queue:!1,time:new Date})):i({type:"status",stage:"error",endpoint:g,fn_index:_,message:S.error,queue:!1,time:new Date})}).catch(S=>{i({type:"status",stage:"error",message:S.message,endpoint:g,fn_index:_,queue:!1,time:new Date})});else if(G=="ws"){const{ws_protocol:S,host:C}=await Be(f,h);i({type:"status",stage:"pending",queue:!0,endpoint:g,fn_index:_,time:new Date});let k=new URL(`${S}://${Wt(C,p.path,!0)}/queue/join${W?"?"+W:""}`);this.jwt&&k.searchParams.set("__sign",this.jwt),B=new WebSocket(k),B.onclose=M=>{M.wasClean||i({type:"status",stage:"error",broken:!0,message:Z,queue:!0,endpoint:g,fn_index:_,time:new Date})},B.onmessage=function(M){const D=JSON.parse(M.data),{type:H,status:z,data:I}=ze(D,Me[_]);if(H==="update"&&z&&!N)i({type:"status",endpoint:g,fn_index:_,time:new Date,...z}),z.stage==="error"&&B.close();else if(H==="hash"){B.send(JSON.stringify({fn_index:_,session_hash:v}));return}else H==="data"?B.send(JSON.stringify({...de,session_hash:v})):H==="complete"?N=z:H==="log"?i({type:"log",log:I.log,level:I.level,endpoint:g,duration:I.duration,visible:I.visible,fn_index:_}):H==="generating"&&i({type:"status",time:new Date,...z,stage:z?.stage,queue:!0,endpoint:g,fn_index:_});I&&(i({type:"data",time:new Date,data:fe(I.data,K,p.components,"output",he.with_null_state),endpoint:g,fn_index:_,event_data:r,trigger_id:o}),N&&(i({type:"status",time:new Date,...N,stage:z?.stage,queue:!0,endpoint:g,fn_index:_}),B.close()))},zt(p.version||"2.0.0","3.6")<0&&addEventListener("open",()=>B.send(JSON.stringify({hash:v})))}else if(G=="sse"){i({type:"status",stage:"pending",queue:!0,endpoint:g,fn_index:_,time:new Date});var J=new URLSearchParams({fn_index:_.toString(),session_hash:v}).toString();let S=new URL(`${p.root}/queue/join?${W?W+"&":""}${J}`);if(this.jwt&&S.searchParams.set("__sign",this.jwt),ee=this.stream(S),!ee)return Promise.reject(new Error("Cannot connect to SSE endpoint: "+S.toString()));ee.onmessage=async function(C){const k=JSON.parse(C.data),{type:M,status:D,data:H}=ze(k,Me[_]);if(M==="update"&&D&&!N)i({type:"status",endpoint:g,fn_index:_,time:new Date,...D}),D.stage==="error"&&(ee?.close(),s());else if(M==="data"){O=k.event_id;let[z,I]=await Q(`${p.root}/queue/data`,{...de,session_hash:v,event_id:O});I!==200&&(i({type:"status",stage:"error",message:Z,queue:!0,endpoint:g,fn_index:_,time:new Date}),ee?.close(),s())}else M==="complete"?N=D:M==="log"?i({type:"log",log:H.log,level:H.level,endpoint:g,duration:H.duration,visible:H.visible,fn_index:_}):M==="generating"&&i({type:"status",time:new Date,...D,stage:D?.stage,queue:!0,endpoint:g,fn_index:_});H&&(i({type:"data",time:new Date,data:fe(H.data,K,p.components,"output",he.with_null_state),endpoint:g,fn_index:_,event_data:r,trigger_id:o}),N&&(i({type:"status",time:new Date,...N,stage:D?.stage,queue:!0,endpoint:g,fn_index:_}),ee?.close(),s()))}}else if(G=="sse_v1"||G=="sse_v2"||G=="sse_v2.1"||G=="sse_v3"){i({type:"status",stage:"pending",queue:!0,endpoint:g,fn_index:_,time:new Date});let S="";typeof window<"u"&&typeof document<"u"&&(S=window?.location?.hostname);const k=S.includes(".dev.")?`https://moon-${S.split(".")[1]}.dev.spaces.huggingface.tech`:"https://huggingface.co",M=typeof window<"u"&&typeof document<"u"&&window.parent!=window,D=K.zerogpu&&p.space_id;(M&&D?Si("zerogpu-headers",k):Promise.resolve(null)).then(I=>Q(`${p.root}/queue/join?${W}`,{...de,session_hash:v},I)).then(async([I,bt])=>{if(bt===503)i({type:"status",stage:"error",message:qt,queue:!0,endpoint:g,fn_index:_,time:new Date});else if(bt!==200)i({type:"status",stage:"error",message:Z,queue:!0,endpoint:g,fn_index:_,time:new Date});else{O=I.event_id;let yt=async function($e){try{const{type:q,status:j,data:$}=ze($e,Me[_]);if(q=="heartbeat")return;if(q==="update"&&j&&!N)i({type:"status",endpoint:g,fn_index:_,time:new Date,...j});else if(q==="complete")N=j;else if(q=="unexpected_error")console.error("Unexpected error",j?.message),i({type:"status",stage:"error",message:j?.message||"An Unexpected Error Occurred!",queue:!0,endpoint:g,fn_index:_,time:new Date});else if(q==="log"){i({type:"log",log:$.log,level:$.level,endpoint:g,duration:$.duration,visible:$.visible,fn_index:_});return}else q==="generating"&&(i({type:"status",time:new Date,...j,stage:j?.stage,queue:!0,endpoint:g,fn_index:_}),$&&["sse_v2","sse_v2.1","sse_v3"].includes(G)&&Ui(U,O,$));$&&(i({type:"data",time:new Date,data:fe($.data,K,p.components,"output",he.with_null_state),endpoint:g,fn_index:_}),$.render_config&&await mt($.render_config),N&&(i({type:"status",time:new Date,...N,stage:j?.stage,queue:!0,endpoint:g,fn_index:_}),s())),(j?.stage==="complete"||j?.stage==="error")&&(R[O]&&delete R[O],O in U&&delete U[O])}catch(q){console.error("Unexpected client exception",q),i({type:"status",stage:"error",message:"An Unexpected Error Occurred!",queue:!0,endpoint:g,fn_index:_,time:new Date}),["sse_v2","sse_v2.1","sse_v3"].includes(G)&&(Oe(T,ft.abort_controller),T.open=!1,s())}};O in A&&(A[O].forEach($e=>yt($e)),delete A[O]),R[O]=yt,Y.add(O),T.open||await this.open_stream()}})}});let De=!1;const je=[],pe=[],gt={[Symbol.asyncIterator]:()=>gt,next:c,throw:async x=>(u(x),c()),return:async()=>(s(),c()),cancel:Xn};return gt}catch(i){throw console.error("Submit function encountered an error:",i),i}}function Vi(e){return{then:(t,r)=>r(e)}}function Xi(e,t,r,o){let n,i,s;if(typeof t=="number")n=t,i=e.unnamed_endpoints[n],s=o.dependencies.find(a=>a.id==t);else{const a=t.replace(/^\//,"");n=r[a],i=e.named_endpoints[t.trim()],s=o.dependencies.find(u=>u.id==r[a])}if(typeof n!="number")throw new Error("There is no endpoint matching that name of fn_index matching that number.");return{fn_index:n,endpoint_info:i,dependency:s}}class Pe{app_reference;options;config;api_info;api_map={};session_hash=Math.random().toString(36).substring(2);jwt=!1;last_status={};cookies=null;stream_status={open:!1};pending_stream_messages={};pending_diff_streams={};event_callbacks={};unclosed_events=new Set;heartbeat_event=null;abort_controller=null;stream_instance=null;fetch(t,r){const o=new Headers(r?.headers||{});return this&&this.cookies&&o.append("Cookie",this.cookies),fetch(t,{...r,headers:o})}stream(t){const r=new Headers;return this&&this.cookies&&r.append("Cookie",this.cookies),this.abort_controller=new AbortController,this.stream_instance=zi(t.toString(),{credentials:"include",headers:r,signal:this.abort_controller.signal}),this.stream_instance}view_api;upload_files;upload;handle_blob;post_data;submit;predict;open_stream;resolve_config;resolve_cookies;constructor(t,r={events:["data"]}){this.app_reference=t,r.events||(r.events=["data"]),this.options=r,this.view_api=yi.bind(this),this.upload_files=vi.bind(this),this.handle_blob=Ti.bind(this),this.post_data=Ai.bind(this),this.submit=qi.bind(this),this.predict=Ci.bind(this),this.open_stream=$i.bind(this),this.resolve_config=di.bind(this),this.resolve_cookies=pi.bind(this),this.upload=wi.bind(this),this.fetch=this.fetch.bind(this),this.handle_space_success=this.handle_space_success.bind(this),this.stream=this.stream.bind(this)}async init(){if((typeof window>"u"||!("WebSocket"in window))&&!global.WebSocket){const t=await Te(()=>import("./browser-Dv62-DLq.js").then(r=>r.b),[],import.meta.url);global.WebSocket=t.WebSocket}try{this.options.auth&&await this.resolve_cookies(),await this._resolve_config().then(({config:t})=>this._resolve_hearbeat(t))}catch(t){throw Error(t)}this.api_info=await this.view_api(),this.api_map=hi(this.config?.dependencies||[])}async _resolve_hearbeat(t){if(t&&(this.config=t,this.config&&this.config.connect_heartbeat&&this.config.space_id&&this.options.hf_token&&(this.jwt=await xt(this.config.space_id,this.options.hf_token,this.cookies))),t.space_id&&this.options.hf_token&&(this.jwt=await xt(t.space_id,this.options.hf_token)),this.config&&this.config.connect_heartbeat){const r=new URL(`${this.config.root}/heartbeat/${this.session_hash}`);this.jwt&&r.searchParams.set("__sign",this.jwt),this.heartbeat_event||(this.heartbeat_event=this.stream(r))}}static async connect(t,r={events:["data"]}){const o=new this(t,r);return await o.init(),o}close(){Oe(this.stream_status,this.abort_controller)}static async duplicate(t,r={events:["data"]}){return Ii(t,r)}async _resolve_config(){const{http_protocol:t,host:r,space_id:o}=await Be(this.app_reference,this.options.hf_token),{status_callback:n}=this.options;o&&n&&await Kt(o,n);let i;try{if(i=await this.resolve_config(`${t}//${r}`),!i)throw new Error(re);return this.config_success(i)}catch(s){if(o&&n)ge(o,ut.test(o)?"space_name":"subdomain",this.handle_space_success);else throw n&&n({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"}),Error(s)}}async config_success(t){if(this.config=t,typeof window<"u"&&typeof document<"u"&&window.location.protocol==="https:"&&(this.config.root=this.config.root.replace("http://","https://")),this.config.auth_required)return this.prepare_return_obj();try{this.api_info=await this.view_api()}catch(r){console.error(ii+r.message)}return this.prepare_return_obj()}async handle_space_success(t){if(!this)throw new Error(re);const{status_callback:r}=this.options;if(r&&r(t),t.status==="running")try{if(this.config=await this._resolve_config(),!this.config)throw new Error(re);return await this.config_success(this.config)}catch(o){throw r&&r({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"}),o}}async component_server(t,r,o){if(!this.config)throw new Error(re);const n={},{hf_token:i}=this.options,{session_hash:s}=this;i&&(n.Authorization=`Bearer ${this.options.hf_token}`);let a,u=this.config.components.find(c=>c.id===t);u?.props?.root_url?a=u.props.root_url:a=this.config.root;let l;if("binary"in o){l=new FormData;for(const c in o.data)c!=="binary"&&l.append(c,o.data[c]);l.set("component_id",t.toString()),l.set("fn_name",r),l.set("session_hash",s)}else l=JSON.stringify({data:o,component_id:t,fn_name:r,session_hash:s}),n["Content-Type"]="application/json";i&&(n.Authorization=`Bearer ${i}`);try{const c=await this.fetch(`${a}/component_server/`,{method:"POST",body:l,headers:n,credentials:"include"});if(!c.ok)throw new Error("Could not connect to component server: "+c.statusText);return await c.json()}catch(c){console.warn(c)}}set_cookies(t){this.cookies=Zt(t).join("; ")}prepare_return_obj(){return{config:this.config,predict:this.predict,submit:this.submit,view_api:this.view_api,component_server:this.component_server}}}function oe(){}const zl=e=>e;function Wi(e){return e()}function Ji(e){e.forEach(Wi)}function Zi(e){return typeof e=="function"}function Yi(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}function er(e,...t){if(e==null){for(const o of t)o(void 0);return oe}const r=e.subscribe(...t);return r.unsubscribe?()=>r.unsubscribe():r}function ql(e){let t;return er(e,r=>t=r)(),t}function Vl(e){const t=typeof e=="string"&&e.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return t?[parseFloat(t[1]),t[2]||"px"]:[e,"px"]}const te=[];function Qi(e,t){return{subscribe:Ie(e,t).subscribe}}function Ie(e,t=oe){let r;const o=new Set;function n(a){if(Yi(e,a)&&(e=a,r)){const u=!te.length;for(const l of o)l[1](),te.push(l,e);if(u){for(let l=0;l<te.length;l+=2)te[l][0](te[l+1]);te.length=0}}}function i(a){n(a(e))}function s(a,u=oe){const l=[a,u];return o.add(l),o.size===1&&(r=t(n,i)||oe),a(e),()=>{o.delete(l),o.size===0&&r&&(r(),r=null)}}return{set:n,update:i,subscribe:s}}function le(e,t,r){const o=!Array.isArray(e),n=o?[e]:e;if(!n.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const i=t.length<2;return Qi(r,(s,a)=>{let u=!1;const l=[];let c=0,h=oe;const d=()=>{if(c)return;h();const p=t(o?l[0]:l,s,a);i?s(p):h=Zi(p)?p:oe},f=n.map((p,v)=>er(p,w=>{l[v]=w,c&=~(1<<v),u&&d()},()=>{c|=1<<v}));return u=!0,d(),function(){Ji(f),h(),u=!1}})}var Xl=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ki(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Wl(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var r=function o(){return this instanceof o?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(o){var n=Object.getOwnPropertyDescriptor(e,o);Object.defineProperty(r,o,n.get?n:{enumerable:!0,get:function(){return e[o]}})}),r}var es=function(t){return ts(t)&&!rs(t)};function ts(e){return!!e&&typeof e=="object"}function rs(e){var t=Object.prototype.toString.call(e);return t==="[object RegExp]"||t==="[object Date]"||is(e)}var os=typeof Symbol=="function"&&Symbol.for,ns=os?Symbol.for("react.element"):60103;function is(e){return e.$$typeof===ns}function ss(e){return Array.isArray(e)?[]:{}}function be(e,t){return t.clone!==!1&&t.isMergeableObject(e)?ne(ss(e),e,t):e}function as(e,t,r){return e.concat(t).map(function(o){return be(o,r)})}function ls(e,t){if(!t.customMerge)return ne;var r=t.customMerge(e);return typeof r=="function"?r:ne}function us(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[]}function Pt(e){return Object.keys(e).concat(us(e))}function tr(e,t){try{return t in e}catch{return!1}}function cs(e,t){return tr(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))}function hs(e,t,r){var o={};return r.isMergeableObject(e)&&Pt(e).forEach(function(n){o[n]=be(e[n],r)}),Pt(t).forEach(function(n){cs(e,n)||(tr(e,n)&&r.isMergeableObject(t[n])?o[n]=ls(n,r)(e[n],t[n],r):o[n]=be(t[n],r))}),o}function ne(e,t,r){r=r||{},r.arrayMerge=r.arrayMerge||as,r.isMergeableObject=r.isMergeableObject||es,r.cloneUnlessOtherwiseSpecified=be;var o=Array.isArray(t),n=Array.isArray(e),i=o===n;return i?o?r.arrayMerge(e,t,r):hs(e,t,r):be(t,r)}ne.all=function(t,r){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce(function(o,n){return ne(o,n,r)},{})};var ds=ne,ps=ds;const fs=Ki(ps);var Qe=function(e,t){return Qe=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,o){r.__proto__=o}||function(r,o){for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(r[n]=o[n])},Qe(e,t)};function Le(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Qe(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var y=function(){return y=Object.assign||function(t){for(var r,o=1,n=arguments.length;o<n;o++){r=arguments[o];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},y.apply(this,arguments)};function _s(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r}function qe(e,t,r){if(r||arguments.length===2)for(var o=0,n=t.length,i;o<n;o++)(i||!(o in t))&&(i||(i=Array.prototype.slice.call(t,0,o)),i[o]=t[o]);return e.concat(i||Array.prototype.slice.call(t))}var m;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(m||(m={}));var E;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(E||(E={}));var ie;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(ie||(ie={}));function At(e){return e.type===E.literal}function ms(e){return e.type===E.argument}function rr(e){return e.type===E.number}function or(e){return e.type===E.date}function nr(e){return e.type===E.time}function ir(e){return e.type===E.select}function sr(e){return e.type===E.plural}function gs(e){return e.type===E.pound}function ar(e){return e.type===E.tag}function lr(e){return!!(e&&typeof e=="object"&&e.type===ie.number)}function Ke(e){return!!(e&&typeof e=="object"&&e.type===ie.dateTime)}var ur=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,bs=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function ys(e){var t={};return e.replace(bs,function(r){var o=r.length;switch(r[0]){case"G":t.era=o===4?"long":o===5?"narrow":"short";break;case"y":t.year=o===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][o-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][o-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=o===4?"long":o===5?"narrow":"short";break;case"e":if(o<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][o-4];break;case"c":if(o<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][o-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][o-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][o-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][o-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][o-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][o-1];break;case"s":t.second=["numeric","2-digit"][o-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=o<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),t}var vs=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function ws(e){if(e.length===0)throw new Error("Number skeleton cannot be empty");for(var t=e.split(vs).filter(function(d){return d.length>0}),r=[],o=0,n=t;o<n.length;o++){var i=n[o],s=i.split("/");if(s.length===0)throw new Error("Invalid number skeleton");for(var a=s[0],u=s.slice(1),l=0,c=u;l<c.length;l++){var h=c[l];if(h.length===0)throw new Error("Invalid number skeleton")}r.push({stem:a,options:u})}return r}function xs(e){return e.replace(/^(.*?)-/,"")}var Ct=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,cr=/^(@+)?(\+|#+)?[rs]?$/g,Es=/(\*)(0+)|(#+)(0+)|(0+)/g,hr=/^(0+)$/;function Ht(e){var t={};return e[e.length-1]==="r"?t.roundingPriority="morePrecision":e[e.length-1]==="s"&&(t.roundingPriority="lessPrecision"),e.replace(cr,function(r,o,n){return typeof n!="string"?(t.minimumSignificantDigits=o.length,t.maximumSignificantDigits=o.length):n==="+"?t.minimumSignificantDigits=o.length:o[0]==="#"?t.maximumSignificantDigits=o.length:(t.minimumSignificantDigits=o.length,t.maximumSignificantDigits=o.length+(typeof n=="string"?n.length:0)),""}),t}function dr(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function Ss(e){var t;if(e[0]==="E"&&e[1]==="E"?(t={notation:"engineering"},e=e.slice(2)):e[0]==="E"&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if(r==="+!"?(t.signDisplay="always",e=e.slice(2)):r==="+?"&&(t.signDisplay="exceptZero",e=e.slice(2)),!hr.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function Bt(e){var t={},r=dr(e);return r||t}function Ts(e){for(var t={},r=0,o=e;r<o.length;r++){var n=o[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=xs(n.options[0]);continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=y(y(y({},t),{notation:"scientific"}),n.options.reduce(function(u,l){return y(y({},u),Bt(l))},{}));continue;case"engineering":t=y(y(y({},t),{notation:"engineering"}),n.options.reduce(function(u,l){return y(y({},u),Bt(l))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");n.options[0].replace(Es,function(u,l,c,h,d,f){if(l)t.minimumIntegerDigits=c.length;else{if(h&&d)throw new Error("We currently do not support maximum integer digits");if(f)throw new Error("We currently do not support exact integer digits")}return""});continue}if(hr.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(Ct.test(n.stem)){if(n.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(Ct,function(u,l,c,h,d,f){return c==="*"?t.minimumFractionDigits=l.length:h&&h[0]==="#"?t.maximumFractionDigits=h.length:d&&f?(t.minimumFractionDigits=d.length,t.maximumFractionDigits=d.length+f.length):(t.minimumFractionDigits=l.length,t.maximumFractionDigits=l.length),""});var i=n.options[0];i==="w"?t=y(y({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=y(y({},t),Ht(i)));continue}if(cr.test(n.stem)){t=y(y({},t),Ht(n.stem));continue}var s=dr(n.stem);s&&(t=y(y({},t),s));var a=Ss(n.stem);a&&(t=y(y({},t),a))}return t}var xe={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function Os(e,t){for(var r="",o=0;o<e.length;o++){var n=e.charAt(o);if(n==="j"){for(var i=0;o+1<e.length&&e.charAt(o+1)===n;)i++,o++;var s=1+(i&1),a=i<2?1:3+(i>>1),u="a",l=Ps(t);for((l=="H"||l=="k")&&(a=0);a-- >0;)r+=u;for(;s-- >0;)r=l+r}else n==="J"?r+="H":r+=n}return r}function Ps(e){var t=e.hourCycle;if(t===void 0&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var r=e.language,o;r!=="root"&&(o=e.maximize().region);var n=xe[o||""]||xe[r||""]||xe["".concat(r,"-001")]||xe["001"];return n[0]}var Ve,As=new RegExp("^".concat(ur.source,"*")),Cs=new RegExp("".concat(ur.source,"*$"));function b(e,t){return{start:e,end:t}}var Hs=!!String.prototype.startsWith&&"_a".startsWith("a",1),Bs=!!String.fromCodePoint,Ns=!!Object.fromEntries,Is=!!String.prototype.codePointAt,Ls=!!String.prototype.trimStart,Rs=!!String.prototype.trimEnd,ks=!!Number.isSafeInteger,Ms=ks?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},et=!0;try{var Ds=fr("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");et=((Ve=Ds.exec("a"))===null||Ve===void 0?void 0:Ve[0])==="a"}catch{et=!1}var Nt=Hs?function(t,r,o){return t.startsWith(r,o)}:function(t,r,o){return t.slice(o,o+r.length)===r},tt=Bs?String.fromCodePoint:function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var o="",n=t.length,i=0,s;n>i;){if(s=t[i++],s>1114111)throw RangeError(s+" is not a valid code point");o+=s<65536?String.fromCharCode(s):String.fromCharCode(((s-=65536)>>10)+55296,s%1024+56320)}return o},It=Ns?Object.fromEntries:function(t){for(var r={},o=0,n=t;o<n.length;o++){var i=n[o],s=i[0],a=i[1];r[s]=a}return r},pr=Is?function(t,r){return t.codePointAt(r)}:function(t,r){var o=t.length;if(!(r<0||r>=o)){var n=t.charCodeAt(r),i;return n<55296||n>56319||r+1===o||(i=t.charCodeAt(r+1))<56320||i>57343?n:(n-55296<<10)+(i-56320)+65536}},js=Ls?function(t){return t.trimStart()}:function(t){return t.replace(As,"")},$s=Rs?function(t){return t.trimEnd()}:function(t){return t.replace(Cs,"")};function fr(e,t){return new RegExp(e,t)}var rt;if(et){var Lt=fr("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");rt=function(t,r){var o;Lt.lastIndex=r;var n=Lt.exec(t);return(o=n[1])!==null&&o!==void 0?o:""}}else rt=function(t,r){for(var o=[];;){var n=pr(t,r);if(n===void 0||_r(n)||zs(n))break;o.push(n),r+=n>=65536?2:1}return tt.apply(void 0,o)};var Us=function(){function e(t,r){r===void 0&&(r={}),this.message=t,this.position={offset:0,line:1,column:1},this.ignoreTag=!!r.ignoreTag,this.locale=r.locale,this.requiresOtherClause=!!r.requiresOtherClause,this.shouldParseSkeletons=!!r.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(t,r,o){for(var n=[];!this.isEOF();){var i=this.char();if(i===123){var s=this.parseArgument(t,o);if(s.err)return s;n.push(s.val)}else{if(i===125&&t>0)break;if(i===35&&(r==="plural"||r==="selectordinal")){var a=this.clonePosition();this.bump(),n.push({type:E.pound,location:b(a,this.clonePosition())})}else if(i===60&&!this.ignoreTag&&this.peek()===47){if(o)break;return this.error(m.UNMATCHED_CLOSING_TAG,b(this.clonePosition(),this.clonePosition()))}else if(i===60&&!this.ignoreTag&&ot(this.peek()||0)){var s=this.parseTag(t,r);if(s.err)return s;n.push(s.val)}else{var s=this.parseLiteral(t,r);if(s.err)return s;n.push(s.val)}}}return{val:n,err:null}},e.prototype.parseTag=function(t,r){var o=this.clonePosition();this.bump();var n=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:E.literal,value:"<".concat(n,"/>"),location:b(o,this.clonePosition())},err:null};if(this.bumpIf(">")){var i=this.parseMessage(t+1,r,!0);if(i.err)return i;var s=i.val,a=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!ot(this.char()))return this.error(m.INVALID_TAG,b(a,this.clonePosition()));var u=this.clonePosition(),l=this.parseTagName();return n!==l?this.error(m.UNMATCHED_CLOSING_TAG,b(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:E.tag,value:n,children:s,location:b(o,this.clonePosition())},err:null}:this.error(m.INVALID_TAG,b(a,this.clonePosition())))}else return this.error(m.UNCLOSED_TAG,b(o,this.clonePosition()))}else return this.error(m.INVALID_TAG,b(o,this.clonePosition()))},e.prototype.parseTagName=function(){var t=this.offset();for(this.bump();!this.isEOF()&&Fs(this.char());)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(t,r){for(var o=this.clonePosition(),n="";;){var i=this.tryParseQuote(r);if(i){n+=i;continue}var s=this.tryParseUnquoted(t,r);if(s){n+=s;continue}var a=this.tryParseLeftAngleBracket();if(a){n+=a;continue}break}var u=b(o,this.clonePosition());return{val:{type:E.literal,value:n,location:u},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!Gs(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(t){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(t==="plural"||t==="selectordinal")break;return null;default:return null}this.bump();var r=[this.char()];for(this.bump();!this.isEOF();){var o=this.char();if(o===39)if(this.peek()===39)r.push(39),this.bump();else{this.bump();break}else r.push(o);this.bump()}return tt.apply(void 0,r)},e.prototype.tryParseUnquoted=function(t,r){if(this.isEOF())return null;var o=this.char();return o===60||o===123||o===35&&(r==="plural"||r==="selectordinal")||o===125&&t>0?null:(this.bump(),tt(o))},e.prototype.parseArgument=function(t,r){var o=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(m.EXPECT_ARGUMENT_CLOSING_BRACE,b(o,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(m.EMPTY_ARGUMENT,b(o,this.clonePosition()));var n=this.parseIdentifierIfPossible().value;if(!n)return this.error(m.MALFORMED_ARGUMENT,b(o,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(m.EXPECT_ARGUMENT_CLOSING_BRACE,b(o,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:E.argument,value:n,location:b(o,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(m.EXPECT_ARGUMENT_CLOSING_BRACE,b(o,this.clonePosition())):this.parseArgumentOptions(t,r,n,o);default:return this.error(m.MALFORMED_ARGUMENT,b(o,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var t=this.clonePosition(),r=this.offset(),o=rt(this.message,r),n=r+o.length;this.bumpTo(n);var i=this.clonePosition(),s=b(t,i);return{value:o,location:s}},e.prototype.parseArgumentOptions=function(t,r,o,n){var i,s=this.clonePosition(),a=this.parseIdentifierIfPossible().value,u=this.clonePosition();switch(a){case"":return this.error(m.EXPECT_ARGUMENT_TYPE,b(s,u));case"number":case"date":case"time":{this.bumpSpace();var l=null;if(this.bumpIf(",")){this.bumpSpace();var c=this.clonePosition(),h=this.parseSimpleArgStyleIfPossible();if(h.err)return h;var d=$s(h.val);if(d.length===0)return this.error(m.EXPECT_ARGUMENT_STYLE,b(this.clonePosition(),this.clonePosition()));var f=b(c,this.clonePosition());l={style:d,styleLocation:f}}var p=this.tryParseArgumentClose(n);if(p.err)return p;var v=b(n,this.clonePosition());if(l&&Nt(l?.style,"::",0)){var w=js(l.style.slice(2));if(a==="number"){var h=this.parseNumberSkeletonFromString(w,l.styleLocation);return h.err?h:{val:{type:E.number,value:o,location:v,style:h.val},err:null}}else{if(w.length===0)return this.error(m.EXPECT_DATE_TIME_SKELETON,v);var L=w;this.locale&&(L=Os(w,this.locale));var d={type:ie.dateTime,pattern:L,location:l.styleLocation,parsedOptions:this.shouldParseSkeletons?ys(L):{}},T=a==="date"?E.date:E.time;return{val:{type:T,value:o,location:v,style:d},err:null}}}return{val:{type:a==="number"?E.number:a==="date"?E.date:E.time,value:o,location:v,style:(i=l?.style)!==null&&i!==void 0?i:null},err:null}}case"plural":case"selectordinal":case"select":{var A=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(m.EXPECT_SELECT_ARGUMENT_OPTIONS,b(A,y({},A)));this.bumpSpace();var U=this.parseIdentifierIfPossible(),R=0;if(a!=="select"&&U.value==="offset"){if(!this.bumpIf(":"))return this.error(m.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,b(this.clonePosition(),this.clonePosition()));this.bumpSpace();var h=this.tryParseDecimalInteger(m.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,m.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(h.err)return h;this.bumpSpace(),U=this.parseIdentifierIfPossible(),R=h.val}var Y=this.tryParsePluralOrSelectOptions(t,a,r,U);if(Y.err)return Y;var p=this.tryParseArgumentClose(n);if(p.err)return p;var Q=b(n,this.clonePosition());return a==="select"?{val:{type:E.select,value:o,options:It(Y.val),location:Q},err:null}:{val:{type:E.plural,value:o,options:It(Y.val),offset:R,pluralType:a==="plural"?"cardinal":"ordinal",location:Q},err:null}}default:return this.error(m.INVALID_ARGUMENT_TYPE,b(s,u))}},e.prototype.tryParseArgumentClose=function(t){return this.isEOF()||this.char()!==125?this.error(m.EXPECT_ARGUMENT_CLOSING_BRACE,b(t,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var t=0,r=this.clonePosition();!this.isEOF();){var o=this.char();switch(o){case 39:{this.bump();var n=this.clonePosition();if(!this.bumpUntil("'"))return this.error(m.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,b(n,this.clonePosition()));this.bump();break}case 123:{t+=1,this.bump();break}case 125:{if(t>0)t-=1;else return{val:this.message.slice(r.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(r.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(t,r){var o=[];try{o=ws(t)}catch{return this.error(m.INVALID_NUMBER_SKELETON,r)}return{val:{type:ie.number,tokens:o,location:r,parsedOptions:this.shouldParseSkeletons?Ts(o):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(t,r,o,n){for(var i,s=!1,a=[],u=new Set,l=n.value,c=n.location;;){if(l.length===0){var h=this.clonePosition();if(r!=="select"&&this.bumpIf("=")){var d=this.tryParseDecimalInteger(m.EXPECT_PLURAL_ARGUMENT_SELECTOR,m.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;c=b(h,this.clonePosition()),l=this.message.slice(h.offset,this.offset())}else break}if(u.has(l))return this.error(r==="select"?m.DUPLICATE_SELECT_ARGUMENT_SELECTOR:m.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);l==="other"&&(s=!0),this.bumpSpace();var f=this.clonePosition();if(!this.bumpIf("{"))return this.error(r==="select"?m.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:m.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,b(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(t+1,r,o);if(p.err)return p;var v=this.tryParseArgumentClose(f);if(v.err)return v;a.push([l,{value:p.val,location:b(f,this.clonePosition())}]),u.add(l),this.bumpSpace(),i=this.parseIdentifierIfPossible(),l=i.value,c=i.location}return a.length===0?this.error(r==="select"?m.EXPECT_SELECT_ARGUMENT_SELECTOR:m.EXPECT_PLURAL_ARGUMENT_SELECTOR,b(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(m.MISSING_OTHER_CLAUSE,b(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(t,r){var o=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(o=-1);for(var i=!1,s=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)i=!0,s=s*10+(a-48),this.bump();else break}var u=b(n,this.clonePosition());return i?(s*=o,Ms(s)?{val:s,err:null}:this.error(r,u)):this.error(t,u)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var t=this.position.offset;if(t>=this.message.length)throw Error("out of bound");var r=pr(this.message,t);if(r===void 0)throw Error("Offset ".concat(t," is at invalid UTF-16 code unit boundary"));return r},e.prototype.error=function(t,r){return{val:null,err:{kind:t,message:this.message,location:r}}},e.prototype.bump=function(){if(!this.isEOF()){var t=this.char();t===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=t<65536?1:2)}},e.prototype.bumpIf=function(t){if(Nt(this.message,t,this.offset())){for(var r=0;r<t.length;r++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(t){var r=this.offset(),o=this.message.indexOf(t,r);return o>=0?(this.bumpTo(o),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(t){if(this.offset()>t)throw Error("targetOffset ".concat(t," must be greater than or equal to the current offset ").concat(this.offset()));for(t=Math.min(t,this.message.length);;){var r=this.offset();if(r===t)break;if(r>t)throw Error("targetOffset ".concat(t," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&_r(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var t=this.char(),r=this.offset(),o=this.message.charCodeAt(r+(t>=65536?2:1));return o??null},e}();function ot(e){return e>=97&&e<=122||e>=65&&e<=90}function Gs(e){return ot(e)||e===47}function Fs(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function _r(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function zs(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}function nt(e){e.forEach(function(t){if(delete t.location,ir(t)||sr(t))for(var r in t.options)delete t.options[r].location,nt(t.options[r].value);else rr(t)&&lr(t.style)||(or(t)||nr(t))&&Ke(t.style)?delete t.style.location:ar(t)&&nt(t.children)})}function qs(e,t){t===void 0&&(t={}),t=y({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var r=new Us(e,t).parse();if(r.err){var o=SyntaxError(m[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return t?.captureLocation||nt(r.val),r.val}function Xe(e,t){var r=t&&t.cache?t.cache:Ys,o=t&&t.serializer?t.serializer:Zs,n=t&&t.strategy?t.strategy:Xs;return n(e,{cache:r,serializer:o})}function Vs(e){return e==null||typeof e=="number"||typeof e=="boolean"}function mr(e,t,r,o){var n=Vs(o)?o:r(o),i=t.get(n);return typeof i>"u"&&(i=e.call(this,o),t.set(n,i)),i}function gr(e,t,r){var o=Array.prototype.slice.call(arguments,3),n=r(o),i=t.get(n);return typeof i>"u"&&(i=e.apply(this,o),t.set(n,i)),i}function ct(e,t,r,o,n){return r.bind(t,e,o,n)}function Xs(e,t){var r=e.length===1?mr:gr;return ct(e,this,r,t.cache.create(),t.serializer)}function Ws(e,t){return ct(e,this,gr,t.cache.create(),t.serializer)}function Js(e,t){return ct(e,this,mr,t.cache.create(),t.serializer)}var Zs=function(){return JSON.stringify(arguments)};function ht(){this.cache=Object.create(null)}ht.prototype.get=function(e){return this.cache[e]};ht.prototype.set=function(e,t){this.cache[e]=t};var Ys={create:function(){return new ht}},We={variadic:Ws,monadic:Js},se;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(se||(se={}));var Re=function(e){Le(t,e);function t(r,o,n){var i=e.call(this,r)||this;return i.code=o,i.originalMessage=n,i}return t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),Rt=function(e){Le(t,e);function t(r,o,n,i){return e.call(this,'Invalid values for "'.concat(r,'": "').concat(o,'". Options are "').concat(Object.keys(n).join('", "'),'"'),se.INVALID_VALUE,i)||this}return t}(Re),Qs=function(e){Le(t,e);function t(r,o,n){return e.call(this,'Value for "'.concat(r,'" must be of type ').concat(o),se.INVALID_VALUE,n)||this}return t}(Re),Ks=function(e){Le(t,e);function t(r,o){return e.call(this,'The intl string context variable "'.concat(r,'" was not provided to the string "').concat(o,'"'),se.MISSING_VALUE,o)||this}return t}(Re),P;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(P||(P={}));function ea(e){return e.length<2?e:e.reduce(function(t,r){var o=t[t.length-1];return!o||o.type!==P.literal||r.type!==P.literal?t.push(r):o.value+=r.value,t},[])}function ta(e){return typeof e=="function"}function Ee(e,t,r,o,n,i,s){if(e.length===1&&At(e[0]))return[{type:P.literal,value:e[0].value}];for(var a=[],u=0,l=e;u<l.length;u++){var c=l[u];if(At(c)){a.push({type:P.literal,value:c.value});continue}if(gs(c)){typeof i=="number"&&a.push({type:P.literal,value:r.getNumberFormat(t).format(i)});continue}var h=c.value;if(!(n&&h in n))throw new Ks(h,s);var d=n[h];if(ms(c)){(!d||typeof d=="string"||typeof d=="number")&&(d=typeof d=="string"||typeof d=="number"?String(d):""),a.push({type:typeof d=="string"?P.literal:P.object,value:d});continue}if(or(c)){var f=typeof c.style=="string"?o.date[c.style]:Ke(c.style)?c.style.parsedOptions:void 0;a.push({type:P.literal,value:r.getDateTimeFormat(t,f).format(d)});continue}if(nr(c)){var f=typeof c.style=="string"?o.time[c.style]:Ke(c.style)?c.style.parsedOptions:o.time.medium;a.push({type:P.literal,value:r.getDateTimeFormat(t,f).format(d)});continue}if(rr(c)){var f=typeof c.style=="string"?o.number[c.style]:lr(c.style)?c.style.parsedOptions:void 0;f&&f.scale&&(d=d*(f.scale||1)),a.push({type:P.literal,value:r.getNumberFormat(t,f).format(d)});continue}if(ar(c)){var p=c.children,v=c.value,w=n[v];if(!ta(w))throw new Qs(v,"function",s);var L=Ee(p,t,r,o,n,i),T=w(L.map(function(R){return R.value}));Array.isArray(T)||(T=[T]),a.push.apply(a,T.map(function(R){return{type:typeof R=="string"?P.literal:P.object,value:R}}))}if(ir(c)){var A=c.options[d]||c.options.other;if(!A)throw new Rt(c.value,d,Object.keys(c.options),s);a.push.apply(a,Ee(A.value,t,r,o,n));continue}if(sr(c)){var A=c.options["=".concat(d)];if(!A){if(!Intl.PluralRules)throw new Re(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,se.MISSING_INTL_API,s);var U=r.getPluralRules(t,{type:c.pluralType}).select(d-(c.offset||0));A=c.options[U]||c.options.other}if(!A)throw new Rt(c.value,d,Object.keys(c.options),s);a.push.apply(a,Ee(A.value,t,r,o,n,d-(c.offset||0)));continue}}return ea(a)}function ra(e,t){return t?y(y(y({},e||{}),t||{}),Object.keys(e).reduce(function(r,o){return r[o]=y(y({},e[o]),t[o]||{}),r},{})):e}function oa(e,t){return t?Object.keys(e).reduce(function(r,o){return r[o]=ra(e[o],t[o]),r},y({},e)):e}function Je(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}function na(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:Xe(function(){for(var t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return new((t=Intl.NumberFormat).bind.apply(t,qe([void 0],r,!1)))},{cache:Je(e.number),strategy:We.variadic}),getDateTimeFormat:Xe(function(){for(var t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return new((t=Intl.DateTimeFormat).bind.apply(t,qe([void 0],r,!1)))},{cache:Je(e.dateTime),strategy:We.variadic}),getPluralRules:Xe(function(){for(var t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return new((t=Intl.PluralRules).bind.apply(t,qe([void 0],r,!1)))},{cache:Je(e.pluralRules),strategy:We.variadic})}}var br=function(){function e(t,r,o,n){var i=this;if(r===void 0&&(r=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(u){var l=i.formatToParts(u);if(l.length===1)return l[0].value;var c=l.reduce(function(h,d){return!h.length||d.type!==P.literal||typeof h[h.length-1]!="string"?h.push(d.value):h[h.length-1]+=d.value,h},[]);return c.length<=1?c[0]||"":c},this.formatToParts=function(u){return Ee(i.ast,i.locales,i.formatters,i.formats,u,void 0,i.message)},this.resolvedOptions=function(){var u;return{locale:((u=i.resolvedLocale)===null||u===void 0?void 0:u.toString())||Intl.NumberFormat.supportedLocalesOf(i.locales)[0]}},this.getAst=function(){return i.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),typeof t=="string"){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var s=n||{};s.formatters;var a=_s(s,["formatters"]);this.ast=e.__parse(t,y(y({},a),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=oa(e.formats,o),this.formatters=n&&n.formatters||na(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(t){if(!(typeof Intl.Locale>"u")){var r=Intl.NumberFormat.supportedLocalesOf(t);return r.length>0?new Intl.Locale(r[0]):new Intl.Locale(typeof t=="string"?t:t[0])}},e.__parse=qs,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();function ia(e,t){if(t==null)return;if(t in e)return e[t];const r=t.split(".");let o=e;for(let n=0;n<r.length;n++)if(typeof o=="object"){if(n>0){const i=r.slice(n,r.length).join(".");if(i in o){o=o[i];break}}o=o[r[n]]}else o=void 0;return o}const V={},sa=(e,t,r)=>r&&(t in V||(V[t]={}),e in V[t]||(V[t][e]=r),r),yr=(e,t)=>{if(t==null)return;if(t in V&&e in V[t])return V[t][e];const r=we(t);for(let o=0;o<r.length;o++){const n=r[o],i=la(n,e);if(i)return sa(e,t,i)}};let dt;const ve=Ie({});function aa(e){return dt[e]||null}function vr(e){return e in dt}function la(e,t){if(!vr(e))return null;const r=aa(e);return ia(r,t)}function ua(e){if(e==null)return;const t=we(e);for(let r=0;r<t.length;r++){const o=t[r];if(vr(o))return o}}function wr(e,...t){delete V[e],ve.update(r=>(r[e]=fs.all([r[e]||{},...t]),r))}le([ve],([e])=>Object.keys(e));ve.subscribe(e=>dt=e);const Se={};function ca(e,t){Se[e].delete(t),Se[e].size===0&&delete Se[e]}function xr(e){return Se[e]}function ha(e){return we(e).map(t=>{const r=xr(t);return[t,r?[...r]:[]]}).filter(([,t])=>t.length>0)}function Ae(e){return e==null?!1:we(e).some(t=>{var r;return(r=xr(t))==null?void 0:r.size})}function da(e,t){return Promise.all(t.map(o=>(ca(e,o),o().then(n=>n.default||n)))).then(o=>wr(e,...o))}const _e={};function Er(e){if(!Ae(e))return e in _e?_e[e]:Promise.resolve();const t=ha(e);return _e[e]=Promise.all(t.map(([r,o])=>da(r,o))).then(()=>{if(Ae(e))return Er(e);delete _e[e]}),_e[e]}var kt=Object.getOwnPropertySymbols,pa=Object.prototype.hasOwnProperty,fa=Object.prototype.propertyIsEnumerable,_a=(e,t)=>{var r={};for(var o in e)pa.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&kt)for(var o of kt(e))t.indexOf(o)<0&&fa.call(e,o)&&(r[o]=e[o]);return r};const ma={number:{scientific:{notation:"scientific"},engineering:{notation:"engineering"},compactLong:{notation:"compact",compactDisplay:"long"},compactShort:{notation:"compact",compactDisplay:"short"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}};function ga({locale:e,id:t}){console.warn(`[svelte-i18n] The message "${t}" was not found in "${we(e).join('", "')}".${Ae(X())?`

Note: there are at least one loader still registered to this locale that wasn't executed.`:""}`)}const ba={fallbackLocale:null,loadingDelay:200,formats:ma,warnOnMissingMessages:!0,handleMissingMessage:void 0,ignoreTag:!0},me=ba;function ae(){return me}function ya(e){const t=e,{formats:r}=t,o=_a(t,["formats"]);let n=e.fallbackLocale;if(e.initialLocale)try{br.resolveLocale(e.initialLocale)&&(n=e.initialLocale)}catch{console.warn(`[svelte-i18n] The initial locale "${e.initialLocale}" is not a valid locale.`)}return o.warnOnMissingMessages&&(delete o.warnOnMissingMessages,o.handleMissingMessage==null?o.handleMissingMessage=ga:console.warn('[svelte-i18n] The "warnOnMissingMessages" option is deprecated. Please use the "handleMissingMessage" option instead.')),Object.assign(me,o,{initialLocale:n}),r&&("number"in r&&Object.assign(me.formats.number,r.number),"date"in r&&Object.assign(me.formats.date,r.date),"time"in r&&Object.assign(me.formats.time,r.time)),ue.set(n)}const Ze=Ie(!1);var va=Object.defineProperty,wa=Object.defineProperties,xa=Object.getOwnPropertyDescriptors,Mt=Object.getOwnPropertySymbols,Ea=Object.prototype.hasOwnProperty,Sa=Object.prototype.propertyIsEnumerable,Dt=(e,t,r)=>t in e?va(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Ta=(e,t)=>{for(var r in t||(t={}))Ea.call(t,r)&&Dt(e,r,t[r]);if(Mt)for(var r of Mt(t))Sa.call(t,r)&&Dt(e,r,t[r]);return e},Oa=(e,t)=>wa(e,xa(t));let it;const Ce=Ie(null);function jt(e){return e.split("-").map((t,r,o)=>o.slice(0,r+1).join("-")).reverse()}function we(e,t=ae().fallbackLocale){const r=jt(e);return t?[...new Set([...r,...jt(t)])]:r}function X(){return it??void 0}Ce.subscribe(e=>{it=e??void 0,typeof window<"u"&&e!=null&&document.documentElement.setAttribute("lang",e)});const Pa=e=>{if(e&&ua(e)&&Ae(e)){const{loadingDelay:t}=ae();let r;return typeof window<"u"&&X()!=null&&t?r=window.setTimeout(()=>Ze.set(!0),t):Ze.set(!0),Er(e).then(()=>{Ce.set(e)}).finally(()=>{clearTimeout(r),Ze.set(!1)})}return Ce.set(e)},ue=Oa(Ta({},Ce),{set:Pa}),Aa=()=>typeof window>"u"?null:window.navigator.language||window.navigator.languages[0],ke=e=>{const t=Object.create(null);return o=>{const n=JSON.stringify(o);return n in t?t[n]:t[n]=e(o)}};var Ca=Object.defineProperty,He=Object.getOwnPropertySymbols,Sr=Object.prototype.hasOwnProperty,Tr=Object.prototype.propertyIsEnumerable,$t=(e,t,r)=>t in e?Ca(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,pt=(e,t)=>{for(var r in t||(t={}))Sr.call(t,r)&&$t(e,r,t[r]);if(He)for(var r of He(t))Tr.call(t,r)&&$t(e,r,t[r]);return e},ce=(e,t)=>{var r={};for(var o in e)Sr.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&He)for(var o of He(e))t.indexOf(o)<0&&Tr.call(e,o)&&(r[o]=e[o]);return r};const ye=(e,t)=>{const{formats:r}=ae();if(e in r&&t in r[e])return r[e][t];throw new Error(`[svelte-i18n] Unknown "${t}" ${e} format.`)},Ha=ke(e=>{var t=e,{locale:r,format:o}=t,n=ce(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format numbers');return o&&(n=ye("number",o)),new Intl.NumberFormat(r,n)}),Ba=ke(e=>{var t=e,{locale:r,format:o}=t,n=ce(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format dates');return o?n=ye("date",o):Object.keys(n).length===0&&(n=ye("date","short")),new Intl.DateTimeFormat(r,n)}),Na=ke(e=>{var t=e,{locale:r,format:o}=t,n=ce(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format time values');return o?n=ye("time",o):Object.keys(n).length===0&&(n=ye("time","short")),new Intl.DateTimeFormat(r,n)}),Ia=(e={})=>{var t=e,{locale:r=X()}=t,o=ce(t,["locale"]);return Ha(pt({locale:r},o))},La=(e={})=>{var t=e,{locale:r=X()}=t,o=ce(t,["locale"]);return Ba(pt({locale:r},o))},Ra=(e={})=>{var t=e,{locale:r=X()}=t,o=ce(t,["locale"]);return Na(pt({locale:r},o))},ka=ke((e,t=X())=>new br(e,t,ae().formats,{ignoreTag:ae().ignoreTag})),Ma=(e,t={})=>{var r,o,n,i;let s=t;typeof e=="object"&&(s=e,e=s.id);const{values:a,locale:u=X(),default:l}=s;if(u==null)throw new Error("[svelte-i18n] Cannot format a message without first setting the initial locale.");let c=yr(e,u);if(!c)c=(i=(n=(o=(r=ae()).handleMissingMessage)==null?void 0:o.call(r,{locale:u,id:e,defaultValue:l}))!=null?n:l)!=null?i:e;else if(typeof c!="string")return console.warn(`[svelte-i18n] Message with id "${e}" must be of type "string", found: "${typeof c}". Gettin its value through the "$format" method is deprecated; use the "json" method instead.`),c;if(!a)return c;let h=c;try{h=ka(c,u).format(a)}catch(d){d instanceof Error&&console.warn(`[svelte-i18n] Message "${e}" has syntax error:`,d.message)}return h},Da=(e,t)=>Ra(t).format(e),ja=(e,t)=>La(t).format(e),$a=(e,t)=>Ia(t).format(e),Ua=(e,t=X())=>yr(e,t),Jl=le([ue,ve],()=>Ma);le([ue],()=>Da);le([ue],()=>ja);le([ue],()=>$a);le([ue,ve],()=>Ua);let Or=!1;"attachShadow"in Element.prototype&&"adoptedStyleSheets"in Document.prototype&&(Or="adoptedStyleSheets"in document.createElement("div").attachShadow({mode:"open"}));function Ut(e,t){const r=new URL(import.meta.url).origin,o=new URL(e,r).href;if(document.querySelector(`link[href='${o}']`))return Promise.resolve();const i=document.createElement("link");return i.rel="stylesheet",i.href=o,new Promise((s,a)=>{i.addEventListener("load",()=>s()),i.addEventListener("error",()=>{console.error(`Unable to preload CSS for ${o}`),s()}),t.appendChild(i)})}function Zl(e,t,r=document.createElement("style")){if(!Or)return null;r.remove();const o=new CSSStyleSheet;o.replaceSync(e);let n="";e=e.replace(/@import\s+url\((.*?)\);\s*/g,(u,l)=>(n+=`@import url(${l});
`,""));const i=o.cssRules;let s="",a=`gradio-app .gradio-container.gradio-container-${t} .contain `;for(let u=0;u<i.length;u++){const l=i[u];let c=l.cssText.includes(".dark");if(l instanceof CSSStyleRule){const h=l.selectorText;if(h){const d=h.replace(".dark","").split(",").map(f=>`${c?".dark":""} ${a} ${f.trim()} `).join(",");s+=l.cssText,s+=l.cssText.replace(h,d)}}else if(l instanceof CSSMediaRule){let h=`@media ${l.media.mediaText} {`;for(let d=0;d<l.cssRules.length;d++){const f=l.cssRules[d];if(f instanceof CSSStyleRule){let p=f.cssText.includes(".dark ");const v=f.selectorText,w=v.replace(".dark","").split(",").map(L=>`${p?".dark":""} ${a} ${L.trim()} `).join(",");h+=f.cssText.replace(v,w)}}h+="}",s+=h}else if(l instanceof CSSKeyframesRule){s+=`@keyframes ${l.name} {`;for(let h=0;h<l.cssRules.length;h++){const d=l.cssRules[h];d instanceof CSSKeyframeRule&&(s+=`${d.keyText} { ${d.style.cssText} }`)}s+="}"}else l instanceof CSSFontFaceRule&&(s+=`@font-face { ${l.style.cssText} }`)}return s=n+s,r.textContent=s,document.head.appendChild(r),r}const Pr={built_with_gradio:"تم الإنشاء بإستخدام Gradio",clear:"أمسح",or:"أو",submit:"أرسل"},Ar={click_to_upload:"إضغط للتحميل",drop_audio:"أسقط الملف الصوتي هنا",drop_csv:"أسقط ملف البيانات هنا",drop_file:"أسقط الملف هنا",drop_image:"أسقط الصورة هنا",drop_video:"أسقط الفيديو هنا"},Ga={common:Pr,upload_text:Ar},Fa=Object.freeze(Object.defineProperty({__proto__:null,common:Pr,default:Ga,upload_text:Ar},Symbol.toStringTag,{value:"Module"})),Cr={built_with_gradio:"Construït amb gradio",clear:"Neteja",empty:"Buit",error:"Error",loading:"S'està carregant",or:"o",submit:"Envia"},Hr={click_to_upload:"Feu clic per pujar",drop_audio:"Deixeu anar l'àudio aquí",drop_csv:"Deixeu anar el CSV aquí",drop_file:"Deixeu anar el fitxer aquí",drop_image:"Deixeu anar la imatge aquí",drop_video:"Deixeu anar el vídeo aquí"},za={common:Cr,upload_text:Hr},qa=Object.freeze(Object.defineProperty({__proto__:null,common:Cr,default:za,upload_text:Hr},Symbol.toStringTag,{value:"Module"})),Br={annotated_image:"وێنەی نیشانە کراو"},Nr={allow_recording_access:"تکایە ڕێگە بدە بە بەکارهێنانی مایکرۆفۆنەکە بۆ تۆمارکردن.",audio:"دەنگ",record_from_microphone:"تۆمارکردن لە مایکەوە",stop_recording:"تۆمارکردن بوەستێنە"},Ir={connection_can_break:"لە مۆبایلدا، پەیوەندییەکە دەکرێت بپچڕێت ئەگەر ئەم تابە چالاک نەبێت یان ئامێرەکە بچێتە دۆخی پشوو، ئەمەش شوێنی خۆت لە ڕیزدا لەدەست دەدات.",long_requests_queue:"ڕیزێکی درێژی داواکاری هەیە. ئەم سپەیسە دووباد بکە بۆی چاوەڕوان نەبیت.",lost_connection:"پەیوەندی پچڕا بەهۆی جێهێشتنی پەیج. "},Lr={checkbox:"بۆکسی هەڵبژاردن",checkbox_group:"گروپی بۆکسی هەڵبژاردن"},Rr={code:"کۆد"},kr={color_picker:"ڕەنگ هەڵبژاردە"},Mr={built_with:"دروستکراوە لەگەڵ...",built_with_gradio:"Gradio دروستکراوە بە",clear:"خاوێنکردنەوە",download:"دابەزاندن",edit:"بژارکردن",empty:"بەتاڵ",error:"هەڵە",hosted_on:"میوانداری کراوە لە",loading:"بارکردن",logo:"لۆگۆ",or:"یان",remove:"لابردن",share:"هاوبەشکردن",submit:"پێشکەشکردن",undo:"پووچکردنەوە"},Dr={incorrect_format:"فۆرماتێکی هەڵە، تەنها فایلەکانی CSV و TSV پشتگیری دەکرێن",new_column:"ستوونی نوێ",new_row:"ڕیزێکی نوێ"},jr={dropdown:"فڕێدانە خوار"},$r={build_error:"هەڵەی دروستکردن هەیە",config_error:"هەڵەی ڕێکخستن هەیە",contact_page_author:"تکایە پەیوەندی بە نووسەری پەیجەوە بکەن بۆ ئەوەی ئاگاداریان بکەنەوە.",no_app_file:"هیچ فایلێکی ئەپ نییە",runtime_error:"هەڵەیەکی runtime هەیە",space_not_working:'"سپەیسەکە کارناکات چونکە" {0}',space_paused:"فەزاکە وەستاوە",use_via_api:"لە ڕێگەی API بەکاری بهێنە"},Ur={uploading:"بارکردن..."},Gr={highlighted_text:"دەقی ڕۆشن کراو"},Fr={allow_webcam_access:"تکایە ڕێگە بدە بە بەکارهێنانی وێبکامەکە بۆ تۆمارکردن.",brush_color:"ڕەنگی فڵچە",brush_radius:"تیژڕەوی فڵچە",image:"وێنە",remove_image:"لابردنی وێنە",select_brush_color:"ڕەنگی فڵچە هەڵبژێرە",start_drawing:"دەست بکە بە وێنەکێشان",use_brush:"فڵچە بەکاربهێنە"},zr={label:"لەیبڵ"},qr={enable_cookies:"ئەگەر تۆ سەردانی HuggingFace Space دەکەیت لە دۆخی نادیاردا، پێویستە کووکی لایەنی سێیەم چالاک بکەیت.",incorrect_credentials:"بڕوانامەی هەڵە",login:"چونه‌ ژووره‌وه‌"},Vr={number:"ژمارە"},Xr={plot:"هێڵکاری"},Wr={radio:"ڕادیۆ"},Jr={slider:"خلیسکە"},Zr={click_to_upload:"کلیک بکە بۆ بارکردن",drop_audio:"دەنگ لێرە دابنێ",drop_csv:"لێرەدا CSV دابنێ",drop_file:"فایل لێرە دابنێ",drop_image:"وێنە لێرەدا دابنێ",drop_video:"ڤیدیۆ لێرە دابنێ"},Va={"3D_model":{"3d_model":"مۆدێلی سێ ڕەهەندی"},annotated_image:Br,audio:Nr,blocks:Ir,checkbox:Lr,code:Rr,color_picker:kr,common:Mr,dataframe:Dr,dropdown:jr,errors:$r,file:Ur,highlighted_text:Gr,image:Fr,label:zr,login:qr,number:Vr,plot:Xr,radio:Wr,slider:Jr,upload_text:Zr},Xa=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:Br,audio:Nr,blocks:Ir,checkbox:Lr,code:Rr,color_picker:kr,common:Mr,dataframe:Dr,default:Va,dropdown:jr,errors:$r,file:Ur,highlighted_text:Gr,image:Fr,label:zr,login:qr,number:Vr,plot:Xr,radio:Wr,slider:Jr,upload_text:Zr},Symbol.toStringTag,{value:"Module"})),Yr={built_with_gradio:"Mit Gradio erstellt",clear:"Löschen",or:"oder",submit:"Absenden"},Qr={click_to_upload:"Hochladen",drop_audio:"Audio hier ablegen",drop_csv:"CSV Datei hier ablegen",drop_file:"Datei hier ablegen",drop_image:"Bild hier ablegen",drop_video:"Video hier ablegen"},Wa={common:Yr,upload_text:Qr},Ja=Object.freeze(Object.defineProperty({__proto__:null,common:Yr,default:Wa,upload_text:Qr},Symbol.toStringTag,{value:"Module"})),Kr={annotated_image:"Annotated Image"},eo={allow_recording_access:"Please allow access to the microphone for recording.",audio:"Audio",record_from_microphone:"Record from microphone",stop_recording:"Stop recording",no_device_support:"Media devices could not be accessed. Check that you are running on a secure origin (https) or localhost (or you have passed a valid SSL certificate to ssl_verify), and you have allowed browser access to your device.",stop:"Stop",resume:"Resume",record:"Record",no_microphone:"No microphone found",pause:"Pause",play:"Play"},to={connection_can_break:"On mobile, the connection can break if this tab is unfocused or the device sleeps, losing your position in queue.",long_requests_queue:"There is a long queue of requests pending. Duplicate this Space to skip.",lost_connection:"Lost connection due to leaving page. Rejoining queue..."},ro={checkbox:"Checkbox",checkbox_group:"Checkbox Group"},oo={code:"Code"},no={color_picker:"Color Picker"},io={built_with:"built with",built_with_gradio:"Built with Gradio",clear:"Clear",download:"Download",edit:"Edit",empty:"Empty",error:"Error",hosted_on:"Hosted on",loading:"Loading",logo:"logo",or:"or",remove:"Remove",share:"Share",submit:"Submit",undo:"Undo",no_devices:"No devices found"},so={incorrect_format:"Incorrect format, only CSV and TSV files are supported",new_column:"New column",new_row:"New row"},ao={dropdown:"Dropdown"},lo={build_error:"there is a build error",config_error:"there is a config error",contact_page_author:"Please contact the author of the page to let them know.",no_app_file:"there is no app file",runtime_error:"there is a runtime error",space_not_working:`"Space isn't working because" {0}`,space_paused:"the space is paused",use_via_api:"Use via API"},uo={uploading:"Uploading..."},co={highlighted_text:"Highlighted Text"},ho={allow_webcam_access:"Please allow access to the webcam for recording.",brush_color:"Brush color",brush_radius:"Brush radius",image:"Image",remove_image:"Remove Image",select_brush_color:"Select brush color",start_drawing:"Start drawing",use_brush:"Use brush"},po={label:"Label"},fo={enable_cookies:"If you are visiting a HuggingFace Space in Incognito mode, you must enable third party cookies.",incorrect_credentials:"Incorrect Credentials",login:"Login"},_o={number:"Number"},mo={plot:"Plot"},go={radio:"Radio"},bo={slider:"Slider"},yo={click_to_upload:"Click to Upload",drop_audio:"Drop Audio Here",drop_csv:"Drop CSV Here",drop_file:"Drop File Here",drop_image:"Drop Image Here",drop_video:"Drop Video Here",drop_gallery:"Drop Image(s) Here",paste_clipboard:"Paste from Clipboard"},Za={"3D_model":{"3d_model":"3D Model"},annotated_image:Kr,audio:eo,blocks:to,checkbox:ro,code:oo,color_picker:no,common:io,dataframe:so,dropdown:ao,errors:lo,file:uo,highlighted_text:co,image:ho,label:po,login:fo,number:_o,plot:mo,radio:go,slider:bo,upload_text:yo},Ya=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:Kr,audio:eo,blocks:to,checkbox:ro,code:oo,color_picker:no,common:io,dataframe:so,default:Za,dropdown:ao,errors:lo,file:uo,highlighted_text:co,image:ho,label:po,login:fo,number:_o,plot:mo,radio:go,slider:bo,upload_text:yo},Symbol.toStringTag,{value:"Module"})),vo={built_with_gradio:"Construido con Gradio",clear:"Limpiar",or:"o",submit:"Enviar"},wo={click_to_upload:"Haga click para cargar",drop_audio:"Coloque el audio aquí",drop_csv:"Coloque el CSV aquí",drop_file:"Coloque el archivo aquí",drop_image:"Coloque la imagen aquí",drop_video:"Coloque el video aquí",drop_gallery:"Coloque las imagenes aquí"},Qa={common:vo,upload_text:wo},Ka=Object.freeze(Object.defineProperty({__proto__:null,common:vo,default:Qa,upload_text:wo},Symbol.toStringTag,{value:"Module"})),xo={built_with_gradio:"Gradiorekin eraikia",clear:"Garbitu",or:"edo",submit:"Bidali"},Eo={click_to_upload:"Klik egin kargatzeko",drop_audio:"Jarri hemen audioa",drop_csv:"Jarri hemen CSVa",drop_file:"Jarri hemen fitxategia",drop_image:"Jarri hemen irudia",drop_video:"Jarri hemen bideoa"},el={common:xo,upload_text:Eo},tl=Object.freeze(Object.defineProperty({__proto__:null,common:xo,default:el,upload_text:Eo},Symbol.toStringTag,{value:"Module"})),So={built_with_gradio:"ساخته شده با gradio",clear:"حذف",or:"یا",submit:"ارسال"},To={click_to_upload:"برای آپلود کلیک کنید",drop_audio:"صوت را اینجا رها کنید",drop_csv:"فایل csv را  اینجا رها کنید",drop_file:"فایل را اینجا رها کنید",drop_image:"تصویر را اینجا رها کنید",drop_video:"ویدیو را اینجا رها کنید"},rl={common:So,upload_text:To},ol=Object.freeze(Object.defineProperty({__proto__:null,common:So,default:rl,upload_text:To},Symbol.toStringTag,{value:"Module"})),Oo={allow_recording_access:"Veuillez autoriser l'accès à l'enregistrement",audio:"Audio",record_from_microphone:"Enregistrer avec le microphone",stop_recording:"Arrêter l'enregistrement"},Po={built_with:"Construit avec",built_with_gradio:"Construit avec Gradio",clear:"Effacer",download:"Télécharger",edit:"Éditer",error:"Erreur",loading:"Chargement",logo:"logo",or:"ou",remove:"Supprimer",share:"Partager",submit:"Soumettre"},Ao={click_to_upload:"Cliquer pour Télécharger",drop_audio:"Déposer l'Audio Ici",drop_csv:"Déposer le CSV Ici",drop_file:"Déposer le Fichier Ici",drop_image:"Déposer l'Image Ici",drop_video:"Déposer la Vidéo Ici"},nl={audio:Oo,common:Po,upload_text:Ao},il=Object.freeze(Object.defineProperty({__proto__:null,audio:Oo,common:Po,default:nl,upload_text:Ao},Symbol.toStringTag,{value:"Module"})),Co={built_with_gradio:"בנוי עם גרדיו",clear:"נקה",or:"או",submit:"שלח"},Ho={click_to_upload:"לחץ כדי להעלות",drop_audio:"גרור לכאן קובץ שמע",drop_csv:"גרור csv קובץ לכאן",drop_file:"גרור קובץ לכאן",drop_image:"גרור קובץ תמונה לכאן",drop_video:"גרור קובץ סרטון לכאן"},sl={common:Co,upload_text:Ho},al=Object.freeze(Object.defineProperty({__proto__:null,common:Co,default:sl,upload_text:Ho},Symbol.toStringTag,{value:"Module"})),Bo={built_with_gradio:"Gradio से बना",clear:"हटाये",or:"या",submit:"सबमिट करे"},No={click_to_upload:"अपलोड के लिए बटन दबायें",drop_audio:"यहाँ ऑडियो ड्रॉप करें",drop_csv:"यहाँ CSV ड्रॉप करें",drop_file:"यहाँ File ड्रॉप करें",drop_image:"यहाँ इमेज ड्रॉप करें",drop_video:"यहाँ वीडियो ड्रॉप करें"},ll={common:Bo,upload_text:No},ul=Object.freeze(Object.defineProperty({__proto__:null,common:Bo,default:ll,upload_text:No},Symbol.toStringTag,{value:"Module"})),Io={built_with_gradio:"gradioで作ろう",clear:"クリア",or:"または",submit:"送信"},Lo={click_to_upload:"クリックしてアップロード",drop_audio:"ここに音声をドロップ",drop_csv:"ここにCSVをドロップ",drop_file:"ここにファイルをドロップ",drop_image:"ここに画像をドロップ",drop_video:"ここに動画をドロップ"},cl={common:Io,upload_text:Lo},hl=Object.freeze(Object.defineProperty({__proto__:null,common:Io,default:cl,upload_text:Lo},Symbol.toStringTag,{value:"Module"})),Ro={built_with_gradio:"gradio로 제작되었습니다",clear:"클리어",or:"또는",submit:"제출하기"},ko={click_to_upload:"클릭해서 업로드하기",drop_audio:"오디오를 끌어 놓으세요",drop_csv:"CSV파일을 끌어 놓으세요",drop_file:"파일을 끌어 놓으세요",drop_image:"이미지를 끌어 놓으세요",drop_video:"비디오를 끌어 놓으세요"},dl={common:Ro,upload_text:ko},pl=Object.freeze(Object.defineProperty({__proto__:null,common:Ro,default:dl,upload_text:ko},Symbol.toStringTag,{value:"Module"})),Mo={built_with_gradio:"sukurta su gradio",clear:"Trinti",or:"arba",submit:"Pateikti"},Do={click_to_upload:"Spustelėkite norėdami įkelti",drop_audio:"Įkelkite garso įrašą čia",drop_csv:"Įkelkite CSV čia",drop_file:"Įkelkite bylą čia",drop_image:"Įkelkite paveikslėlį čia",drop_video:"Įkelkite vaizdo įrašą čia"},fl={common:Mo,upload_text:Do},_l=Object.freeze(Object.defineProperty({__proto__:null,common:Mo,default:fl,upload_text:Do},Symbol.toStringTag,{value:"Module"})),jo={built_with_gradio:"gemaakt met gradio",clear:"Wis",or:"of",submit:"Zend in"},$o={click_to_upload:"Klik om the Uploaden",drop_audio:"Sleep een Geluidsbestand hier",drop_csv:"Sleep een CSV hier",drop_file:"Sleep een Document hier",drop_image:"Sleep een Afbeelding hier",drop_video:"Sleep een Video hier"},ml={common:jo,upload_text:$o},gl=Object.freeze(Object.defineProperty({__proto__:null,common:jo,default:ml,upload_text:$o},Symbol.toStringTag,{value:"Module"})),Uo={built_with_gradio:"utworzone z gradio",clear:"Wyczyść",or:"lub",submit:"Zatwierdź"},Go={click_to_upload:"Kliknij, aby przesłać",drop_audio:"Przeciągnij tutaj audio",drop_csv:"Przeciągnij tutaj CSV",drop_file:"Przeciągnij tutaj plik",drop_image:"Przeciągnij tutaj zdjęcie",drop_video:"Przeciągnij tutaj video"},bl={common:Uo,upload_text:Go},yl=Object.freeze(Object.defineProperty({__proto__:null,common:Uo,default:bl,upload_text:Go},Symbol.toStringTag,{value:"Module"})),Fo={built_with_gradio:"Construído com gradio",clear:"Limpar",error:"Erro",flag:"Marcar",loading:"Carregando",or:"ou",submit:"Enviar"},zo={click_to_upload:"Clique para o Upload",drop_audio:"Solte o Áudio Aqui",drop_csv:"Solte o CSV Aqui",drop_file:"Solte o Arquivo Aqui",drop_image:"Solte a Imagem Aqui",drop_video:"Solte o Vídeo Aqui"},vl={common:Fo,upload_text:zo},wl=Object.freeze(Object.defineProperty({__proto__:null,common:Fo,default:vl,upload_text:zo},Symbol.toStringTag,{value:"Module"})),qo={annotated_image:"Аннотированное изображение"},Vo={allow_recording_access:"Пожалуйста, предоставьте доступ к микрофону для записи.",audio:"Аудио",record_from_microphone:"Записать с микрофона",stop_recording:"Остановить запись",no_device_support:"Не удалось получить доступ к медиаустройствам. Убедитесь, что вы работаете на защищенном источнике (https) или localhost (или передали действительный SSL-сертификат в ssl_verify), и разрешили браузеру доступ к устройству.",stop:"Стоп",resume:"Продолжить",record:"Записать",no_microphone:"Микрофон не найден",pause:"Пауза",play:"Воспроизвести"},Xo={connection_can_break:"На мобильных устройствах соединение может прерваться, если вкладка будет переключена или устройство отключится, что приведет к потере вашей позиции в очереди.",long_requests_queue:"Очередь запросов длинная. Продублируйте это пространство, чтобы пропустить.",lost_connection:"Потеряно соединение из-за ухода со страницы. Повторное подключение..."},Wo={checkbox:"Чекбокс",checkbox_group:"Группа чекбоксов"},Jo={code:"Код"},Zo={color_picker:"Выбор цвета"},Yo={built_with:"создано с",built_with_gradio:"Создано с помощью Gradio",clear:"Очистить",download:"Скачать",edit:"Изменить",empty:"Пусто",error:"Ошибка",hosted_on:"Размещено на",loading:"Загрузка",logo:"логотип",or:"или",remove:"Удалить",share:"Поделиться",submit:"Отправить",undo:"Отменить",no_devices:"Не найдено ни одного устройства"},Qo={incorrect_format:"Неправильный формат, поддерживаются только файлы CSV и TSV",new_column:"Новая колонка",new_row:"Новый ряд"},Ko={dropdown:"Dropdown"},en={build_error:"возникла ошибка сборки",config_error:"возникла ошибка конфигурации",contact_page_author:"Пожалуйста, свяжитесь с автором страницы, чтобы сообщить ему об этом.",no_app_file:"отсутствует файл приложения",runtime_error:"возникла проблема с выполнением",space_not_working:'"Пространство не работает, потому что" {0}',space_paused:"пространство приостановлено",use_via_api:"Использовать через API"},tn={uploading:"Загружаем..."},rn={highlighted_text:"Выделенный текст"},on={allow_webcam_access:"Пожалуйста, разрешите доступ к веб-камере для записи.",brush_color:"Цвет кисти",brush_radius:"Радиус кисти",image:"Изображение",remove_image:"Удалить изображение",select_brush_color:"Выберите цвет кисти",start_drawing:"Начните рисовать",use_brush:"Используйте кисть"},nn={label:"Лейбл"},sn={enable_cookies:"Если вы посещаете пространство HuggingFace в режиме инкогнито, вы должны разрешить сторонние файлы cookie.",incorrect_credentials:"Неправильные учетные данные",login:"Вход в систему"},an={number:"Число"},ln={plot:"Схема"},un={radio:"Радио"},cn={slider:"Слайдер"},hn={click_to_upload:"Нажмите, чтобы загрузить",drop_audio:"Перетащите аудио сюда",drop_csv:"Перетащите файл CSV сюда",drop_file:"Перетащите файл сюда",drop_image:"Перетащите изображение сюда",drop_video:"Перетащите видео сюда",drop_gallery:"Перетащите изображение(-я) сюда",paste_clipboard:"Вставка из буфера обмена"},xl={"3D_model":{"3d_model":"3D-модель"},annotated_image:qo,audio:Vo,blocks:Xo,checkbox:Wo,code:Jo,color_picker:Zo,common:Yo,dataframe:Qo,dropdown:Ko,errors:en,file:tn,highlighted_text:rn,image:on,label:nn,login:sn,number:an,plot:ln,radio:un,slider:cn,upload_text:hn},El=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:qo,audio:Vo,blocks:Xo,checkbox:Wo,code:Jo,color_picker:Zo,common:Yo,dataframe:Qo,default:xl,dropdown:Ko,errors:en,file:tn,highlighted_text:rn,image:on,label:nn,login:sn,number:an,plot:ln,radio:un,slider:cn,upload_text:hn},Symbol.toStringTag,{value:"Module"})),dn={built_with_gradio:"கிரேடியோ வுடன் உருவாக்கப்பட்டது",clear:"அழிக்கவும்",or:"அல்லது",submit:"சமர்ப்பிக்கவும்"},pn={click_to_upload:"பதிவேற்ற அழுத்தவும்",drop_audio:"ஆடியோவை பதிவேற்றவும்",drop_csv:"csv ஐ பதிவேற்றவும்",drop_file:"கோப்பை பதிவேற்றவும்",drop_image:"படத்தை பதிவேற்றவும்",drop_video:"காணொளியை பதிவேற்றவும்"},Sl={common:dn,upload_text:pn},Tl=Object.freeze(Object.defineProperty({__proto__:null,common:dn,default:Sl,upload_text:pn},Symbol.toStringTag,{value:"Module"})),fn={built_with_gradio:"Gradio ile oluşturulmuştur",clear:"Temizle",or:"veya",submit:"Yükle"},_n={click_to_upload:"Yüklemek için Tıkla",drop_audio:"Kaydı Buraya Sürükle",drop_csv:"CSV'yi Buraya Sürükle",drop_file:"Dosyayı Buraya Sürükle",drop_image:"Resmi Buraya Sürükle",drop_video:"Videoyu Buraya Sürükle"},Ol={common:fn,upload_text:_n},Pl=Object.freeze(Object.defineProperty({__proto__:null,common:fn,default:Ol,upload_text:_n},Symbol.toStringTag,{value:"Module"})),mn={built_with_gradio:"Зроблено на основі gradio",clear:"Очистити",or:"або",submit:"Надіслати"},gn={click_to_upload:"Натисніть щоб завантажити",drop_audio:"Перетягніть аудіо сюди",drop_csv:"Перетягніть CSV-файл сюди",drop_file:"Перетягніть файл сюди",drop_image:"Перетягніть зображення сюди",drop_video:"Перетягніть відео сюди"},Al={common:mn,upload_text:gn},Cl=Object.freeze(Object.defineProperty({__proto__:null,common:mn,default:Al,upload_text:gn},Symbol.toStringTag,{value:"Module"})),bn={built_with_gradio:"کے ساتھ بنایا گیا Gradio",clear:"ہٹا دیں",or:"یا",submit:"جمع کریں"},yn={click_to_upload:"اپ لوڈ کے لیے کلک کریں",drop_audio:"یہاں آڈیو ڈراپ کریں",drop_csv:"یہاں فائل ڈراپ کریں",drop_file:"یہاں فائل ڈراپ کریں",drop_image:"یہاں تصویر ڈراپ کریں",drop_video:"یہاں ویڈیو ڈراپ کریں"},Hl={common:bn,upload_text:yn},Bl=Object.freeze(Object.defineProperty({__proto__:null,common:bn,default:Hl,upload_text:yn},Symbol.toStringTag,{value:"Module"})),vn={built_with_gradio:"gradio bilan qilingan",clear:"Tozalash",submit:"Yubor"},wn={click_to_upload:"Yuklash uchun Bosing",drop_audio:"Audioni Shu Yerga Tashlang",drop_csv:"CSVni Shu Yerga Tashlang",drop_file:"Faylni Shu Yerga Tashlang",drop_image:"Rasmni Shu Yerga Tashlang",drop_video:"Videoni Shu Yerga Tashlang"},Nl={common:vn,upload_text:wn},Il=Object.freeze(Object.defineProperty({__proto__:null,common:vn,default:Nl,upload_text:wn},Symbol.toStringTag,{value:"Module"})),xn={annotated_image:"标注图像"},En={allow_recording_access:"请允许访问麦克风以进行录音。",audio:"音频",record_from_microphone:"从麦克风录制",stop_recording:"停止录制",no_device_support:"无法访问媒体设备。请检查您是否在安全来源（https）或本地主机上运行（或者您已经通过 ssl_verify 传递了有效的 SSL 证书），并且您已经允许浏览器访问您的设备。",stop:"停止",resume:"继续",record:"录制",no_microphone:"找不到麦克风",pause:"暂停",play:"播放"},Sn={connection_can_break:"在移动设备上，如果此标签页失去焦点或设备休眠，连接可能会中断，导致您在队列中失去位置。",long_requests_queue:"有一个长时间的待处理请求队列。复制此空间以跳过。",lost_connection:"由于离开页面，连接已丢失。重新加入队列..."},Tn={checkbox:"复选框",checkbox_group:"复选框组"},On={code:"代码"},Pn={color_picker:"颜色选择器"},An={built_with:"构建于",built_with_gradio:"使用 Gradio 构建",clear:"清除",download:"下载",edit:"编辑",empty:"空",error:"错误",hosted_on:"托管在",loading:"加载中",logo:"标志",or:"或",remove:"移除",share:"分享",submit:"提交",undo:"撤销"},Cn={incorrect_format:"格式不正确，仅支持 CSV 和 TSV 文件",new_column:"新列",new_row:"新行"},Hn={dropdown:"下拉菜单"},Bn={build_error:"存在构建错误",config_error:"存在配置错误",contact_page_author:"请联系页面的作者并告知他们。",no_app_file:"不存在应用文件",runtime_error:"存在运行时错误",space_not_working:'"空间无法工作，原因：" {0}',space_paused:"空间已暂停",use_via_api:"通过 API 使用"},Nn={uploading:"正在上传..."},In={highlighted_text:"高亮文本"},Ln={allow_webcam_access:"请允许访问网络摄像头以进行录制。",brush_color:"画笔颜色",brush_radius:"画笔半径",image:"图像",remove_image:"移除图像",select_brush_color:"选择画笔颜色",start_drawing:"开始绘画",use_brush:"使用画笔"},Rn={label:"标签"},kn={enable_cookies:"如果您正在使用隐身模式访问 HuggingFace 空间，您必须启用第三方 cookie。",incorrect_credentials:"凭据不正确",login:"登录"},Mn={number:"数字"},Dn={plot:"图表"},jn={radio:"单选框"},$n={slider:"滑块"},Un={click_to_upload:"点击上传",drop_audio:"将音频拖放到此处",drop_csv:"将 CSV 文件拖放到此处",drop_file:"将文件拖放到此处",drop_image:"将图像拖放到此处",drop_video:"将视频拖放到此处"},Ll={"3D_model":{"3d_model":"3D模型"},annotated_image:xn,audio:En,blocks:Sn,checkbox:Tn,code:On,color_picker:Pn,common:An,dataframe:Cn,dropdown:Hn,errors:Bn,file:Nn,highlighted_text:In,image:Ln,label:Rn,login:kn,number:Mn,plot:Dn,radio:jn,slider:$n,upload_text:Un},Rl=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:xn,audio:En,blocks:Sn,checkbox:Tn,code:On,color_picker:Pn,common:An,dataframe:Cn,default:Ll,dropdown:Hn,errors:Bn,file:Nn,highlighted_text:In,image:Ln,label:Rn,login:kn,number:Mn,plot:Dn,radio:jn,slider:$n,upload_text:Un},Symbol.toStringTag,{value:"Module"})),Gn={built_with_gradio:"使用Gradio構建",clear:"清除",or:"或",submit:"提交"},Fn={click_to_upload:"點擊上傳",drop_audio:"拖放音訊至此處",drop_csv:"拖放CSV至此處",drop_file:"拖放檔案至此處",drop_image:"拖放圖片至此處",drop_video:"拖放影片至此處"},kl={common:Gn,upload_text:Fn},Ml=Object.freeze(Object.defineProperty({__proto__:null,common:Gn,default:kl,upload_text:Fn},Symbol.toStringTag,{value:"Module"})),Gt=Object.assign({"./lang/ar.json":Fa,"./lang/ca.json":qa,"./lang/ckb.json":Xa,"./lang/de.json":Ja,"./lang/en.json":Ya,"./lang/es.json":Ka,"./lang/eu.json":tl,"./lang/fa.json":ol,"./lang/fr.json":il,"./lang/he.json":al,"./lang/hi.json":ul,"./lang/ja.json":hl,"./lang/ko.json":pl,"./lang/lt.json":_l,"./lang/nl.json":gl,"./lang/pl.json":yl,"./lang/pt-BR.json":wl,"./lang/ru.json":El,"./lang/ta.json":Tl,"./lang/tr.json":Pl,"./lang/uk.json":Cl,"./lang/ur.json":Bl,"./lang/uz.json":Il,"./lang/zh-CN.json":Rl,"./lang/zh-TW.json":Ml});function Dl(){let e={};for(const t in Gt){const r=t.split("/").pop().split(".").shift();e[r]=Gt[t].default}return e}const Ft=Dl();for(const e in Ft)wr(e,Ft[e]);async function Yl(){await ya({fallbackLocale:"en",initialLocale:Aa()})}const jl="./assets/index-BOW2xVAS.css";let st;st=[];let at,zn,$l=new Promise(e=>{zn=e});async function Ul(){at=(await Te(()=>import("./Index-DB1XLvMK.js").then(e=>e.a),__vite__mapDeps([0,1]),import.meta.url)).default,zn()}function Gl(){const e={SvelteComponent:Ue.SvelteComponent};for(const r in Ue)r!=="SvelteComponent"&&(r==="SvelteComponentDev"?e[r]=e.SvelteComponent:e[r]=Ue[r]);window.__gradio__svelte__internal=e;class t extends HTMLElement{constructor(){super(),this.host=this.getAttribute("host"),this.space=this.getAttribute("space"),this.src=this.getAttribute("src"),this.control_page_title=this.getAttribute("control_page_title"),this.initial_height=this.getAttribute("initial_height")??"300px",this.is_embed=this.getAttribute("embed")??"true",this.container=this.getAttribute("container")??"true",this.info=this.getAttribute("info")??!0,this.autoscroll=this.getAttribute("autoscroll"),this.eager=this.getAttribute("eager"),this.theme_mode=this.getAttribute("theme_mode"),this.updating=!1,this.loading=!1}async connectedCallback(){await Ul(),this.loading=!0,this.app&&this.app.$destroy(),typeof st!="string"&&st.forEach(i=>Ut(i,document.head)),await Ut(jl,document.head);const o=new CustomEvent("domchange",{bubbles:!0,cancelable:!1,composed:!0});new MutationObserver(i=>{this.dispatchEvent(o)}).observe(this,{childList:!0}),this.app=new at({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,host:this.host?this.host.trim():this.host,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"4-44-1",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",Client:Pe,app_mode:window.__gradio_mode__==="app"}}),this.updating&&this.setAttribute(this.updating.name,this.updating.value),this.loading=!1}static get observedAttributes(){return["src","space","host"]}async attributeChangedCallback(o,n,i){if(await $l,(o==="host"||o==="space"||o==="src")&&i!==n){if(this.updating={name:o,value:i},this.loading)return;this.app&&this.app.$destroy(),this.space=null,this.host=null,this.src=null,o==="host"?this.host=i:o==="space"?this.space=i:o==="src"&&(this.src=i),this.app=new at({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,host:this.host?this.host.trim():this.host,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"4-44-1",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",Client:Pe,app_mode:window.__gradio_mode__==="app"}}),this.updating=!1}}}customElements.get("gradio-app")||customElements.define("gradio-app",t)}Gl();export{Jl as $,Te as _,Dl as a,Fl as b,Xl as c,Vl as d,Zi as e,ql as f,Ki as g,Wl as h,zl as i,Ut as m,oe as n,Zl as p,Yl as s,Ie as w};
//# sourceMappingURL=index-BQPjLIsY.js.map

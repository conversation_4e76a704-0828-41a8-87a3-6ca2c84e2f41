{"version": 3, "file": "Index-D9JcTZE7.js", "sources": ["../../../../js/slider/Index.svelte"], "sourcesContent": ["<script context=\"module\">\n\tlet _id = 0;\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { Block, BlockTitle } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { afterUpdate } from \"svelte\";\n\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tinput: never;\n\t\trelease: number;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = 0;\n\texport let label = gradio.i18n(\"slider.slider\");\n\texport let info: string | undefined = undefined;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let minimum: number;\n\texport let maximum = 100;\n\texport let step: number;\n\texport let show_label: boolean;\n\texport let interactive: boolean;\n\texport let loading_status: LoadingStatus;\n\texport let value_is_output = false;\n\n\tlet rangeInput: HTMLInputElement;\n\tlet numberInput: HTMLInputElement;\n\n\tconst id = `range_id_${_id++}`;\n\n\tfunction handle_change(): void {\n\t\tgradio.dispatch(\"change\");\n\t\tif (!value_is_output) {\n\t\t\tgradio.dispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t\tsetSlider();\n\t});\n\n\tfunction handle_release(e: MouseEvent): void {\n\t\tgradio.dispatch(\"release\", value);\n\t}\n\tfunction clamp(): void {\n\t\tgradio.dispatch(\"release\", value);\n\t\tvalue = Math.min(Math.max(value, minimum), maximum);\n\t}\n\n\tfunction setSlider(): void {\n\t\tsetSliderRange();\n\t\trangeInput.addEventListener(\"input\", setSliderRange);\n\t\tnumberInput.addEventListener(\"input\", setSliderRange);\n\t}\n\tfunction setSliderRange(): void {\n\t\tconst dividend = Number(rangeInput.value) - Number(rangeInput.min);\n\t\tconst divisor = Number(rangeInput.max) - Number(rangeInput.min);\n\t\tconst h = divisor === 0 ? 0 : dividend / divisor;\n\t\trangeInput.style.backgroundSize = h * 100 + \"% 100%\";\n\t}\n\n\t$: disabled = !interactive;\n\n\t// When the value changes, dispatch the change event via handle_change()\n\t// See the docs for an explanation: https://svelte.dev/docs/svelte-components#script-3-$-marks-a-statement-as-reactive\n\t$: value, handle_change();\n</script>\n\n<Block {visible} {elem_id} {elem_classes} {container} {scale} {min_width}>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\n\t<div class=\"wrap\">\n\t\t<div class=\"head\">\n\t\t\t<label for={id}>\n\t\t\t\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\t\t\t</label>\n\n\t\t\t<input\n\t\t\t\taria-label={`number input for ${label}`}\n\t\t\t\tdata-testid=\"number-input\"\n\t\t\t\ttype=\"number\"\n\t\t\t\tbind:value\n\t\t\t\tbind:this={numberInput}\n\t\t\t\tmin={minimum}\n\t\t\t\tmax={maximum}\n\t\t\t\ton:blur={clamp}\n\t\t\t\t{step}\n\t\t\t\t{disabled}\n\t\t\t\ton:pointerup={handle_release}\n\t\t\t/>\n\t\t</div>\n\t</div>\n\n\t<input\n\t\ttype=\"range\"\n\t\t{id}\n\t\tname=\"cowbell\"\n\t\tbind:value\n\t\tbind:this={rangeInput}\n\t\tmin={minimum}\n\t\tmax={maximum}\n\t\t{step}\n\t\t{disabled}\n\t\ton:pointerup={handle_release}\n\t\taria-label={`range slider for ${label}`}\n\t/>\n</Block>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\twidth: 100%;\n\t}\n\n\t.head {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t}\n\n\tinput[type=\"number\"] {\n\t\tdisplay: block;\n\t\tposition: relative;\n\t\toutline: none !important;\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: var(--input-border-width) solid var(--input-border-color);\n\t\tborder-radius: var(--input-radius);\n\t\tbackground: var(--input-background-fill);\n\t\tpadding: var(--size-2) var(--size-2);\n\t\theight: var(--size-6);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-sm);\n\t\ttext-align: center;\n\t}\n\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t}\n\n\tinput[type=\"number\"]:focus {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t}\n\n\tinput::placeholder {\n\t\tcolor: var(--input-placeholder-color);\n\t}\n\n\tinput[disabled] {\n\t\tcursor: not-allowed;\n\t}\n\n\tinput[type=\"range\"] {\n\t\t-webkit-appearance: none;\n\t\tappearance: none;\n\t\twidth: 100%;\n\t\taccent-color: var(--slider-color);\n\t\theight: 4px;\n\t\tbackground: var(--neutral-200);\n\t\tborder-radius: 5px;\n\t\tbackground-image: linear-gradient(var(--slider-color), var(--slider-color));\n\t\tbackground-size: 0% 100%;\n\t\tbackground-repeat: no-repeat;\n\t}\n\n\tinput[type=\"range\"]::-webkit-slider-thumb {\n\t\t-webkit-appearance: none;\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: solid 0.5px #ddd;\n\t\theight: 20px;\n\t\twidth: 20px;\n\t\tborder-radius: 50%;\n\t\tbackground-color: white;\n\t\tcursor: pointer;\n\t\tmargin-top: -2px;\n\t\ttransition: background-color 0.1s ease;\n\t}\n\n\tinput[type=\"range\"]::-webkit-slider-thumb:hover {\n\t\tbackground: var(--neutral-50);\n\t}\n\n\tinput[type=\"range\"][disabled] {\n\t\tbackground: var(--body-text-color-subdued);\n\t}\n\n\tinput[type=\"range\"][disabled]::-webkit-slider-thumb {\n\t\tcursor: not-allowed;\n\t\tbackground-color: var(--body-text-color-subdued);\n\t}\n\n\tinput[type=\"range\"][disabled]::-moz-range-track {\n\t\tcursor: not-allowed;\n\t\tbackground-color: var(--body-text-color-subdued);\n\t}\n\n\tinput[type=\"range\"][disabled]::-webkit-slider-thumb:hover {\n\t\tbackground-color: var(--body-text-color-subdued);\n\t}\n\n\tinput[type=\"range\"][disabled]::-moz-range-track:hover {\n\t\tbackground-color: var(--body-text-color-subdued);\n\t}\n\n\tinput[type=\"range\"]::-webkit-slider-runnable-track {\n\t\t-webkit-appearance: none;\n\t\tbox-shadow: none;\n\t\tborder: none;\n\t\tbackground: transparent;\n\t\theight: 400%;\n\t}\n\n\tinput[type=\"range\"]::-moz-range-track {\n\t\theight: 12px;\n\t}\n</style>\n"], "names": ["afterUpdate", "ctx", "insert", "target", "div1", "anchor", "append", "div0", "label_1", "input0", "input1", "dirty", "_id", "gradio", "$$props", "elem_id", "elem_classes", "visible", "value", "label", "info", "container", "scale", "min_width", "minimum", "maximum", "step", "show_label", "interactive", "loading_status", "value_is_output", "rangeInput", "numberInput", "id", "handle_change", "$$invalidate", "set<PERSON><PERSON><PERSON>", "handle_release", "e", "clamp", "setSliderRange", "dividend", "divisor", "h", "clear_status_handler", "$$value", "disabled"], "mappings": "4kBASU,CAAA,YAAAA,WAA2B,gEA+EAC,EAAK,CAAA,CAAA,yCAALA,EAAK,CAAA,CAAA,4EAT5B,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,kUAMLA,EAAE,EAAA,CAAA,yCAKmBA,EAAK,CAAA,CAAA,EAAA,mEAKhCA,EAAO,EAAA,CAAA,YACPA,EAAO,EAAA,CAAA,0MAeTA,EAAO,EAAA,CAAA,YACPA,EAAO,EAAA,CAAA,4EAIoBA,EAAK,CAAA,CAAA,EAAA,yDAjCtCC,EAoBKC,EAAAC,EAAAC,CAAA,EAnBJC,EAkBKF,EAAAG,CAAA,EAjBJD,EAEOC,EAAAC,CAAA,qBAEPF,EAYCC,EAAAE,CAAA,8BAIHP,EAYCC,EAAAO,EAAAL,CAAA,+DApBWJ,EAAK,EAAA,CAAA,kBAGAA,EAAc,EAAA,CAAA,yDAehBA,EAAc,EAAA,CAAA,0CAtChBU,EAAA,CAAA,EAAA,GAAA,CAAA,WAAAV,KAAO,UAAU,EACvBU,EAAA,CAAA,EAAA,GAAA,CAAA,KAAAV,KAAO,IAAI,iBACbA,EAAc,EAAA,CAAA,kLAWgBA,EAAK,CAAA,CAAA,qDAKhCA,EAAO,EAAA,CAAA,6BACPA,EAAO,EAAA,CAAA,2IAeTA,EAAO,EAAA,CAAA,6BACPA,EAAO,EAAA,CAAA,oHAIoBA,EAAK,CAAA,CAAA,6vBArHlC,IAAAW,GAAM,4BAUC,OAAAC,CAKT,EAAAC,EACS,CAAA,QAAAC,EAAU,EAAE,EAAAD,GACZ,aAAAE,EAAY,EAAA,EAAAF,EACZ,CAAA,QAAAG,EAAU,EAAI,EAAAH,EACd,CAAA,MAAAI,EAAQ,CAAC,EAAAJ,EACT,CAAA,MAAAK,EAAQN,EAAO,KAAK,eAAe,CAAA,EAAAC,EACnC,CAAA,KAAAM,EAA2B,MAAS,EAAAN,EACpC,CAAA,UAAAO,EAAY,EAAI,EAAAP,EAChB,CAAA,MAAAQ,EAAuB,IAAI,EAAAR,EAC3B,CAAA,UAAAS,EAAgC,MAAS,EAAAT,GACzC,QAAAU,CAAe,EAAAV,EACf,CAAA,QAAAW,EAAU,GAAG,EAAAX,GACb,KAAAY,CAAY,EAAAZ,GACZ,WAAAa,CAAmB,EAAAb,GACnB,YAAAc,CAAoB,EAAAd,GACpB,eAAAe,CAA6B,EAAAf,EAC7B,CAAA,gBAAAgB,EAAkB,EAAK,EAAAhB,EAE9BiB,EACAC,EAEE,MAAAC,cAAiBrB,IAAG,YAEjBsB,GAAa,CACrBrB,EAAO,SAAS,QAAQ,EACnBiB,GACJjB,EAAO,SAAS,OAAO,EAGzBb,GAAW,IAAA,CACVmC,EAAA,GAAAL,EAAkB,EAAK,EACvBM,MAGQ,SAAAC,EAAeC,EAAa,CACpCzB,EAAO,SAAS,UAAWK,CAAK,WAExBqB,GAAK,CACb1B,EAAO,SAAS,UAAWK,CAAK,EAChCiB,EAAA,EAAAjB,EAAQ,KAAK,IAAI,KAAK,IAAIA,EAAOM,CAAO,EAAGC,CAAO,CAAA,WAG1CW,GAAS,CACjBI,IACAT,EAAW,iBAAiB,QAASS,CAAc,EACnDR,EAAY,iBAAiB,QAASQ,CAAc,WAE5CA,GAAc,OAChBC,EAAW,OAAOV,EAAW,KAAK,EAAI,OAAOA,EAAW,GAAG,EAC3DW,EAAU,OAAOX,EAAW,GAAG,EAAI,OAAOA,EAAW,GAAG,EACxDY,EAAID,IAAY,EAAI,EAAID,EAAWC,OACzCX,EAAW,MAAM,eAAiBY,EAAI,IAAM,SAAQZ,CAAA,EAe7B,MAAAa,EAAA,IAAA/B,EAAO,SAAS,eAAgBgB,CAAc,+EAcxDG,EAAWa,yFAgBbd,EAAUc,mpBA1CrBV,EAAA,GAAEW,EAAQ,CAAIlB,CAAW,mBAIhBM,EAAa"}
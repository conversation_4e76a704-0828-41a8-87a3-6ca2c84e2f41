const{SvelteComponent:F,append:v,attr:g,destroy_each:S,detach:a,element:h,empty:G,ensure_array_like:m,flush:b,init:H,insert:_,listen:k,noop:y,run_all:I,safe_not_equal:J,set_data:j,space:z,text:B,toggle_class:o}=window.__gradio__svelte__internal;function p(s,e,l){const t=s.slice();return t[8]=e[l],t[10]=l,t}function w(s,e,l){const t=s.slice();return t[11]=e[l],t[13]=l,t}function K(s){let e,l,t;function i(r,c){return typeof r[0]=="string"?M:L}let f=i(s),n=f(s);return{c(){e=h("div"),n.c(),g(e,"class","svelte-1cib1xd"),o(e,"table",s[1]==="table"),o(e,"gallery",s[1]==="gallery"),o(e,"selected",s[2])},m(r,c){_(r,e,c),n.m(e,null),l||(t=[k(e,"mouseenter",s[6]),k(e,"mouseleave",s[7])],l=!0)},p(r,c){f===(f=i(r))&&n?n.p(r,c):(n.d(1),n=f(r),n&&(n.c(),n.m(e,null))),c&2&&o(e,"table",r[1]==="table"),c&2&&o(e,"gallery",r[1]==="gallery"),c&4&&o(e,"selected",r[2])},d(r){r&&a(e),n.d(),l=!1,I(t)}}}function L(s){let e,l,t=m(s[0].slice(0,3)),i=[];for(let n=0;n<t.length;n+=1)i[n]=q(p(s,t,n));let f=s[0].length>3&&E(s);return{c(){e=h("table");for(let n=0;n<i.length;n+=1)i[n].c();l=z(),f&&f.c(),g(e,"class"," svelte-1cib1xd")},m(n,r){_(n,e,r);for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(e,null);v(e,l),f&&f.m(e,null)},p(n,r){if(r&1){t=m(n[0].slice(0,3));let c;for(c=0;c<t.length;c+=1){const d=p(n,t,c);i[c]?i[c].p(d,r):(i[c]=q(d),i[c].c(),i[c].m(e,l))}for(;c<i.length;c+=1)i[c].d(1);i.length=t.length}n[0].length>3?f?f.p(n,r):(f=E(n),f.c(),f.m(e,null)):f&&(f.d(1),f=null)},d(n){n&&a(e),S(i,n),f&&f.d()}}}function M(s){let e;return{c(){e=B(s[0])},m(l,t){_(l,e,t)},p(l,t){t&1&&j(e,l[0])},d(l){l&&a(e)}}}function A(s){let e,l=s[11]+"",t;return{c(){e=h("td"),t=B(l),g(e,"class","svelte-1cib1xd")},m(i,f){_(i,e,f),v(e,t)},p(i,f){f&1&&l!==(l=i[11]+"")&&j(t,l)},d(i){i&&a(e)}}}function C(s){let e;return{c(){e=h("td"),e.textContent="…",g(e,"class","svelte-1cib1xd")},m(l,t){_(l,e,t)},d(l){l&&a(e)}}}function q(s){let e,l,t=m(s[8].slice(0,3)),i=[];for(let n=0;n<t.length;n+=1)i[n]=A(w(s,t,n));let f=s[8].length>3&&C();return{c(){e=h("tr");for(let n=0;n<i.length;n+=1)i[n].c();l=z(),f&&f.c()},m(n,r){_(n,e,r);for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(e,null);v(e,l),f&&f.m(e,null)},p(n,r){if(r&1){t=m(n[8].slice(0,3));let c;for(c=0;c<t.length;c+=1){const d=w(n,t,c);i[c]?i[c].p(d,r):(i[c]=A(d),i[c].c(),i[c].m(e,l))}for(;c<i.length;c+=1)i[c].d(1);i.length=t.length}n[8].length>3?f||(f=C(),f.c(),f.m(e,null)):f&&(f.d(1),f=null)},d(n){n&&a(e),S(i,n),f&&f.d()}}}function E(s){let e;return{c(){e=h("div"),g(e,"class","overlay svelte-1cib1xd"),o(e,"odd",s[3]%2!=0),o(e,"even",s[3]%2==0),o(e,"button",s[1]==="gallery")},m(l,t){_(l,e,t)},p(l,t){t&8&&o(e,"odd",l[3]%2!=0),t&8&&o(e,"even",l[3]%2==0),t&2&&o(e,"button",l[1]==="gallery")},d(l){l&&a(e)}}}function N(s){let e,l=s[5]&&K(s);return{c(){l&&l.c(),e=G()},m(t,i){l&&l.m(t,i),_(t,e,i)},p(t,[i]){t[5]&&l.p(t,i)},i:y,o:y,d(t){t&&a(e),l&&l.d(t)}}}function O(s,e,l){let{value:t}=e,{type:i}=e,{selected:f=!1}=e,{index:n}=e,r=!1,c=Array.isArray(t);const d=()=>l(4,r=!0),D=()=>l(4,r=!1);return s.$$set=u=>{"value"in u&&l(0,t=u.value),"type"in u&&l(1,i=u.type),"selected"in u&&l(2,f=u.selected),"index"in u&&l(3,n=u.index)},[t,i,f,n,r,c,d,D]}class P extends F{constructor(e){super(),H(this,e,O,N,J,{value:0,type:1,selected:2,index:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),b()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),b()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),b()}get index(){return this.$$.ctx[3]}set index(e){this.$$set({index:e}),b()}}export{P as default};
//# sourceMappingURL=Example-BT2jlY4j.js.map

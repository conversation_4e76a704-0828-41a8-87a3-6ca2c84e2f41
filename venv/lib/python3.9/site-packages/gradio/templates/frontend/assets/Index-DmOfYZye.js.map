{"version": 3, "file": "Index-DmOfYZye.js", "sources": ["../../../../js/checkboxgroup/Index.svelte"], "sourcesContent": ["<svelte:options immutable={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { Block, BlockTitle } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: (string | number)[] = [];\n\texport let choices: [string, string | number][];\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label = gradio.i18n(\"checkbox.checkbox_group\");\n\texport let info: string | undefined = undefined;\n\texport let show_label = true;\n\n\texport let loading_status: LoadingStatus;\n\texport let interactive = true;\n\texport let old_value = value.slice();\n\n\tfunction toggle_choice(choice: string | number): void {\n\t\tif (value.includes(choice)) {\n\t\t\tvalue = value.filter((v) => v !== choice);\n\t\t} else {\n\t\t\tvalue = [...value, choice];\n\t\t}\n\t\tgradio.dispatch(\"input\");\n\t}\n\n\t$: disabled = !interactive;\n\n\t$: if (JSON.stringify(old_value) !== JSON.stringify(value)) {\n\t\told_value = value;\n\t\tgradio.dispatch(\"change\");\n\t}\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\ttype=\"fieldset\"\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n\t<div class=\"wrap\" data-testid=\"checkbox-group\">\n\t\t{#each choices as [display_value, internal_value], i}\n\t\t\t<label class:disabled class:selected={value.includes(internal_value)}>\n\t\t\t\t<input\n\t\t\t\t\t{disabled}\n\t\t\t\t\ton:change={() => toggle_choice(internal_value)}\n\t\t\t\t\ton:input={(evt) =>\n\t\t\t\t\t\tgradio.dispatch(\"select\", {\n\t\t\t\t\t\t\tindex: i,\n\t\t\t\t\t\t\tvalue: internal_value,\n\t\t\t\t\t\t\tselected: evt.currentTarget.checked\n\t\t\t\t\t\t})}\n\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\ttoggle_choice(internal_value);\n\t\t\t\t\t\t\tgradio.dispatch(\"select\", {\n\t\t\t\t\t\t\t\tindex: i,\n\t\t\t\t\t\t\t\tvalue: internal_value,\n\t\t\t\t\t\t\t\tselected: !value.includes(internal_value)\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}}\n\t\t\t\t\tchecked={value.includes(internal_value)}\n\t\t\t\t\ttype=\"checkbox\"\n\t\t\t\t\tname={internal_value?.toString()}\n\t\t\t\t\ttitle={internal_value?.toString()}\n\t\t\t\t/>\n\t\t\t\t<span class=\"ml-2\">{display_value}</span>\n\t\t\t</label>\n\t\t{/each}\n\t</div>\n</Block>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--checkbox-label-gap);\n\t}\n\tlabel {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\ttransition: var(--button-transition);\n\t\tcursor: pointer;\n\t\tbox-shadow: var(--checkbox-label-shadow);\n\t\tborder: var(--checkbox-label-border-width) solid\n\t\t\tvar(--checkbox-label-border-color);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--checkbox-label-background-fill);\n\t\tpadding: var(--checkbox-label-padding);\n\t\tcolor: var(--checkbox-label-text-color);\n\t\tfont-weight: var(--checkbox-label-text-weight);\n\t\tfont-size: var(--checkbox-label-text-size);\n\t\tline-height: var(--line-md);\n\t}\n\n\tlabel:hover {\n\t\tbackground: var(--checkbox-label-background-fill-hover);\n\t}\n\tlabel:focus {\n\t\tbackground: var(--checkbox-label-background-fill-focus);\n\t}\n\tlabel.selected {\n\t\tbackground: var(--checkbox-label-background-fill-selected);\n\t\tcolor: var(--checkbox-label-text-color-selected);\n\t}\n\n\tlabel > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\tinput {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow: var(--checkbox-shadow);\n\t\tborder: var(--checkbox-border-width) solid var(--checkbox-border-color);\n\t\tborder-radius: var(--checkbox-border-radius);\n\t\tbackground-color: var(--checkbox-background-color);\n\t\tline-height: var(--line-sm);\n\t}\n\n\tinput:checked,\n\tinput:checked:hover,\n\tinput:checked:focus {\n\t\tborder-color: var(--checkbox-border-color-selected);\n\t\tbackground-image: var(--checkbox-check);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t}\n\n\tinput:checked:focus {\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t\tbackground-image: var(--checkbox-check);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t}\n\n\tinput:hover {\n\t\tborder-color: var(--checkbox-border-color-hover);\n\t\tbackground-color: var(--checkbox-background-color-hover);\n\t}\n\n\tinput:not(:checked):focus {\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t}\n\n\tinput[disabled],\n\t.disabled {\n\t\tcursor: not-allowed;\n\t}\n</style>\n"], "names": ["ctx", "attr", "input", "input_name_value", "input_title_value", "insert", "target", "label_1", "anchor", "append", "span", "dirty", "set_data", "t1", "t1_value", "div", "i", "gradio", "$$props", "elem_id", "elem_classes", "visible", "value", "choices", "container", "scale", "min_width", "label", "info", "show_label", "loading_status", "interactive", "old_value", "toggle_choice", "choice", "v", "clear_status_handler", "change_handler", "internal_value", "input_handler", "evt", "event", "$$invalidate", "disabled"], "mappings": "orBA8DkCA,EAAK,CAAA,CAAA,sCAALA,EAAK,CAAA,CAAA,oDA6BfA,EAAa,EAAA,EAAA,6OALvBA,EAAK,CAAA,EAAC,SAASA,EAAc,EAAA,CAAA,yBAEhCC,EAAAC,EAAA,OAAAC,EAAAH,OAAgB,SAAQ,CAAA,EACvBC,EAAAC,EAAA,QAAAE,EAAAJ,OAAgB,SAAQ,CAAA,sIAvBKA,EAAK,CAAA,EAAC,SAASA,EAAc,EAAA,CAAA,CAAA,UAAnEK,EA0BOC,EAAAC,EAAAC,CAAA,EAzBNC,EAuBCF,EAAAL,CAAA,SACDO,EAAwCF,EAAAG,CAAA,kIAL9BV,EAAK,CAAA,EAAC,SAASA,EAAc,EAAA,CAAA,kBAEhCW,EAAA,IAAAR,KAAAA,EAAAH,OAAgB,SAAQ,kBACvBW,EAAA,IAAAP,KAAAA,EAAAJ,OAAgB,SAAQ,gCAEZA,EAAa,EAAA,EAAA,KAAAY,EAAAC,EAAAC,CAAA,qDAzBId,EAAK,CAAA,EAAC,SAASA,EAAc,EAAA,CAAA,CAAA,qEATxD,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,iMAMXA,EAAO,CAAA,CAAA,uBAAZ,OAAI,GAAA,+OADPK,EA8BKC,EAAAS,EAAAP,CAAA,oFArCQG,EAAA,GAAA,CAAA,WAAAX,KAAO,UAAU,EACvBW,EAAA,GAAA,CAAA,KAAAX,KAAO,IAAI,YACbA,EAAc,EAAA,CAAA,kJAMXA,EAAO,CAAA,CAAA,oBAAZ,OAAIgB,GAAA,EAAA,iHAAJ,2sBAzDQ,OAAAC,CAKT,EAAAC,EACS,CAAA,QAAAC,EAAU,EAAE,EAAAD,GACZ,aAAAE,EAAY,EAAA,EAAAF,EACZ,CAAA,QAAAG,EAAU,EAAI,EAAAH,GACd,MAAAI,EAAK,EAAA,EAAAJ,GACL,QAAAK,CAAoC,EAAAL,EACpC,CAAA,UAAAM,EAAY,EAAI,EAAAN,EAChB,CAAA,MAAAO,EAAuB,IAAI,EAAAP,EAC3B,CAAA,UAAAQ,EAAgC,MAAS,EAAAR,EACzC,CAAA,MAAAS,EAAQV,EAAO,KAAK,yBAAyB,CAAA,EAAAC,EAC7C,CAAA,KAAAU,EAA2B,MAAS,EAAAV,EACpC,CAAA,WAAAW,EAAa,EAAI,EAAAX,GAEjB,eAAAY,CAA6B,EAAAZ,EAC7B,CAAA,YAAAa,EAAc,EAAI,EAAAb,GAClB,UAAAc,EAAYV,EAAM,MAAK,CAAA,EAAAJ,EAEzB,SAAAe,EAAcC,EAAuB,CACzCZ,EAAM,SAASY,CAAM,MACxBZ,EAAQA,EAAM,OAAQa,GAAMA,IAAMD,CAAM,CAAA,MAExCZ,EAAK,CAAA,GAAOA,EAAOY,CAAM,CAAA,EAE1BjB,EAAO,SAAS,OAAO,EAwBA,MAAAmB,EAAA,IAAAnB,EAAO,SAAS,eAAgBa,CAAc,EASjDO,EAAAC,GAAAL,EAAcK,CAAc,EAClCC,EAAA,CAAAvB,EAAAsB,EAAAE,IACVvB,EAAO,SAAS,SAAQ,CACvB,MAAOD,EACP,MAAOsB,EACP,SAAUE,EAAI,cAAc,iBAEjBC,IAAK,CACbA,EAAM,MAAQ,UACjBR,EAAcK,CAAc,EAC5BrB,EAAO,SAAS,SAAQ,CACvB,MAAOD,EACP,MAAOsB,EACP,SAAW,CAAAhB,EAAM,SAASgB,CAAc,gkBA3C9CI,EAAA,GAAEC,EAAQ,CAAIZ,CAAW,oBAEnB,KAAK,UAAUC,CAAS,IAAM,KAAK,UAAUV,CAAK,IACxDoB,EAAA,GAAAV,EAAYV,CAAK,EACjBL,EAAO,SAAS,QAAQ"}
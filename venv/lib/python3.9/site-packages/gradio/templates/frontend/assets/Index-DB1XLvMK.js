const __vite__fileDeps=["./Blocks-CyfcXtBq.js","./index-BQPjLIsY.js","./index-BOW2xVAS.css","./Button-BIUaXfcG.js","./Button-CTZL5Nos.css","./Blocks-yLdzXwzS.css","./Login-C6lN_nrD.js","./Index-DDCF2BFd.js","./Index-B0JJ6p9c.css","./Textbox-BxQ_qaNA.js","./BlockTitle-CXNngU7y.js","./Info-CrBVEpWV.js","./Check-CZUQOzJl.js","./Copy-B6RcHnoK.js","./Textbox-D8IAzrZj.css","./Index-CuoXAbPt.js","./Index-CptIZeFZ.css","./Login-BCwzjozv.css","./Example-Cj3ii62O.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{n as Hi,$ as ri,w as ai,s as Wi,m as Bi,p as En,_ as An}from"./index-BQPjLIsY.js";const Gi="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='10'%20height='10'%20fill='none'%3e%3cpath%20fill='%23FF3270'%20d='M1.93%206.03v2.04h2.04V6.03H1.93Z'/%3e%3cpath%20fill='%23861FFF'%20d='M6.03%206.03v2.04h2.04V6.03H6.03Z'/%3e%3cpath%20fill='%23097EFF'%20d='M1.93%201.93v2.04h2.04V1.93H1.93Z'/%3e%3cpath%20fill='%23000'%20fill-rule='evenodd'%20d='M.5%201.4c0-.5.4-.9.9-.9h3.1a.9.9%200%200%201%20.87.67A2.44%202.44%200%200%201%209.5%202.95c0%20.65-.25%201.24-.67%***********.67.46.67.88v3.08c0%20.5-.4.91-.9.91H1.4a.9.9%200%200%201-.9-.9V1.4Zm1.43.53v2.04h2.04V1.93H1.93Zm0%206.14V6.03h2.04v2.04H1.93Zm4.1%200V6.03h2.04v2.04H6.03Zm0-5.12a1.02%201.02%200%201%201%202.04%200%201.02%201.02%200%200%201-2.04%200Z'%20clip-rule='evenodd'/%3e%3cpath%20fill='%23FFD702'%20d='M7.05%201.93a1.02%201.02%200%201%200%200%202.04%201.02%201.02%200%200%200%200-2.04Z'/%3e%3c/svg%3e",fi=typeof window<"u";let Tn=fi?()=>window.performance.now():()=>Date.now(),ci=fi?n=>requestAnimationFrame(n):Hi;const ot=new Set;function ui(n){ot.forEach(e=>{e.c(n)||(ot.delete(e),e.f())}),ot.size!==0&&ci(ui)}function qi(n){let e;return ot.size===0&&ci(ui),{promise:new Promise(t=>{ot.add(e={c:n,f:t})}),abort(){ot.delete(e)}}}const{SvelteComponent:Vi,append:j,attr:x,binding_callbacks:Yi,component_subscribe:Zi,create_slot:ji,detach:di,element:Ie,flush:De,get_all_dirty_from_scope:Xi,get_slot_changes:Ki,init:xi,insert:_i,safe_not_equal:Ji,set_data:qt,set_style:Et,space:ut,text:At,toggle_class:Ge,transition_in:Qi,transition_out:$i,update_slot_base:el}=window.__gradio__svelte__internal;function kn(n){let e,t,i,l,o,s,r,u=n[9]("common.built_with")+"",a,f,d,h,y,T,M=n[9]("common.hosted_on")+"",w,P,b;return{c(){e=Ie("div"),t=Ie("span"),i=Ie("a"),l=At(n[5]),s=ut(),r=Ie("span"),a=At(u),f=ut(),d=Ie("a"),d.textContent="Gradio",h=At("."),y=ut(),T=Ie("span"),w=At(M),P=ut(),b=Ie("a"),b.innerHTML=`<span class="space-logo svelte-wpkpf6"><img src="${Gi}" alt="Hugging Face Space" class="svelte-wpkpf6"/></span> Spaces`,x(i,"href",o="https://huggingface.co/spaces/"+n[5]),x(i,"class","title svelte-wpkpf6"),x(t,"class","svelte-wpkpf6"),x(d,"class","gradio svelte-wpkpf6"),x(d,"href","https://gradio.app"),x(r,"class","svelte-wpkpf6"),x(b,"class","hf svelte-wpkpf6"),x(b,"href","https://huggingface.co/spaces"),x(T,"class","svelte-wpkpf6"),x(e,"class","info svelte-wpkpf6")},m(m,k){_i(m,e,k),j(e,t),j(t,i),j(i,l),j(e,s),j(e,r),j(r,a),j(r,f),j(r,d),j(r,h),j(e,y),j(e,T),j(T,w),j(T,P),j(T,b)},p(m,k){k&32&&qt(l,m[5]),k&32&&o!==(o="https://huggingface.co/spaces/"+m[5])&&x(i,"href",o),k&512&&u!==(u=m[9]("common.built_with")+"")&&qt(a,u),k&512&&M!==(M=m[9]("common.hosted_on")+"")&&qt(w,M)},d(m){m&&di(e)}}}function tl(n){let e,t,i,l,o;const s=n[11].default,r=ji(s,n,n[10],null);let u=n[6]&&n[5]&&n[7]&&kn(n);return{c(){e=Ie("div"),t=Ie("div"),r&&r.c(),i=ut(),u&&u.c(),x(t,"class","main svelte-wpkpf6"),x(e,"class",l="gradio-container gradio-container-"+n[1]+" svelte-wpkpf6"),x(e,"data-iframe-height",""),Ge(e,"app",!n[6]&&!n[4]),Ge(e,"fill_width",n[3]),Ge(e,"embed-container",n[6]),Ge(e,"with-info",n[7]),Et(e,"min-height",n[8]?"initial":n[2]),Et(e,"flex-grow",n[6]?"auto":"1")},m(a,f){_i(a,e,f),j(e,t),r&&r.m(t,null),j(e,i),u&&u.m(e,null),n[12](e),o=!0},p(a,[f]){r&&r.p&&(!o||f&1024)&&el(r,s,a,a[10],o?Ki(s,a[10],f,null):Xi(a[10]),null),a[6]&&a[5]&&a[7]?u?u.p(a,f):(u=kn(a),u.c(),u.m(e,null)):u&&(u.d(1),u=null),(!o||f&2&&l!==(l="gradio-container gradio-container-"+a[1]+" svelte-wpkpf6"))&&x(e,"class",l),(!o||f&82)&&Ge(e,"app",!a[6]&&!a[4]),(!o||f&10)&&Ge(e,"fill_width",a[3]),(!o||f&66)&&Ge(e,"embed-container",a[6]),(!o||f&130)&&Ge(e,"with-info",a[7]),f&260&&Et(e,"min-height",a[8]?"initial":a[2]),f&64&&Et(e,"flex-grow",a[6]?"auto":"1")},i(a){o||(Qi(r,a),o=!0)},o(a){$i(r,a),o=!1},d(a){a&&di(e),r&&r.d(a),u&&u.d(),n[12](null)}}}function nl(n,e,t){let i;Zi(n,ri,w=>t(9,i=w));let{$$slots:l={},$$scope:o}=e,{wrapper:s}=e,{version:r}=e,{initial_height:u}=e,{fill_width:a}=e,{is_embed:f}=e,{space:d}=e,{display:h}=e,{info:y}=e,{loaded:T}=e;function M(w){Yi[w?"unshift":"push"](()=>{s=w,t(0,s)})}return n.$$set=w=>{"wrapper"in w&&t(0,s=w.wrapper),"version"in w&&t(1,r=w.version),"initial_height"in w&&t(2,u=w.initial_height),"fill_width"in w&&t(3,a=w.fill_width),"is_embed"in w&&t(4,f=w.is_embed),"space"in w&&t(5,d=w.space),"display"in w&&t(6,h=w.display),"info"in w&&t(7,y=w.info),"loaded"in w&&t(8,T=w.loaded),"$$scope"in w&&t(10,o=w.$$scope)},[s,r,u,a,f,d,h,y,T,i,o,l,M]}class il extends Vi{constructor(e){super(),xi(this,e,nl,tl,Ji,{wrapper:0,version:1,initial_height:2,fill_width:3,is_embed:4,space:5,display:6,info:7,loaded:8})}get wrapper(){return this.$$.ctx[0]}set wrapper(e){this.$$set({wrapper:e}),De()}get version(){return this.$$.ctx[1]}set version(e){this.$$set({version:e}),De()}get initial_height(){return this.$$.ctx[2]}set initial_height(e){this.$$set({initial_height:e}),De()}get fill_width(){return this.$$.ctx[3]}set fill_width(e){this.$$set({fill_width:e}),De()}get is_embed(){return this.$$.ctx[4]}set is_embed(e){this.$$set({is_embed:e}),De()}get space(){return this.$$.ctx[5]}set space(e){this.$$set({space:e}),De()}get display(){return this.$$.ctx[6]}set display(e){this.$$set({display:e}),De()}get info(){return this.$$.ctx[7]}set info(e){this.$$set({info:e}),De()}get loaded(){return this.$$.ctx[8]}set loaded(e){this.$$set({loaded:e}),De()}}function st(n){let e=["","k","M","G","T","P","E","Z"],t=0;for(;n>1e3&&t<e.length-1;)n/=1e3,t++;let i=e[t];return(Number.isInteger(n)?n:n.toFixed(1))+i}function vn(n){return Object.prototype.toString.call(n)==="[object Date]"}function Qt(n,e,t,i){if(typeof t=="number"||vn(t)){const l=i-t,o=(t-e)/(n.dt||1/60),s=n.opts.stiffness*l,r=n.opts.damping*o,u=(s-r)*n.inv_mass,a=(o+u)*n.dt;return Math.abs(a)<n.opts.precision&&Math.abs(l)<n.opts.precision?i:(n.settled=!1,vn(t)?new Date(t.getTime()+a):t+a)}else{if(Array.isArray(t))return t.map((l,o)=>Qt(n,e[o],t[o],i[o]));if(typeof t=="object"){const l={};for(const o in t)l[o]=Qt(n,e[o],t[o],i[o]);return l}else throw new Error(`Cannot spring ${typeof t} values`)}}function Cn(n,e={}){const t=ai(n),{stiffness:i=.15,damping:l=.8,precision:o=.01}=e;let s,r,u,a=n,f=n,d=1,h=0,y=!1;function T(w,P={}){f=w;const b=u={};return n==null||P.hard||M.stiffness>=1&&M.damping>=1?(y=!0,s=Tn(),a=w,t.set(n=f),Promise.resolve()):(P.soft&&(h=1/((P.soft===!0?.5:+P.soft)*60),d=0),r||(s=Tn(),y=!1,r=qi(m=>{if(y)return y=!1,r=null,!1;d=Math.min(d+h,1);const k={inv_mass:d,opts:M,settled:!0,dt:(m-s)*60/1e3},V=Qt(k,a,n,f);return s=m,a=n,t.set(n=V),k.settled&&(r=null),!k.settled})),new Promise(m=>{r.promise.then(()=>{b===u&&m()})}))}const M={set:T,update:(w,P)=>T(w(f,n),P),subscribe:t.subscribe,stiffness:i,damping:l,precision:o};return M}const{SvelteComponent:ll,append:ce,attr:S,component_subscribe:Ln,detach:sl,element:ol,flush:rl,init:al,insert:fl,noop:Sn,safe_not_equal:cl,set_style:Tt,svg_element:ue,toggle_class:Rn}=window.__gradio__svelte__internal,{onMount:ul}=window.__gradio__svelte__internal;function dl(n){let e,t,i,l,o,s,r,u,a,f,d,h;return{c(){e=ol("div"),t=ue("svg"),i=ue("g"),l=ue("path"),o=ue("path"),s=ue("path"),r=ue("path"),u=ue("g"),a=ue("path"),f=ue("path"),d=ue("path"),h=ue("path"),S(l,"d","M255.926 0.754768L509.702 139.936V221.027L255.926 81.8465V0.754768Z"),S(l,"fill","#FF7C00"),S(l,"fill-opacity","0.4"),S(l,"class","svelte-zyxd38"),S(o,"d","M509.69 139.936L254.981 279.641V361.255L509.69 221.55V139.936Z"),S(o,"fill","#FF7C00"),S(o,"class","svelte-zyxd38"),S(s,"d","M0.250138 139.937L254.981 279.641V361.255L0.250138 221.55V139.937Z"),S(s,"fill","#FF7C00"),S(s,"fill-opacity","0.4"),S(s,"class","svelte-zyxd38"),S(r,"d","M255.923 0.232622L0.236328 139.936V221.55L255.923 81.8469V0.232622Z"),S(r,"fill","#FF7C00"),S(r,"class","svelte-zyxd38"),Tt(i,"transform","translate("+n[1][0]+"px, "+n[1][1]+"px)"),S(a,"d","M255.926 141.5L509.702 280.681V361.773L255.926 222.592V141.5Z"),S(a,"fill","#FF7C00"),S(a,"fill-opacity","0.4"),S(a,"class","svelte-zyxd38"),S(f,"d","M509.69 280.679L254.981 420.384V501.998L509.69 362.293V280.679Z"),S(f,"fill","#FF7C00"),S(f,"class","svelte-zyxd38"),S(d,"d","M0.250138 280.681L254.981 420.386V502L0.250138 362.295V280.681Z"),S(d,"fill","#FF7C00"),S(d,"fill-opacity","0.4"),S(d,"class","svelte-zyxd38"),S(h,"d","M255.923 140.977L0.236328 280.68V362.294L255.923 222.591V140.977Z"),S(h,"fill","#FF7C00"),S(h,"class","svelte-zyxd38"),Tt(u,"transform","translate("+n[2][0]+"px, "+n[2][1]+"px)"),S(t,"viewBox","-1200 -1200 3000 3000"),S(t,"fill","none"),S(t,"xmlns","http://www.w3.org/2000/svg"),S(t,"class","svelte-zyxd38"),S(e,"class","svelte-zyxd38"),Rn(e,"margin",n[0])},m(y,T){fl(y,e,T),ce(e,t),ce(t,i),ce(i,l),ce(i,o),ce(i,s),ce(i,r),ce(t,u),ce(u,a),ce(u,f),ce(u,d),ce(u,h)},p(y,[T]){T&2&&Tt(i,"transform","translate("+y[1][0]+"px, "+y[1][1]+"px)"),T&4&&Tt(u,"transform","translate("+y[2][0]+"px, "+y[2][1]+"px)"),T&1&&Rn(e,"margin",y[0])},i:Sn,o:Sn,d(y){y&&sl(e)}}}function _l(n,e,t){let i,l,{margin:o=!0}=e;const s=Cn([0,0]);Ln(n,s,h=>t(1,i=h));const r=Cn([0,0]);Ln(n,r,h=>t(2,l=h));let u;async function a(){await Promise.all([s.set([125,140]),r.set([-125,-140])]),await Promise.all([s.set([-125,140]),r.set([125,-140])]),await Promise.all([s.set([-125,0]),r.set([125,-0])]),await Promise.all([s.set([125,0]),r.set([-125,0])])}async function f(){await a(),u||f()}async function d(){await Promise.all([s.set([125,0]),r.set([-125,0])]),f()}return ul(()=>(d(),()=>u=!0)),n.$$set=h=>{"margin"in h&&t(0,o=h.margin)},[o,i,l,s,r]}class ml extends ll{constructor(e){super(),al(this,e,_l,dl,cl,{margin:0})}get margin(){return this.$$.ctx[0]}set margin(e){this.$$set({margin:e}),rl()}}const{SvelteComponent:pl,append:$t,attr:Me,bubble:hl,create_component:gl,destroy_component:bl,detach:mi,element:en,flush:te,init:wl,insert:pi,listen:yl,mount_component:El,safe_not_equal:Al,set_data:Tl,set_style:it,space:kl,text:vl,toggle_class:Q,transition_in:Cl,transition_out:Ll}=window.__gradio__svelte__internal;function Nn(n){let e,t;return{c(){e=en("span"),t=vl(n[1]),Me(e,"class","svelte-rk35yg")},m(i,l){pi(i,e,l),$t(e,t)},p(i,l){l&2&&Tl(t,i[1])},d(i){i&&mi(e)}}}function Sl(n){let e,t,i,l,o,s,r,u=n[2]&&Nn(n);return l=new n[0]({}),{c(){e=en("button"),u&&u.c(),t=kl(),i=en("div"),gl(l.$$.fragment),Me(i,"class","svelte-rk35yg"),Q(i,"small",n[4]==="small"),Q(i,"large",n[4]==="large"),Q(i,"medium",n[4]==="medium"),e.disabled=n[7],Me(e,"aria-label",n[1]),Me(e,"aria-haspopup",n[8]),Me(e,"title",n[1]),Me(e,"class","svelte-rk35yg"),Q(e,"pending",n[3]),Q(e,"padded",n[5]),Q(e,"highlight",n[6]),Q(e,"transparent",n[9]),it(e,"color",!n[7]&&n[12]?n[12]:"var(--block-label-text-color)"),it(e,"--bg-color",n[7]?"auto":n[10]),it(e,"margin-left",n[11]+"px")},m(a,f){pi(a,e,f),u&&u.m(e,null),$t(e,t),$t(e,i),El(l,i,null),o=!0,s||(r=yl(e,"click",n[14]),s=!0)},p(a,[f]){a[2]?u?u.p(a,f):(u=Nn(a),u.c(),u.m(e,t)):u&&(u.d(1),u=null),(!o||f&16)&&Q(i,"small",a[4]==="small"),(!o||f&16)&&Q(i,"large",a[4]==="large"),(!o||f&16)&&Q(i,"medium",a[4]==="medium"),(!o||f&128)&&(e.disabled=a[7]),(!o||f&2)&&Me(e,"aria-label",a[1]),(!o||f&256)&&Me(e,"aria-haspopup",a[8]),(!o||f&2)&&Me(e,"title",a[1]),(!o||f&8)&&Q(e,"pending",a[3]),(!o||f&32)&&Q(e,"padded",a[5]),(!o||f&64)&&Q(e,"highlight",a[6]),(!o||f&512)&&Q(e,"transparent",a[9]),f&4224&&it(e,"color",!a[7]&&a[12]?a[12]:"var(--block-label-text-color)"),f&1152&&it(e,"--bg-color",a[7]?"auto":a[10]),f&2048&&it(e,"margin-left",a[11]+"px")},i(a){o||(Cl(l.$$.fragment,a),o=!0)},o(a){Ll(l.$$.fragment,a),o=!1},d(a){a&&mi(e),u&&u.d(),bl(l),s=!1,r()}}}function Rl(n,e,t){let i,{Icon:l}=e,{label:o=""}=e,{show_label:s=!1}=e,{pending:r=!1}=e,{size:u="small"}=e,{padded:a=!0}=e,{highlight:f=!1}=e,{disabled:d=!1}=e,{hasPopup:h=!1}=e,{color:y="var(--block-label-text-color)"}=e,{transparent:T=!1}=e,{background:M="var(--background-fill-primary)"}=e,{offset:w=0}=e;function P(b){hl.call(this,n,b)}return n.$$set=b=>{"Icon"in b&&t(0,l=b.Icon),"label"in b&&t(1,o=b.label),"show_label"in b&&t(2,s=b.show_label),"pending"in b&&t(3,r=b.pending),"size"in b&&t(4,u=b.size),"padded"in b&&t(5,a=b.padded),"highlight"in b&&t(6,f=b.highlight),"disabled"in b&&t(7,d=b.disabled),"hasPopup"in b&&t(8,h=b.hasPopup),"color"in b&&t(13,y=b.color),"transparent"in b&&t(9,T=b.transparent),"background"in b&&t(10,M=b.background),"offset"in b&&t(11,w=b.offset)},n.$$.update=()=>{n.$$.dirty&8256&&t(12,i=f?"var(--color-accent)":y)},[l,o,s,r,u,a,f,d,h,T,M,w,i,y,P]}class Nl extends pl{constructor(e){super(),wl(this,e,Rl,Sl,Al,{Icon:0,label:1,show_label:2,pending:3,size:4,padded:5,highlight:6,disabled:7,hasPopup:8,color:13,transparent:9,background:10,offset:11})}get Icon(){return this.$$.ctx[0]}set Icon(e){this.$$set({Icon:e}),te()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),te()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),te()}get pending(){return this.$$.ctx[3]}set pending(e){this.$$set({pending:e}),te()}get size(){return this.$$.ctx[4]}set size(e){this.$$set({size:e}),te()}get padded(){return this.$$.ctx[5]}set padded(e){this.$$set({padded:e}),te()}get highlight(){return this.$$.ctx[6]}set highlight(e){this.$$set({highlight:e}),te()}get disabled(){return this.$$.ctx[7]}set disabled(e){this.$$set({disabled:e}),te()}get hasPopup(){return this.$$.ctx[8]}set hasPopup(e){this.$$set({hasPopup:e}),te()}get color(){return this.$$.ctx[13]}set color(e){this.$$set({color:e}),te()}get transparent(){return this.$$.ctx[9]}set transparent(e){this.$$set({transparent:e}),te()}get background(){return this.$$.ctx[10]}set background(e){this.$$set({background:e}),te()}get offset(){return this.$$.ctx[11]}set offset(e){this.$$set({offset:e}),te()}}const{SvelteComponent:Dl,append:Vt,attr:de,detach:Ml,init:Il,insert:Ol,noop:Yt,safe_not_equal:Pl,set_style:Ee,svg_element:kt}=window.__gradio__svelte__internal;function Fl(n){let e,t,i,l;return{c(){e=kt("svg"),t=kt("g"),i=kt("path"),l=kt("path"),de(i,"d","M18,6L6.087,17.913"),Ee(i,"fill","none"),Ee(i,"fill-rule","nonzero"),Ee(i,"stroke-width","2px"),de(t,"transform","matrix(1.14096,-0.140958,-0.140958,1.14096,-0.0559523,0.0559523)"),de(l,"d","M4.364,4.364L19.636,19.636"),Ee(l,"fill","none"),Ee(l,"fill-rule","nonzero"),Ee(l,"stroke-width","2px"),de(e,"width","100%"),de(e,"height","100%"),de(e,"viewBox","0 0 24 24"),de(e,"version","1.1"),de(e,"xmlns","http://www.w3.org/2000/svg"),de(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),de(e,"xml:space","preserve"),de(e,"stroke","currentColor"),Ee(e,"fill-rule","evenodd"),Ee(e,"clip-rule","evenodd"),Ee(e,"stroke-linecap","round"),Ee(e,"stroke-linejoin","round")},m(o,s){Ol(o,e,s),Vt(e,t),Vt(t,i),Vt(e,l)},p:Yt,i:Yt,o:Yt,d(o){o&&Ml(e)}}}class Ul extends Dl{constructor(e){super(),Il(this,e,null,Fl,Pl,{})}}const yo=["red","green","blue","yellow","purple","teal","orange","cyan","lime","pink"],zl=[{color:"red",primary:600,secondary:100},{color:"green",primary:600,secondary:100},{color:"blue",primary:600,secondary:100},{color:"yellow",primary:500,secondary:100},{color:"purple",primary:600,secondary:100},{color:"teal",primary:600,secondary:100},{color:"orange",primary:600,secondary:100},{color:"cyan",primary:600,secondary:100},{color:"lime",primary:500,secondary:100},{color:"pink",primary:600,secondary:100}],Dn={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"}},Eo=zl.reduce((n,{color:e,primary:t,secondary:i})=>({...n,[e]:{primary:Dn[e][t],secondary:Dn[e][i]}}),{}),{SvelteComponent:Hl,append:Xe,attr:pe,binding_callbacks:Mn,check_outros:tn,create_component:hi,create_slot:gi,destroy_component:bi,destroy_each:wi,detach:v,element:Ae,empty:rt,ensure_array_like:Mt,flush:Y,get_all_dirty_from_scope:yi,get_slot_changes:Ei,group_outros:nn,init:Wl,insert:C,mount_component:Ai,noop:ln,safe_not_equal:Bl,set_data:ae,set_style:qe,space:re,text:H,toggle_class:oe,transition_in:me,transition_out:Te,update_slot_base:Ti}=window.__gradio__svelte__internal,{tick:Gl}=window.__gradio__svelte__internal,{onDestroy:ql}=window.__gradio__svelte__internal,{createEventDispatcher:Vl}=window.__gradio__svelte__internal,Yl=n=>({}),In=n=>({}),Zl=n=>({}),On=n=>({});function Pn(n,e,t){const i=n.slice();return i[40]=e[t],i[42]=t,i}function Fn(n,e,t){const i=n.slice();return i[40]=e[t],i}function jl(n){let e,t,i,l,o=n[1]("common.error")+"",s,r,u;t=new Nl({props:{Icon:Ul,label:n[1]("common.clear"),disabled:!1}}),t.$on("click",n[32]);const a=n[30].error,f=gi(a,n,n[29],In);return{c(){e=Ae("div"),hi(t.$$.fragment),i=re(),l=Ae("span"),s=H(o),r=re(),f&&f.c(),pe(e,"class","clear-status svelte-au1olv"),pe(l,"class","error svelte-au1olv")},m(d,h){C(d,e,h),Ai(t,e,null),C(d,i,h),C(d,l,h),Xe(l,s),C(d,r,h),f&&f.m(d,h),u=!0},p(d,h){const y={};h[0]&2&&(y.label=d[1]("common.clear")),t.$set(y),(!u||h[0]&2)&&o!==(o=d[1]("common.error")+"")&&ae(s,o),f&&f.p&&(!u||h[0]&536870912)&&Ti(f,a,d,d[29],u?Ei(a,d[29],h,Yl):yi(d[29]),In)},i(d){u||(me(t.$$.fragment,d),me(f,d),u=!0)},o(d){Te(t.$$.fragment,d),Te(f,d),u=!1},d(d){d&&(v(e),v(i),v(l),v(r)),bi(t),f&&f.d(d)}}}function Xl(n){let e,t,i,l,o,s,r,u,a,f=n[8]==="default"&&n[18]&&n[6]==="full"&&Un(n);function d(m,k){if(m[7])return Jl;if(m[2]!==null&&m[3]!==void 0&&m[2]>=0)return xl;if(m[2]===0)return Kl}let h=d(n),y=h&&h(n),T=n[5]&&Wn(n);const M=[ts,es],w=[];function P(m,k){return m[15]!=null?0:m[6]==="full"?1:-1}~(o=P(n))&&(s=w[o]=M[o](n));let b=!n[5]&&jn(n);return{c(){f&&f.c(),e=re(),t=Ae("div"),y&&y.c(),i=re(),T&&T.c(),l=re(),s&&s.c(),r=re(),b&&b.c(),u=rt(),pe(t,"class","progress-text svelte-au1olv"),oe(t,"meta-text-center",n[8]==="center"),oe(t,"meta-text",n[8]==="default")},m(m,k){f&&f.m(m,k),C(m,e,k),C(m,t,k),y&&y.m(t,null),Xe(t,i),T&&T.m(t,null),C(m,l,k),~o&&w[o].m(m,k),C(m,r,k),b&&b.m(m,k),C(m,u,k),a=!0},p(m,k){m[8]==="default"&&m[18]&&m[6]==="full"?f?f.p(m,k):(f=Un(m),f.c(),f.m(e.parentNode,e)):f&&(f.d(1),f=null),h===(h=d(m))&&y?y.p(m,k):(y&&y.d(1),y=h&&h(m),y&&(y.c(),y.m(t,i))),m[5]?T?T.p(m,k):(T=Wn(m),T.c(),T.m(t,null)):T&&(T.d(1),T=null),(!a||k[0]&256)&&oe(t,"meta-text-center",m[8]==="center"),(!a||k[0]&256)&&oe(t,"meta-text",m[8]==="default");let V=o;o=P(m),o===V?~o&&w[o].p(m,k):(s&&(nn(),Te(w[V],1,1,()=>{w[V]=null}),tn()),~o?(s=w[o],s?s.p(m,k):(s=w[o]=M[o](m),s.c()),me(s,1),s.m(r.parentNode,r)):s=null),m[5]?b&&(nn(),Te(b,1,1,()=>{b=null}),tn()):b?(b.p(m,k),k[0]&32&&me(b,1)):(b=jn(m),b.c(),me(b,1),b.m(u.parentNode,u))},i(m){a||(me(s),me(b),a=!0)},o(m){Te(s),Te(b),a=!1},d(m){m&&(v(e),v(t),v(l),v(r),v(u)),f&&f.d(m),y&&y.d(),T&&T.d(),~o&&w[o].d(m),b&&b.d(m)}}}function Un(n){let e,t=`translateX(${(n[17]||0)*100-100}%)`;return{c(){e=Ae("div"),pe(e,"class","eta-bar svelte-au1olv"),qe(e,"transform",t)},m(i,l){C(i,e,l)},p(i,l){l[0]&131072&&t!==(t=`translateX(${(i[17]||0)*100-100}%)`)&&qe(e,"transform",t)},d(i){i&&v(e)}}}function Kl(n){let e;return{c(){e=H("processing |")},m(t,i){C(t,e,i)},p:ln,d(t){t&&v(e)}}}function xl(n){let e,t=n[2]+1+"",i,l,o,s;return{c(){e=H("queue: "),i=H(t),l=H("/"),o=H(n[3]),s=H(" |")},m(r,u){C(r,e,u),C(r,i,u),C(r,l,u),C(r,o,u),C(r,s,u)},p(r,u){u[0]&4&&t!==(t=r[2]+1+"")&&ae(i,t),u[0]&8&&ae(o,r[3])},d(r){r&&(v(e),v(i),v(l),v(o),v(s))}}}function Jl(n){let e,t=Mt(n[7]),i=[];for(let l=0;l<t.length;l+=1)i[l]=Hn(Fn(n,t,l));return{c(){for(let l=0;l<i.length;l+=1)i[l].c();e=rt()},m(l,o){for(let s=0;s<i.length;s+=1)i[s]&&i[s].m(l,o);C(l,e,o)},p(l,o){if(o[0]&128){t=Mt(l[7]);let s;for(s=0;s<t.length;s+=1){const r=Fn(l,t,s);i[s]?i[s].p(r,o):(i[s]=Hn(r),i[s].c(),i[s].m(e.parentNode,e))}for(;s<i.length;s+=1)i[s].d(1);i.length=t.length}},d(l){l&&v(e),wi(i,l)}}}function zn(n){let e,t=n[40].unit+"",i,l,o=" ",s;function r(f,d){return f[40].length!=null?$l:Ql}let u=r(n),a=u(n);return{c(){a.c(),e=re(),i=H(t),l=H(" | "),s=H(o)},m(f,d){a.m(f,d),C(f,e,d),C(f,i,d),C(f,l,d),C(f,s,d)},p(f,d){u===(u=r(f))&&a?a.p(f,d):(a.d(1),a=u(f),a&&(a.c(),a.m(e.parentNode,e))),d[0]&128&&t!==(t=f[40].unit+"")&&ae(i,t)},d(f){f&&(v(e),v(i),v(l),v(s)),a.d(f)}}}function Ql(n){let e=st(n[40].index||0)+"",t;return{c(){t=H(e)},m(i,l){C(i,t,l)},p(i,l){l[0]&128&&e!==(e=st(i[40].index||0)+"")&&ae(t,e)},d(i){i&&v(t)}}}function $l(n){let e=st(n[40].index||0)+"",t,i,l=st(n[40].length)+"",o;return{c(){t=H(e),i=H("/"),o=H(l)},m(s,r){C(s,t,r),C(s,i,r),C(s,o,r)},p(s,r){r[0]&128&&e!==(e=st(s[40].index||0)+"")&&ae(t,e),r[0]&128&&l!==(l=st(s[40].length)+"")&&ae(o,l)},d(s){s&&(v(t),v(i),v(o))}}}function Hn(n){let e,t=n[40].index!=null&&zn(n);return{c(){t&&t.c(),e=rt()},m(i,l){t&&t.m(i,l),C(i,e,l)},p(i,l){i[40].index!=null?t?t.p(i,l):(t=zn(i),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(i){i&&v(e),t&&t.d(i)}}}function Wn(n){let e,t=n[0]?`/${n[19]}`:"",i,l;return{c(){e=H(n[20]),i=H(t),l=H("s")},m(o,s){C(o,e,s),C(o,i,s),C(o,l,s)},p(o,s){s[0]&1048576&&ae(e,o[20]),s[0]&524289&&t!==(t=o[0]?`/${o[19]}`:"")&&ae(i,t)},d(o){o&&(v(e),v(i),v(l))}}}function es(n){let e,t;return e=new ml({props:{margin:n[8]==="default"}}),{c(){hi(e.$$.fragment)},m(i,l){Ai(e,i,l),t=!0},p(i,l){const o={};l[0]&256&&(o.margin=i[8]==="default"),e.$set(o)},i(i){t||(me(e.$$.fragment,i),t=!0)},o(i){Te(e.$$.fragment,i),t=!1},d(i){bi(e,i)}}}function ts(n){let e,t,i,l,o,s=`${n[15]*100}%`,r=n[7]!=null&&Bn(n);return{c(){e=Ae("div"),t=Ae("div"),r&&r.c(),i=re(),l=Ae("div"),o=Ae("div"),pe(t,"class","progress-level-inner svelte-au1olv"),pe(o,"class","progress-bar svelte-au1olv"),qe(o,"width",s),pe(l,"class","progress-bar-wrap svelte-au1olv"),pe(e,"class","progress-level svelte-au1olv")},m(u,a){C(u,e,a),Xe(e,t),r&&r.m(t,null),Xe(e,i),Xe(e,l),Xe(l,o),n[31](o)},p(u,a){u[7]!=null?r?r.p(u,a):(r=Bn(u),r.c(),r.m(t,null)):r&&(r.d(1),r=null),a[0]&32768&&s!==(s=`${u[15]*100}%`)&&qe(o,"width",s)},i:ln,o:ln,d(u){u&&v(e),r&&r.d(),n[31](null)}}}function Bn(n){let e,t=Mt(n[7]),i=[];for(let l=0;l<t.length;l+=1)i[l]=Zn(Pn(n,t,l));return{c(){for(let l=0;l<i.length;l+=1)i[l].c();e=rt()},m(l,o){for(let s=0;s<i.length;s+=1)i[s]&&i[s].m(l,o);C(l,e,o)},p(l,o){if(o[0]&16512){t=Mt(l[7]);let s;for(s=0;s<t.length;s+=1){const r=Pn(l,t,s);i[s]?i[s].p(r,o):(i[s]=Zn(r),i[s].c(),i[s].m(e.parentNode,e))}for(;s<i.length;s+=1)i[s].d(1);i.length=t.length}},d(l){l&&v(e),wi(i,l)}}}function Gn(n){let e,t,i,l,o=n[42]!==0&&ns(),s=n[40].desc!=null&&qn(n),r=n[40].desc!=null&&n[14]&&n[14][n[42]]!=null&&Vn(),u=n[14]!=null&&Yn(n);return{c(){o&&o.c(),e=re(),s&&s.c(),t=re(),r&&r.c(),i=re(),u&&u.c(),l=rt()},m(a,f){o&&o.m(a,f),C(a,e,f),s&&s.m(a,f),C(a,t,f),r&&r.m(a,f),C(a,i,f),u&&u.m(a,f),C(a,l,f)},p(a,f){a[40].desc!=null?s?s.p(a,f):(s=qn(a),s.c(),s.m(t.parentNode,t)):s&&(s.d(1),s=null),a[40].desc!=null&&a[14]&&a[14][a[42]]!=null?r||(r=Vn(),r.c(),r.m(i.parentNode,i)):r&&(r.d(1),r=null),a[14]!=null?u?u.p(a,f):(u=Yn(a),u.c(),u.m(l.parentNode,l)):u&&(u.d(1),u=null)},d(a){a&&(v(e),v(t),v(i),v(l)),o&&o.d(a),s&&s.d(a),r&&r.d(a),u&&u.d(a)}}}function ns(n){let e;return{c(){e=H(" /")},m(t,i){C(t,e,i)},d(t){t&&v(e)}}}function qn(n){let e=n[40].desc+"",t;return{c(){t=H(e)},m(i,l){C(i,t,l)},p(i,l){l[0]&128&&e!==(e=i[40].desc+"")&&ae(t,e)},d(i){i&&v(t)}}}function Vn(n){let e;return{c(){e=H("-")},m(t,i){C(t,e,i)},d(t){t&&v(e)}}}function Yn(n){let e=(100*(n[14][n[42]]||0)).toFixed(1)+"",t,i;return{c(){t=H(e),i=H("%")},m(l,o){C(l,t,o),C(l,i,o)},p(l,o){o[0]&16384&&e!==(e=(100*(l[14][l[42]]||0)).toFixed(1)+"")&&ae(t,e)},d(l){l&&(v(t),v(i))}}}function Zn(n){let e,t=(n[40].desc!=null||n[14]&&n[14][n[42]]!=null)&&Gn(n);return{c(){t&&t.c(),e=rt()},m(i,l){t&&t.m(i,l),C(i,e,l)},p(i,l){i[40].desc!=null||i[14]&&i[14][i[42]]!=null?t?t.p(i,l):(t=Gn(i),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(i){i&&v(e),t&&t.d(i)}}}function jn(n){let e,t,i,l;const o=n[30]["additional-loading-text"],s=gi(o,n,n[29],On);return{c(){e=Ae("p"),t=H(n[9]),i=re(),s&&s.c(),pe(e,"class","loading svelte-au1olv")},m(r,u){C(r,e,u),Xe(e,t),C(r,i,u),s&&s.m(r,u),l=!0},p(r,u){(!l||u[0]&512)&&ae(t,r[9]),s&&s.p&&(!l||u[0]&536870912)&&Ti(s,o,r,r[29],l?Ei(o,r[29],u,Zl):yi(r[29]),On)},i(r){l||(me(s,r),l=!0)},o(r){Te(s,r),l=!1},d(r){r&&(v(e),v(i)),s&&s.d(r)}}}function is(n){let e,t,i,l,o;const s=[Xl,jl],r=[];function u(a,f){return a[4]==="pending"?0:a[4]==="error"?1:-1}return~(t=u(n))&&(i=r[t]=s[t](n)),{c(){e=Ae("div"),i&&i.c(),pe(e,"class",l="wrap "+n[8]+" "+n[6]+" svelte-au1olv"),oe(e,"hide",!n[4]||n[4]==="complete"||n[6]==="hidden"),oe(e,"translucent",n[8]==="center"&&(n[4]==="pending"||n[4]==="error")||n[11]||n[6]==="minimal"),oe(e,"generating",n[4]==="generating"&&n[6]==="full"),oe(e,"border",n[12]),qe(e,"position",n[10]?"absolute":"static"),qe(e,"padding",n[10]?"0":"var(--size-8) 0")},m(a,f){C(a,e,f),~t&&r[t].m(e,null),n[33](e),o=!0},p(a,f){let d=t;t=u(a),t===d?~t&&r[t].p(a,f):(i&&(nn(),Te(r[d],1,1,()=>{r[d]=null}),tn()),~t?(i=r[t],i?i.p(a,f):(i=r[t]=s[t](a),i.c()),me(i,1),i.m(e,null)):i=null),(!o||f[0]&320&&l!==(l="wrap "+a[8]+" "+a[6]+" svelte-au1olv"))&&pe(e,"class",l),(!o||f[0]&336)&&oe(e,"hide",!a[4]||a[4]==="complete"||a[6]==="hidden"),(!o||f[0]&2384)&&oe(e,"translucent",a[8]==="center"&&(a[4]==="pending"||a[4]==="error")||a[11]||a[6]==="minimal"),(!o||f[0]&336)&&oe(e,"generating",a[4]==="generating"&&a[6]==="full"),(!o||f[0]&4416)&&oe(e,"border",a[12]),f[0]&1024&&qe(e,"position",a[10]?"absolute":"static"),f[0]&1024&&qe(e,"padding",a[10]?"0":"var(--size-8) 0")},i(a){o||(me(i),o=!0)},o(a){Te(i),o=!1},d(a){a&&v(e),~t&&r[t].d(),n[33](null)}}}let vt=[],Zt=!1;async function ls(n,e=!0){if(!(window.__gradio_mode__==="website"||window.__gradio_mode__!=="app"&&e!==!0)){if(vt.push(n),!Zt)Zt=!0;else return;await Gl(),requestAnimationFrame(()=>{let t=[0,0];for(let i=0;i<vt.length;i++){const o=vt[i].getBoundingClientRect();(i===0||o.top+window.scrollY<=t[0])&&(t[0]=o.top+window.scrollY,t[1]=i)}window.scrollTo({top:t[0]-20,behavior:"smooth"}),Zt=!1,vt=[]})}}function ss(n,e,t){let i,{$$slots:l={},$$scope:o}=e;const s=Vl();let{i18n:r}=e,{eta:u=null}=e,{queue_position:a}=e,{queue_size:f}=e,{status:d}=e,{scroll_to_output:h=!1}=e,{timer:y=!0}=e,{show_progress:T="full"}=e,{message:M=null}=e,{progress:w=null}=e,{variant:P="default"}=e,{loading_text:b="Loading..."}=e,{absolute:m=!0}=e,{translucent:k=!1}=e,{border:V=!1}=e,{autoscroll:ve}=e,ge,be=!1,$=0,F=0,X=null,R=null,Ce=0,ie=null,le,ee=null,Fe=!0;const Je=()=>{t(0,u=t(27,X=t(19,N=null))),t(25,$=performance.now()),t(26,F=0),be=!0,U()};function U(){requestAnimationFrame(()=>{t(26,F=(performance.now()-$)/1e3),be&&U()})}function Ue(){t(26,F=0),t(0,u=t(27,X=t(19,N=null))),be&&(be=!1)}ql(()=>{be&&Ue()});let N=null;function K(A){Mn[A?"unshift":"push"](()=>{ee=A,t(16,ee),t(7,w),t(14,ie),t(15,le)})}const I=()=>{s("clear_status")};function we(A){Mn[A?"unshift":"push"](()=>{ge=A,t(13,ge)})}return n.$$set=A=>{"i18n"in A&&t(1,r=A.i18n),"eta"in A&&t(0,u=A.eta),"queue_position"in A&&t(2,a=A.queue_position),"queue_size"in A&&t(3,f=A.queue_size),"status"in A&&t(4,d=A.status),"scroll_to_output"in A&&t(22,h=A.scroll_to_output),"timer"in A&&t(5,y=A.timer),"show_progress"in A&&t(6,T=A.show_progress),"message"in A&&t(23,M=A.message),"progress"in A&&t(7,w=A.progress),"variant"in A&&t(8,P=A.variant),"loading_text"in A&&t(9,b=A.loading_text),"absolute"in A&&t(10,m=A.absolute),"translucent"in A&&t(11,k=A.translucent),"border"in A&&t(12,V=A.border),"autoscroll"in A&&t(24,ve=A.autoscroll),"$$scope"in A&&t(29,o=A.$$scope)},n.$$.update=()=>{n.$$.dirty[0]&436207617&&(u===null&&t(0,u=X),u!=null&&X!==u&&(t(28,R=(performance.now()-$)/1e3+u),t(19,N=R.toFixed(1)),t(27,X=u))),n.$$.dirty[0]&335544320&&t(17,Ce=R===null||R<=0||!F?null:Math.min(F/R,1)),n.$$.dirty[0]&128&&w!=null&&t(18,Fe=!1),n.$$.dirty[0]&114816&&(w!=null?t(14,ie=w.map(A=>{if(A.index!=null&&A.length!=null)return A.index/A.length;if(A.progress!=null)return A.progress})):t(14,ie=null),ie?(t(15,le=ie[ie.length-1]),ee&&(le===0?t(16,ee.style.transition="0",ee):t(16,ee.style.transition="150ms",ee))):t(15,le=void 0)),n.$$.dirty[0]&16&&(d==="pending"?Je():Ue()),n.$$.dirty[0]&20979728&&ge&&h&&(d==="pending"||d==="complete")&&ls(ge,ve),n.$$.dirty[0]&8388624,n.$$.dirty[0]&67108864&&t(20,i=F.toFixed(1))},[u,r,a,f,d,y,T,w,P,b,m,k,V,ge,ie,le,ee,Ce,Fe,N,i,s,h,M,ve,$,F,X,R,o,l,K,I,we]}class os extends Hl{constructor(e){super(),Wl(this,e,ss,is,Bl,{i18n:1,eta:0,queue_position:2,queue_size:3,status:4,scroll_to_output:22,timer:5,show_progress:6,message:23,progress:7,variant:8,loading_text:9,absolute:10,translucent:11,border:12,autoscroll:24},null,[-1,-1])}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),Y()}get eta(){return this.$$.ctx[0]}set eta(e){this.$$set({eta:e}),Y()}get queue_position(){return this.$$.ctx[2]}set queue_position(e){this.$$set({queue_position:e}),Y()}get queue_size(){return this.$$.ctx[3]}set queue_size(e){this.$$set({queue_size:e}),Y()}get status(){return this.$$.ctx[4]}set status(e){this.$$set({status:e}),Y()}get scroll_to_output(){return this.$$.ctx[22]}set scroll_to_output(e){this.$$set({scroll_to_output:e}),Y()}get timer(){return this.$$.ctx[5]}set timer(e){this.$$set({timer:e}),Y()}get show_progress(){return this.$$.ctx[6]}set show_progress(e){this.$$set({show_progress:e}),Y()}get message(){return this.$$.ctx[23]}set message(e){this.$$set({message:e}),Y()}get progress(){return this.$$.ctx[7]}set progress(e){this.$$set({progress:e}),Y()}get variant(){return this.$$.ctx[8]}set variant(e){this.$$set({variant:e}),Y()}get loading_text(){return this.$$.ctx[9]}set loading_text(e){this.$$set({loading_text:e}),Y()}get absolute(){return this.$$.ctx[10]}set absolute(e){this.$$set({absolute:e}),Y()}get translucent(){return this.$$.ctx[11]}set translucent(e){this.$$set({translucent:e}),Y()}get border(){return this.$$.ctx[12]}set border(e){this.$$set({border:e}),Y()}get autoscroll(){return this.$$.ctx[24]}set autoscroll(e){this.$$set({autoscroll:e}),Y()}}/*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE */const{entries:ki,setPrototypeOf:Xn,isFrozen:rs,getPrototypeOf:as,getOwnPropertyDescriptor:fs}=Object;let{freeze:J,seal:he,create:cs}=Object,{apply:sn,construct:on}=typeof Reflect<"u"&&Reflect;sn||(sn=function(e,t,i){return e.apply(t,i)});J||(J=function(e){return e});he||(he=function(e){return e});on||(on=function(e,t){return new e(...t)});const us=fe(Array.prototype.forEach),Kn=fe(Array.prototype.pop),ft=fe(Array.prototype.push),St=fe(String.prototype.toLowerCase),jt=fe(String.prototype.toString),ds=fe(String.prototype.match),_e=fe(String.prototype.replace),_s=fe(String.prototype.indexOf),ms=fe(String.prototype.trim),ne=fe(RegExp.prototype.test),ct=ps(TypeError);function fe(n){return function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),l=1;l<t;l++)i[l-1]=arguments[l];return sn(n,e,i)}}function ps(n){return function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return on(n,t)}}function L(n,e,t){var i;t=(i=t)!==null&&i!==void 0?i:St,Xn&&Xn(n,null);let l=e.length;for(;l--;){let o=e[l];if(typeof o=="string"){const s=t(o);s!==o&&(rs(e)||(e[l]=s),o=s)}n[o]=!0}return n}function lt(n){const e=cs(null);for(const[t,i]of ki(n))e[t]=i;return e}function Ct(n,e){for(;n!==null;){const i=fs(n,e);if(i){if(i.get)return fe(i.get);if(typeof i.value=="function")return fe(i.value)}n=as(n)}function t(i){return console.warn("fallback value for",i),null}return t}const xn=J(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Xt=J(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Kt=J(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),hs=J(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),xt=J(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),gs=J(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Jn=J(["#text"]),Qn=J(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),Jt=J(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),$n=J(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Lt=J(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),bs=he(/\{\{[\w\W]*|[\w\W]*\}\}/gm),ws=he(/<%[\w\W]*|[\w\W]*%>/gm),ys=he(/\${[\w\W]*}/gm),Es=he(/^data-[\-\w.\u00B7-\uFFFF]/),As=he(/^aria-[\-\w]+$/),vi=he(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Ts=he(/^(?:\w+script|data):/i),ks=he(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Ci=he(/^html$/i);var ei=Object.freeze({__proto__:null,MUSTACHE_EXPR:bs,ERB_EXPR:ws,TMPLIT_EXPR:ys,DATA_ATTR:Es,ARIA_ATTR:As,IS_ALLOWED_URI:vi,IS_SCRIPT_OR_DATA:Ts,ATTR_WHITESPACE:ks,DOCTYPE_NAME:Ci});const vs=()=>typeof window>"u"?null:window,Cs=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let i=null;const l="data-tt-policy-suffix";t&&t.hasAttribute(l)&&(i=t.getAttribute(l));const o="dompurify"+(i?"#"+i:"");try{return e.createPolicy(o,{createHTML(s){return s},createScriptURL(s){return s}})}catch{return console.warn("TrustedTypes policy "+o+" could not be created."),null}};function Li(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:vs();const e=E=>Li(E);if(e.version="3.0.3",e.removed=[],!n||!n.document||n.document.nodeType!==9)return e.isSupported=!1,e;const t=n.document,i=t.currentScript;let{document:l}=n;const{DocumentFragment:o,HTMLTemplateElement:s,Node:r,Element:u,NodeFilter:a,NamedNodeMap:f=n.NamedNodeMap||n.MozNamedAttrMap,HTMLFormElement:d,DOMParser:h,trustedTypes:y}=n,T=u.prototype,M=Ct(T,"cloneNode"),w=Ct(T,"nextSibling"),P=Ct(T,"childNodes"),b=Ct(T,"parentNode");if(typeof s=="function"){const E=l.createElement("template");E.content&&E.content.ownerDocument&&(l=E.content.ownerDocument)}let m,k="";const{implementation:V,createNodeIterator:ve,createDocumentFragment:ge,getElementsByTagName:be}=l,{importNode:$}=t;let F={};e.isSupported=typeof ki=="function"&&typeof b=="function"&&V&&V.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:X,ERB_EXPR:R,TMPLIT_EXPR:Ce,DATA_ATTR:ie,ARIA_ATTR:le,IS_SCRIPT_OR_DATA:ee,ATTR_WHITESPACE:Fe}=ei;let{IS_ALLOWED_URI:Je}=ei,U=null;const Ue=L({},[...xn,...Xt,...Kt,...xt,...Jn]);let N=null;const K=L({},[...Qn,...Jt,...$n,...Lt]);let I=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),we=null,A=null,at=!0,Qe=!0,_t=!1,mt=!0,ze=!1,Le=!1,He=!1,Se=!1,We=!1,$e=!1,et=!1,pt=!0,ht=!1;const p="user-content-";let W=!0,O=!1,B={},Be=null;const rn=L({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let an=null;const fn=L({},["audio","video","img","source","image","track"]);let Ft=null;const cn=L({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),gt="http://www.w3.org/1998/Math/MathML",bt="http://www.w3.org/2000/svg",Re="http://www.w3.org/1999/xhtml";let tt=Re,Ut=!1,zt=null;const Di=L({},[gt,bt,Re],jt);let Ze;const Mi=["application/xhtml+xml","text/html"],Ii="text/html";let G,nt=null;const Oi=l.createElement("form"),un=function(c){return c instanceof RegExp||c instanceof Function},Ht=function(c){if(!(nt&&nt===c)){if((!c||typeof c!="object")&&(c={}),c=lt(c),Ze=Mi.indexOf(c.PARSER_MEDIA_TYPE)===-1?Ze=Ii:Ze=c.PARSER_MEDIA_TYPE,G=Ze==="application/xhtml+xml"?jt:St,U="ALLOWED_TAGS"in c?L({},c.ALLOWED_TAGS,G):Ue,N="ALLOWED_ATTR"in c?L({},c.ALLOWED_ATTR,G):K,zt="ALLOWED_NAMESPACES"in c?L({},c.ALLOWED_NAMESPACES,jt):Di,Ft="ADD_URI_SAFE_ATTR"in c?L(lt(cn),c.ADD_URI_SAFE_ATTR,G):cn,an="ADD_DATA_URI_TAGS"in c?L(lt(fn),c.ADD_DATA_URI_TAGS,G):fn,Be="FORBID_CONTENTS"in c?L({},c.FORBID_CONTENTS,G):rn,we="FORBID_TAGS"in c?L({},c.FORBID_TAGS,G):{},A="FORBID_ATTR"in c?L({},c.FORBID_ATTR,G):{},B="USE_PROFILES"in c?c.USE_PROFILES:!1,at=c.ALLOW_ARIA_ATTR!==!1,Qe=c.ALLOW_DATA_ATTR!==!1,_t=c.ALLOW_UNKNOWN_PROTOCOLS||!1,mt=c.ALLOW_SELF_CLOSE_IN_ATTR!==!1,ze=c.SAFE_FOR_TEMPLATES||!1,Le=c.WHOLE_DOCUMENT||!1,We=c.RETURN_DOM||!1,$e=c.RETURN_DOM_FRAGMENT||!1,et=c.RETURN_TRUSTED_TYPE||!1,Se=c.FORCE_BODY||!1,pt=c.SANITIZE_DOM!==!1,ht=c.SANITIZE_NAMED_PROPS||!1,W=c.KEEP_CONTENT!==!1,O=c.IN_PLACE||!1,Je=c.ALLOWED_URI_REGEXP||vi,tt=c.NAMESPACE||Re,I=c.CUSTOM_ELEMENT_HANDLING||{},c.CUSTOM_ELEMENT_HANDLING&&un(c.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(I.tagNameCheck=c.CUSTOM_ELEMENT_HANDLING.tagNameCheck),c.CUSTOM_ELEMENT_HANDLING&&un(c.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(I.attributeNameCheck=c.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),c.CUSTOM_ELEMENT_HANDLING&&typeof c.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(I.allowCustomizedBuiltInElements=c.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),ze&&(Qe=!1),$e&&(We=!0),B&&(U=L({},[...Jn]),N=[],B.html===!0&&(L(U,xn),L(N,Qn)),B.svg===!0&&(L(U,Xt),L(N,Jt),L(N,Lt)),B.svgFilters===!0&&(L(U,Kt),L(N,Jt),L(N,Lt)),B.mathMl===!0&&(L(U,xt),L(N,$n),L(N,Lt))),c.ADD_TAGS&&(U===Ue&&(U=lt(U)),L(U,c.ADD_TAGS,G)),c.ADD_ATTR&&(N===K&&(N=lt(N)),L(N,c.ADD_ATTR,G)),c.ADD_URI_SAFE_ATTR&&L(Ft,c.ADD_URI_SAFE_ATTR,G),c.FORBID_CONTENTS&&(Be===rn&&(Be=lt(Be)),L(Be,c.FORBID_CONTENTS,G)),W&&(U["#text"]=!0),Le&&L(U,["html","head","body"]),U.table&&(L(U,["tbody"]),delete we.tbody),c.TRUSTED_TYPES_POLICY){if(typeof c.TRUSTED_TYPES_POLICY.createHTML!="function")throw ct('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof c.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw ct('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');m=c.TRUSTED_TYPES_POLICY,k=m.createHTML("")}else m===void 0&&(m=Cs(y,i)),m!==null&&typeof k=="string"&&(k=m.createHTML(""));J&&J(c),nt=c}},dn=L({},["mi","mo","mn","ms","mtext"]),_n=L({},["foreignobject","desc","title","annotation-xml"]),Pi=L({},["title","style","font","a","script"]),wt=L({},Xt);L(wt,Kt),L(wt,hs);const Wt=L({},xt);L(Wt,gs);const Fi=function(c){let _=b(c);(!_||!_.tagName)&&(_={namespaceURI:tt,tagName:"template"});const g=St(c.tagName),D=St(_.tagName);return zt[c.namespaceURI]?c.namespaceURI===bt?_.namespaceURI===Re?g==="svg":_.namespaceURI===gt?g==="svg"&&(D==="annotation-xml"||dn[D]):!!wt[g]:c.namespaceURI===gt?_.namespaceURI===Re?g==="math":_.namespaceURI===bt?g==="math"&&_n[D]:!!Wt[g]:c.namespaceURI===Re?_.namespaceURI===bt&&!_n[D]||_.namespaceURI===gt&&!dn[D]?!1:!Wt[g]&&(Pi[g]||!wt[g]):!!(Ze==="application/xhtml+xml"&&zt[c.namespaceURI]):!1},je=function(c){ft(e.removed,{element:c});try{c.parentNode.removeChild(c)}catch{c.remove()}},Bt=function(c,_){try{ft(e.removed,{attribute:_.getAttributeNode(c),from:_})}catch{ft(e.removed,{attribute:null,from:_})}if(_.removeAttribute(c),c==="is"&&!N[c])if(We||$e)try{je(_)}catch{}else try{_.setAttribute(c,"")}catch{}},mn=function(c){let _,g;if(Se)c="<remove></remove>"+c;else{const se=ds(c,/^[\r\n\t ]+/);g=se&&se[0]}Ze==="application/xhtml+xml"&&tt===Re&&(c='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+c+"</body></html>");const D=m?m.createHTML(c):c;if(tt===Re)try{_=new h().parseFromString(D,Ze)}catch{}if(!_||!_.documentElement){_=V.createDocument(tt,"template",null);try{_.documentElement.innerHTML=Ut?k:D}catch{}}const q=_.body||_.documentElement;return c&&g&&q.insertBefore(l.createTextNode(g),q.childNodes[0]||null),tt===Re?be.call(_,Le?"html":"body")[0]:Le?_.documentElement:q},pn=function(c){return ve.call(c.ownerDocument||c,c,a.SHOW_ELEMENT|a.SHOW_COMMENT|a.SHOW_TEXT,null,!1)},Ui=function(c){return c instanceof d&&(typeof c.nodeName!="string"||typeof c.textContent!="string"||typeof c.removeChild!="function"||!(c.attributes instanceof f)||typeof c.removeAttribute!="function"||typeof c.setAttribute!="function"||typeof c.namespaceURI!="string"||typeof c.insertBefore!="function"||typeof c.hasChildNodes!="function")},yt=function(c){return typeof r=="object"?c instanceof r:c&&typeof c=="object"&&typeof c.nodeType=="number"&&typeof c.nodeName=="string"},Ne=function(c,_,g){F[c]&&us(F[c],D=>{D.call(e,_,g,nt)})},hn=function(c){let _;if(Ne("beforeSanitizeElements",c,null),Ui(c))return je(c),!0;const g=G(c.nodeName);if(Ne("uponSanitizeElement",c,{tagName:g,allowedTags:U}),c.hasChildNodes()&&!yt(c.firstElementChild)&&(!yt(c.content)||!yt(c.content.firstElementChild))&&ne(/<[/\w]/g,c.innerHTML)&&ne(/<[/\w]/g,c.textContent))return je(c),!0;if(!U[g]||we[g]){if(!we[g]&&bn(g)&&(I.tagNameCheck instanceof RegExp&&ne(I.tagNameCheck,g)||I.tagNameCheck instanceof Function&&I.tagNameCheck(g)))return!1;if(W&&!Be[g]){const D=b(c)||c.parentNode,q=P(c)||c.childNodes;if(q&&D){const se=q.length;for(let z=se-1;z>=0;--z)D.insertBefore(M(q[z],!0),w(c))}}return je(c),!0}return c instanceof u&&!Fi(c)||(g==="noscript"||g==="noembed")&&ne(/<\/no(script|embed)/i,c.innerHTML)?(je(c),!0):(ze&&c.nodeType===3&&(_=c.textContent,_=_e(_,X," "),_=_e(_,R," "),_=_e(_,Ce," "),c.textContent!==_&&(ft(e.removed,{element:c.cloneNode()}),c.textContent=_)),Ne("afterSanitizeElements",c,null),!1)},gn=function(c,_,g){if(pt&&(_==="id"||_==="name")&&(g in l||g in Oi))return!1;if(!(Qe&&!A[_]&&ne(ie,_))){if(!(at&&ne(le,_))){if(!N[_]||A[_]){if(!(bn(c)&&(I.tagNameCheck instanceof RegExp&&ne(I.tagNameCheck,c)||I.tagNameCheck instanceof Function&&I.tagNameCheck(c))&&(I.attributeNameCheck instanceof RegExp&&ne(I.attributeNameCheck,_)||I.attributeNameCheck instanceof Function&&I.attributeNameCheck(_))||_==="is"&&I.allowCustomizedBuiltInElements&&(I.tagNameCheck instanceof RegExp&&ne(I.tagNameCheck,g)||I.tagNameCheck instanceof Function&&I.tagNameCheck(g))))return!1}else if(!Ft[_]){if(!ne(Je,_e(g,Fe,""))){if(!((_==="src"||_==="xlink:href"||_==="href")&&c!=="script"&&_s(g,"data:")===0&&an[c])){if(!(_t&&!ne(ee,_e(g,Fe,"")))){if(g)return!1}}}}}}return!0},bn=function(c){return c.indexOf("-")>0},wn=function(c){let _,g,D,q;Ne("beforeSanitizeAttributes",c,null);const{attributes:se}=c;if(!se)return;const z={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:N};for(q=se.length;q--;){_=se[q];const{name:ye,namespaceURI:Gt}=_;if(g=ye==="value"?_.value:ms(_.value),D=G(ye),z.attrName=D,z.attrValue=g,z.keepAttr=!0,z.forceKeepAttr=void 0,Ne("uponSanitizeAttribute",c,z),g=z.attrValue,z.forceKeepAttr||(Bt(ye,c),!z.keepAttr))continue;if(!mt&&ne(/\/>/i,g)){Bt(ye,c);continue}ze&&(g=_e(g,X," "),g=_e(g,R," "),g=_e(g,Ce," "));const yn=G(c.nodeName);if(gn(yn,D,g)){if(ht&&(D==="id"||D==="name")&&(Bt(ye,c),g=p+g),m&&typeof y=="object"&&typeof y.getAttributeType=="function"&&!Gt)switch(y.getAttributeType(yn,D)){case"TrustedHTML":{g=m.createHTML(g);break}case"TrustedScriptURL":{g=m.createScriptURL(g);break}}try{Gt?c.setAttributeNS(Gt,ye,g):c.setAttribute(ye,g),Kn(e.removed)}catch{}}}Ne("afterSanitizeAttributes",c,null)},zi=function E(c){let _;const g=pn(c);for(Ne("beforeSanitizeShadowDOM",c,null);_=g.nextNode();)Ne("uponSanitizeShadowNode",_,null),!hn(_)&&(_.content instanceof o&&E(_.content),wn(_));Ne("afterSanitizeShadowDOM",c,null)};return e.sanitize=function(E){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},_,g,D,q;if(Ut=!E,Ut&&(E="<!-->"),typeof E!="string"&&!yt(E))if(typeof E.toString=="function"){if(E=E.toString(),typeof E!="string")throw ct("dirty is not a string, aborting")}else throw ct("toString is not a function");if(!e.isSupported)return E;if(He||Ht(c),e.removed=[],typeof E=="string"&&(O=!1),O){if(E.nodeName){const ye=G(E.nodeName);if(!U[ye]||we[ye])throw ct("root node is forbidden and cannot be sanitized in-place")}}else if(E instanceof r)_=mn("<!---->"),g=_.ownerDocument.importNode(E,!0),g.nodeType===1&&g.nodeName==="BODY"||g.nodeName==="HTML"?_=g:_.appendChild(g);else{if(!We&&!ze&&!Le&&E.indexOf("<")===-1)return m&&et?m.createHTML(E):E;if(_=mn(E),!_)return We?null:et?k:""}_&&Se&&je(_.firstChild);const se=pn(O?E:_);for(;D=se.nextNode();)hn(D)||(D.content instanceof o&&zi(D.content),wn(D));if(O)return E;if(We){if($e)for(q=ge.call(_.ownerDocument);_.firstChild;)q.appendChild(_.firstChild);else q=_;return(N.shadowroot||N.shadowrootmod)&&(q=$.call(t,q,!0)),q}let z=Le?_.outerHTML:_.innerHTML;return Le&&U["!doctype"]&&_.ownerDocument&&_.ownerDocument.doctype&&_.ownerDocument.doctype.name&&ne(Ci,_.ownerDocument.doctype.name)&&(z="<!DOCTYPE "+_.ownerDocument.doctype.name+`>
`+z),ze&&(z=_e(z,X," "),z=_e(z,R," "),z=_e(z,Ce," ")),m&&et?m.createHTML(z):z},e.setConfig=function(E){Ht(E),He=!0},e.clearConfig=function(){nt=null,He=!1},e.isValidAttribute=function(E,c,_){nt||Ht({});const g=G(E),D=G(c);return gn(g,D,_)},e.addHook=function(E,c){typeof c=="function"&&(F[E]=F[E]||[],ft(F[E],c))},e.removeHook=function(E){if(F[E])return Kn(F[E])},e.removeHooks=function(E){F[E]&&(F[E]=[])},e.removeAllHooks=function(){F={}},e}var Ao=Li();const{setContext:Ls,getContext:Ss}=window.__gradio__svelte__internal,Si="WORKER_PROXY_CONTEXT_KEY";function Rs(n){Ls(Si,n)}function To(){return Ss(Si)}var Ns=()=>{const n=document.createElement("link");n.href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap",n.rel="stylesheet";const e=document.createElement("link");e.href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;600;700&display=swap",e.rel="stylesheet",document.head.appendChild(n),document.head.appendChild(e)},Ds=()=>{const n=document.createElement("div");return n.style.backgroundImage="linear-gradient(to top, #f9fafb, white)",n.style.border="1px solid #e5e7eb",n.style.borderRadius="0.75rem",n.style.boxShadow="0 0 10px rgba(0, 0, 0, 0.1)",n.style.color="#374151",n.style.display="flex",n.style.flexDirection="row",n.style.alignItems="center",n.style.height="40px",n.style.justifyContent="space-between",n.style.overflow="hidden",n.style.position="fixed",n.style.right=".75rem",n.style.top=".75rem",n.style.width="auto",n.style.zIndex="20",n.style.paddingLeft="1rem",n.setAttribute("id","huggingface-space-header"),window.matchMedia("(max-width: 768px)").addEventListener("change",e=>{e.matches?n.style.display="none":n.style.display="flex"}),n},Ms=()=>{const n=document.createElementNS("http://www.w3.org/2000/svg","svg");n.setAttribute("xmlns","http://www.w3.org/2000/svg"),n.setAttribute("xmlns:link","http://www.w3.org/1999/xlink"),n.setAttribute("aria-hidden","true"),n.setAttribute("focusable","false"),n.setAttribute("role","img"),n.setAttribute("width","1em"),n.setAttribute("height","1em"),n.setAttribute("preserveAspectRatio","xMidYMid meet"),n.setAttribute("viewBox","0 0 12 12"),n.setAttribute("fill","currentColor");const e=document.createElementNS("http://www.w3.org/2000/svg","path");return e.setAttribute("d","M0.375001 10.3828L0.375 1.61719C0.375 1.104 0.816001 0.687501 1.35938 0.687501L10.6406 0.6875C10.9017 0.6875 11.1521 0.785449 11.3367 0.959797C11.5213 1.13415 11.625 1.37062 11.625 1.61719V10.3828C11.625 10.6294 11.5213 10.8659 11.3367 11.0402C11.1521 11.2145 10.9017 11.3125 10.6406 11.3125H1.35938C0.816001 11.3125 0.375001 10.896 0.375001 10.3828ZM1.35938 10.5156H10.6406C10.7183 10.5156 10.7813 10.4561 10.7813 10.3828V4.40625H1.21875V10.3828C1.21875 10.418 1.23356 10.4518 1.25994 10.4767C1.28631 10.5017 1.32208 10.5156 1.35938 10.5156ZM4.61052 6.38251L5.9999 7.69472L7.38927 6.38251C7.44083 6.33007 7.50645 6.29173 7.57913 6.27153C7.6518 6.25134 7.72898 6.25003 7.8024 6.26776C7.87583 6.28549 7.9428 6.3216 7.99628 6.37227C8.04983 6.42295 8.08785 6.48631 8.10645 6.5557C8.12528 6.62497 8.12393 6.69773 8.10263 6.76635C8.0814 6.83497 8.0409 6.8969 7.98555 6.94564L6.29802 8.53936C6.21892 8.61399 6.11169 8.65592 5.9999 8.65592C5.8881 8.65592 5.78087 8.61399 5.70177 8.53936L4.01427 6.94564C3.95874 6.89694 3.91814 6.835 3.89676 6.76633C3.87538 6.69766 3.874 6.62483 3.89277 6.55549C3.91154 6.48615 3.94977 6.42287 4.00343 6.37233C4.05708 6.32179 4.12418 6.28585 4.19765 6.2683C4.27098 6.25054 4.34803 6.25178 4.42068 6.27188C4.49334 6.29198 4.55891 6.3302 4.61052 6.38251Z"),n.appendChild(e),n},Is=(n,e)=>{const t=document.createElement("div");return t.setAttribute("id","space-header__collapse"),t.style.display="flex",t.style.flexDirection="row",t.style.alignItems="center",t.style.justifyContent="center",t.style.fontSize="16px",t.style.paddingLeft="10px",t.style.paddingRight="10px",t.style.height="40px",t.style.cursor="pointer",t.style.color="#40546e",t.style.transitionDuration="0.1s",t.style.transitionProperty="all",t.style.transitionTimingFunction="ease-in-out",t.appendChild(Ms()),t.addEventListener("click",i=>{i.preventDefault(),i.stopPropagation(),e()}),t.addEventListener("mouseenter",()=>{t.style.color="#213551"}),t.addEventListener("mouseleave",()=>{t.style.color="#40546e"}),t},Os=n=>{const e=document.createElement("p");return e.style.margin="0",e.style.padding="0",e.style.color="#9ca3af",e.style.fontSize="14px",e.style.fontFamily="Source Sans Pro, sans-serif",e.style.padding="0px 6px",e.style.borderLeft="1px solid #e5e7eb",e.style.marginLeft="4px",e.textContent=(n??0).toString(),e},Ps=()=>{const n=document.createElementNS("http://www.w3.org/2000/svg","svg");n.setAttribute("xmlns","http://www.w3.org/2000/svg"),n.setAttribute("xmlns:link","http://www.w3.org/1999/xlink"),n.setAttribute("aria-hidden","true"),n.setAttribute("focusable","false"),n.setAttribute("role","img"),n.setAttribute("width","1em"),n.setAttribute("height","1em"),n.setAttribute("preserveAspectRatio","xMidYMid meet"),n.setAttribute("viewBox","0 0 32 32"),n.setAttribute("fill","#6b7280");const e=document.createElementNS("http://www.w3.org/2000/svg","path");return e.setAttribute("d","M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z"),n.appendChild(e),n},Fs=n=>{const e=document.createElement("a");return e.setAttribute("href",`https://huggingface.co/spaces/${n.id}`),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e.style.border="1px solid #e5e7eb",e.style.borderRadius="6px",e.style.display="flex",e.style.flexDirection="row",e.style.alignItems="center",e.style.margin="0 0 0 12px",e.style.fontSize="14px",e.style.paddingLeft="4px",e.style.textDecoration="none",e.appendChild(Ps()),e.appendChild(Os(n.likes)),e},Us=n=>{const e=document.createElement("img");return e.src=`https://huggingface.co/api/users/${n}/avatar`,e.style.width="0.875rem",e.style.height="0.875rem",e.style.borderRadius="50%",e.style.flex="none",e.style.marginRight="0.375rem",e},zs=n=>{const[e,t]=n.split("/"),i=document.createElement("a");return i.setAttribute("href",`https://huggingface.co/spaces/${n}`),i.setAttribute("rel","noopener noreferrer"),i.setAttribute("target","_blank"),i.style.color="#1f2937",i.style.textDecoration="none",i.style.fontWeight="600",i.style.fontSize="15px",i.style.lineHeight="24px",i.style.flex="none",i.style.fontFamily="IBM Plex Mono, sans-serif",i.addEventListener("mouseover",()=>{i.style.color="#2563eb"}),i.addEventListener("mouseout",()=>{i.style.color="#1f2937"}),i.textContent=t,i},Hs=()=>{const n=document.createElement("div");return n.style.marginLeft=".125rem",n.style.marginRight=".125rem",n.style.color="#d1d5db",n.textContent="/",n},Ws=n=>{const e=document.createElement("a");return e.setAttribute("href",`https://huggingface.co/${n}`),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e.style.color="rgb(107, 114, 128)",e.style.textDecoration="none",e.style.fontWeight="400",e.style.fontSize="16px",e.style.lineHeight="24px",e.style.flex="none",e.style.fontFamily="Source Sans Pro, sans-serif",e.addEventListener("mouseover",()=>{e.style.color="#2563eb"}),e.addEventListener("mouseout",()=>{e.style.color="rgb(107, 114, 128)"}),e.textContent=n,e},Bs=n=>{const e=document.createElement("div");return e.style.display="flex",e.style.flexDirection="row",e.style.alignItems="center",e.style.justifyContent="center",e.style.borderRight="1px solid #e5e7eb",e.style.paddingRight="12px",e.style.height="40px",e.appendChild(Us(n.author)),e.appendChild(Ws(n.author)),e.appendChild(Hs()),e.appendChild(zs(n.id)),e.appendChild(Fs(n)),e},Gs=n=>{const e=Ds(),t=()=>e.style.display="none";return e.appendChild(Bs(n)),e.appendChild(Is(n,t)),e},qs=async n=>{try{return await(await fetch(`https://huggingface.co/api/spaces/${n}`)).json()}catch{return null}},Vs=(n,e)=>{if(document.body===null)return console.error("document.body is null");document.body.appendChild(n)};async function Ys(n,e){var t,i;if(window===void 0)return console.error("Please run this script in a browser environment");if(Object.values((i=(t=window.location)==null?void 0:t.ancestorOrigins)!=null?i:{0:window.document.referrer}).some(r=>{var u;return((u=new URL(r))==null?void 0:u.origin)==="https://huggingface.co"}))return;Ns();let o;if(typeof n=="string"){if(o=await qs(n),o===null)return console.error("Space not found")}else o=n;const s=Gs(o);return Vs(s),{element:s}}var Zs=(n,e)=>Ys(n);const{SvelteComponent:js,add_flush_callback:Rt,append:Pe,assign:Xs,attr:ke,bind:Nt,binding_callbacks:Dt,check_outros:ti,component_subscribe:ni,create_component:It,destroy_component:Ot,detach:Ke,element:Ye,empty:Ks,flush:Z,get_spread_object:xs,get_spread_update:Js,group_outros:ii,init:Qs,insert:xe,mount_component:Pt,noop:$s,safe_not_equal:eo,set_data:Ri,space:Ni,text:dt,transition_in:Oe,transition_out:Ve}=window.__gradio__svelte__internal,{onMount:li,createEventDispatcher:to,onDestroy:no}=window.__gradio__svelte__internal;function si(n){let e,t;return e=new os({props:{absolute:!n[4],status:n[15],timer:!1,queue_position:null,queue_size:null,translucent:!0,loading_text:n[16],i18n:n[22],autoscroll:n[0],$$slots:{error:[ro],"additional-loading-text":[lo]},$$scope:{ctx:n}}}),{c(){It(e.$$.fragment)},m(i,l){Pt(e,i,l),t=!0},p(i,l){const o={};l[0]&16&&(o.absolute=!i[4]),l[0]&32768&&(o.status=i[15]),l[0]&65536&&(o.loading_text=i[16]),l[0]&4194304&&(o.i18n=i[22]),l[0]&1&&(o.autoscroll=i[0]),l[0]&4202752|l[1]&4194304&&(o.$$scope={dirty:l,ctx:i}),e.$set(o)},i(i){t||(Oe(e.$$.fragment,i),t=!0)},o(i){Ve(e.$$.fragment,i),t=!1},d(i){Ot(e,i)}}}function io(n){let e;return{c(){e=Ye("p"),e.innerHTML='If your custom component never loads, consult the troubleshooting <a style="color: blue;" href="https://www.gradio.app/guides/frequently-asked-questions#the-development-server-didnt-work-for-me" class="svelte-y6l4b">guide</a>.'},m(t,i){xe(t,e,i)},d(t){t&&Ke(e)}}}function lo(n){let e,t=n[23]==="dev"&&io();return{c(){e=Ye("div"),t&&t.c(),ke(e,"class","load-text"),ke(e,"slot","additional-loading-text")},m(i,l){xe(i,e,l),t&&t.m(e,null)},p:$s,d(i){i&&Ke(e),t&&t.d()}}}function so(n){let e,t=n[22]("errors.contact_page_author")+"",i;return{c(){e=Ye("p"),i=dt(t),ke(e,"class","svelte-y6l4b")},m(l,o){xe(l,e,o),Pe(e,i)},p(l,o){o[0]&4194304&&t!==(t=l[22]("errors.contact_page_author")+"")&&Ri(i,t)},d(l){l&&Ke(e)}}}function oo(n){let e,t,i,l,o,s;return{c(){e=Ye("p"),t=dt("Please "),i=Ye("a"),l=dt("contact the author of the space"),s=dt(" to let them know."),ke(i,"href",o="https://huggingface.co/spaces/"+n[8]+"/discussions/new?title="+n[24].title(n[13]?.detail)+"&description="+n[24].description(n[13]?.detail,location.origin)),ke(i,"class","svelte-y6l4b"),ke(e,"class","svelte-y6l4b")},m(r,u){xe(r,e,u),Pe(e,t),Pe(e,i),Pe(i,l),Pe(e,s)},p(r,u){u[0]&8448&&o!==(o="https://huggingface.co/spaces/"+r[8]+"/discussions/new?title="+r[24].title(r[13]?.detail)+"&description="+r[24].description(r[13]?.detail,location.origin))&&ke(i,"href",o)},d(r){r&&Ke(e)}}}function ro(n){let e,t,i,l=(n[13]?.message||"")+"",o,s;function r(f,d){return(f[13].status==="space_error"||f[13].status==="paused")&&f[13].discussions_enabled?oo:so}let u=r(n),a=u(n);return{c(){e=Ye("div"),t=Ye("p"),i=Ye("strong"),o=dt(l),s=Ni(),a.c(),ke(t,"class","svelte-y6l4b"),ke(e,"class","error svelte-y6l4b"),ke(e,"slot","error")},m(f,d){xe(f,e,d),Pe(e,t),Pe(t,i),Pe(i,o),Pe(e,s),a.m(e,null)},p(f,d){d[0]&8192&&l!==(l=(f[13]?.message||"")+"")&&Ri(o,l),u===(u=r(f))&&a?a.p(f,d):(a.d(1),a=u(f),a&&(a.c(),a.m(e,null)))},d(f){f&&Ke(e),a.d()}}}function ao(n){let e,t,i,l,o;const s=[{app:n[14]},n[12],{fill_height:!n[4]&&n[12].fill_height},{theme_mode:n[17]},{control_page_title:n[5]},{target:n[9]},{autoscroll:n[0]},{show_footer:!n[4]},{app_mode:n[3]},{version:n[1]}];function r(d){n[33](d)}function u(d){n[34](d)}function a(d){n[35](d)}let f={};for(let d=0;d<s.length;d+=1)f=Xs(f,s[d]);return n[10]!==void 0&&(f.ready=n[10]),n[11]!==void 0&&(f.render_complete=n[11]),n[21]!==void 0&&(f.add_new_message=n[21]),e=new n[19]({props:f}),Dt.push(()=>Nt(e,"ready",r)),Dt.push(()=>Nt(e,"render_complete",u)),Dt.push(()=>Nt(e,"add_new_message",a)),{c(){It(e.$$.fragment)},m(d,h){Pt(e,d,h),o=!0},p(d,h){const y=h[0]&152123?Js(s,[h[0]&16384&&{app:d[14]},h[0]&4096&&xs(d[12]),h[0]&4112&&{fill_height:!d[4]&&d[12].fill_height},h[0]&131072&&{theme_mode:d[17]},h[0]&32&&{control_page_title:d[5]},h[0]&512&&{target:d[9]},h[0]&1&&{autoscroll:d[0]},h[0]&16&&{show_footer:!d[4]},h[0]&8&&{app_mode:d[3]},h[0]&2&&{version:d[1]}]):{};!t&&h[0]&1024&&(t=!0,y.ready=d[10],Rt(()=>t=!1)),!i&&h[0]&2048&&(i=!0,y.render_complete=d[11],Rt(()=>i=!1)),!l&&h[0]&2097152&&(l=!0,y.add_new_message=d[21],Rt(()=>l=!1)),e.$set(y)},i(d){o||(Oe(e.$$.fragment,d),o=!0)},o(d){Ve(e.$$.fragment,d),o=!1},d(d){Ot(e,d)}}}function fo(n){let e,t;return e=new n[20]({props:{auth_message:n[12].auth_message,root:n[12].root,space_id:n[8],app_mode:n[3]}}),{c(){It(e.$$.fragment)},m(i,l){Pt(e,i,l),t=!0},p(i,l){const o={};l[0]&4096&&(o.auth_message=i[12].auth_message),l[0]&4096&&(o.root=i[12].root),l[0]&256&&(o.space_id=i[8]),l[0]&8&&(o.app_mode=i[3]),e.$set(o)},i(i){t||(Oe(e.$$.fragment,i),t=!0)},o(i){Ve(e.$$.fragment,i),t=!1},d(i){Ot(e,i)}}}function co(n){let e,t,i,l,o,s=(n[15]==="pending"||n[15]==="error")&&!(n[12]&&n[12]?.auth_required)&&si(n);const r=[fo,ao],u=[];function a(f,d){return f[12]?.auth_required&&f[20]?0:f[12]&&f[19]&&f[18]?1:-1}return~(t=a(n))&&(i=u[t]=r[t](n)),{c(){s&&s.c(),e=Ni(),i&&i.c(),l=Ks()},m(f,d){s&&s.m(f,d),xe(f,e,d),~t&&u[t].m(f,d),xe(f,l,d),o=!0},p(f,d){(f[15]==="pending"||f[15]==="error")&&!(f[12]&&f[12]?.auth_required)?s?(s.p(f,d),d[0]&36864&&Oe(s,1)):(s=si(f),s.c(),Oe(s,1),s.m(e.parentNode,e)):s&&(ii(),Ve(s,1,1,()=>{s=null}),ti());let h=t;t=a(f),t===h?~t&&u[t].p(f,d):(i&&(ii(),Ve(u[h],1,1,()=>{u[h]=null}),ti()),~t?(i=u[t],i?i.p(f,d):(i=u[t]=r[t](f),i.c()),Oe(i,1),i.m(l.parentNode,l)):i=null)},i(f){o||(Oe(s),Oe(i),o=!0)},o(f){Ve(s),Ve(i),o=!1},d(f){f&&(Ke(e),Ke(l)),s&&s.d(f),~t&&u[t].d(f)}}}function uo(n){let e,t,i;function l(s){n[36](s)}let o={display:n[6]&&n[4],is_embed:n[4],info:!!n[8]&&n[7],version:n[1],initial_height:n[2],space:n[8],loaded:n[15]==="complete",fill_width:n[12]?.fill_width||!1,$$slots:{default:[co]},$$scope:{ctx:n}};return n[9]!==void 0&&(o.wrapper=n[9]),e=new il({props:o}),Dt.push(()=>Nt(e,"wrapper",l)),{c(){It(e.$$.fragment)},m(s,r){Pt(e,s,r),i=!0},p(s,r){const u={};r[0]&80&&(u.display=s[6]&&s[4]),r[0]&16&&(u.is_embed=s[4]),r[0]&384&&(u.info=!!s[8]&&s[7]),r[0]&2&&(u.version=s[1]),r[0]&4&&(u.initial_height=s[2]),r[0]&256&&(u.space=s[8]),r[0]&32768&&(u.loaded=s[15]==="complete"),r[0]&4096&&(u.fill_width=s[12]?.fill_width||!1),r[0]&8388411|r[1]&4194304&&(u.$$scope={dirty:r,ctx:s}),!t&&r[0]&512&&(t=!0,u.wrapper=s[9],Rt(()=>t=!1)),e.$set(u)},i(s){i||(Oe(e.$$.fragment,s),i=!0)},o(s){Ve(e.$$.fragment,s),i=!1},d(s){Ot(e,s)}}}let _o=-1;function mo(){const n=ai({}),e=new Map,t=new IntersectionObserver(l=>{l.forEach(o=>{if(o.isIntersecting){let s=e.get(o.target);s!==void 0&&n.update(r=>({...r,[s]:!0}))}})});function i(l,o){e.set(o,l),t.observe(o)}return{register:i,subscribe:n.subscribe}}const oi=mo();async function po(n){if(n){const e=new DOMParser,t=Array.from(e.parseFromString(n,"text/html").head.children);if(t)for(let i of t){let l=document.createElement(i.tagName);if(Array.from(i.attributes).forEach(o=>{l.setAttribute(o.name,o.value)}),l.textContent=i.textContent,l.tagName=="META"&&l.getAttribute("property")){const s=Array.from(document.head.getElementsByTagName("meta")??[]).find(r=>r.getAttribute("property")==l.getAttribute("property")&&!r.isEqualNode(l));if(s){document.head.replaceChild(l,s);continue}}document.head.appendChild(l)}}}function ho(n,e,t){let i,l;ni(n,ri,p=>t(22,i=p)),ni(n,oi,p=>t(32,l=p)),Wi();const o=to();let{autoscroll:s}=e,{version:r}=e,{initial_height:u}=e,{app_mode:a}=e,{is_embed:f}=e,{theme_mode:d="system"}=e,{control_page_title:h}=e,{container:y}=e,{info:T}=e,{eager:M}=e,w,{mount_css:P=Bi}=e,{Client:b}=e,{worker_proxy:m=void 0}=e;m&&(Rs(m),m.addEventListener("progress-update",p=>{t(16,Ce=p.detail+"...")}));let{space:k}=e,{host:V}=e,{src:ve}=e,ge=_o++,be="pending",$,F=!1,X=!1,R,Ce=i("common.loading")+"...",ie,le,ee=null;async function Fe(p){p&&(ee=En(p,r,ee||void 0)),await P(R.root+"/theme.css?v="+R.theme_hash,document.head),R.stylesheets&&await Promise.all(R.stylesheets.map(W=>W.startsWith("http:")||W.startsWith("https:")?P(W,document.head):fetch(R.root+"/"+W).then(B=>B.text()).then(B=>{En(B,r)})))}function Je(p){const W=window.__gradio_mode__==="website";let O;if(W)O="light";else{const Be=new URL(window.location.toString()).searchParams.get("__theme");O=d||Be||"system"}return O==="dark"||O==="light"?Ue(p,O):O=U(p),O}function U(p){const W=O();window?.matchMedia("(prefers-color-scheme: dark)")?.addEventListener("change",O);function O(){let B=window?.matchMedia?.("(prefers-color-scheme: dark)").matches?"dark":"light";return Ue(p,B),B}return W}function Ue(p,W){const O=f?p.parentElement:document.body,B=f?p:p.parentElement;B.style.background="var(--body-background-fill)",W==="dark"?O.classList.add("dark"):O.classList.remove("dark")}let N={message:"",load_status:"pending",status:"sleeping",detail:"SLEEPING"},K,I=!1;function we(p){t(13,N=p)}const A=window.__GRADIO_DEV__;li(async()=>{t(17,ie=Je($));const p=window.__GRADIO__SERVER_PORT__;if(le=A==="dev"?`http://localhost:${typeof p=="number"?p:7860}`:V||k||ve||location.origin,t(14,K=await b.connect(le,{status_callback:we,with_null_state:!0,events:["data","log","status","render"]})),!K.config)throw new Error("Could not resolve app config");t(12,R=K.config),window.__gradio_space__=R.space_id,t(13,N={message:"",load_status:"complete",status:"running",detail:"RUNNING"}),await Fe(R.css),await po(R.head),t(18,I=!0),window.__is_colab__=R.is_colab,o("loaded"),R.dev_mode&&setTimeout(()=>{const{host:W}=new URL(le);let O=new URL(`http://${W}/dev/reload`);w=new EventSource(O),w.addEventListener("error",async B=>{He("Error reloading app","error"),console.error(JSON.parse(B.data))}),w.addEventListener("reload",async B=>{if(K.close(),t(14,K=await b.connect(le,{status_callback:we,with_null_state:!0,events:["data","log","status","render"]})),!K.config)throw new Error("Could not resolve app config");t(12,R=K.config),window.__gradio_space__=R.space_id,await Fe(R.css)})},200)});let at,Qe;async function _t(){t(19,at=(await An(()=>import("./Blocks-CyfcXtBq.js").then(p=>p.B),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).default)}async function mt(){t(20,Qe=(await An(()=>import("./Login-C6lN_nrD.js"),__vite__mapDeps([6,7,8,9,10,11,12,13,3,1,2,4,14,15,16,17,18]),import.meta.url)).default)}function ze(){R.auth_required?mt():_t()}const Le={readable_error:{NO_APP_FILE:i("errors.no_app_file"),CONFIG_ERROR:i("errors.config_error"),BUILD_ERROR:i("errors.build_error"),RUNTIME_ERROR:i("errors.runtime_error"),PAUSED:i("errors.space_paused")},title(p){return encodeURIComponent(i("errors.space_not_working"))},description(p,W){return encodeURIComponent(`Hello,

Firstly, thanks for creating this space!

I noticed that the space isn't working correctly because there is ${this.readable_error[p]||"an error"}.

It would be great if you could take a look at this because this space is being embedded on ${W}.

Thanks!`)}};let He;li(async()=>{oi.register(ge,$)});let Se;async function We(p,W){if(p&&!W&&window.self===window.top){Se&&(Se.remove(),Se=void 0);const O=await Zs(p);O&&(Se=O.element)}}no(()=>{Se?.remove()});function $e(p){F=p,t(10,F)}function et(p){X=p,t(11,X)}function pt(p){He=p,t(21,He)}function ht(p){$=p,t(9,$)}return n.$$set=p=>{"autoscroll"in p&&t(0,s=p.autoscroll),"version"in p&&t(1,r=p.version),"initial_height"in p&&t(2,u=p.initial_height),"app_mode"in p&&t(3,a=p.app_mode),"is_embed"in p&&t(4,f=p.is_embed),"theme_mode"in p&&t(25,d=p.theme_mode),"control_page_title"in p&&t(5,h=p.control_page_title),"container"in p&&t(6,y=p.container),"info"in p&&t(7,T=p.info),"eager"in p&&t(26,M=p.eager),"mount_css"in p&&t(27,P=p.mount_css),"Client"in p&&t(28,b=p.Client),"worker_proxy"in p&&t(29,m=p.worker_proxy),"space"in p&&t(8,k=p.space),"host"in p&&t(30,V=p.host),"src"in p&&t(31,ve=p.src)},n.$$.update=()=>{n.$$.dirty[0]&4096&&R?.app_id&&R.app_id,n.$$.dirty[0]&9216&&t(15,be=!F&&N.load_status!=="error"?"pending":!F&&N.load_status==="error"?"error":N.load_status),n.$$.dirty[0]&67112960|n.$$.dirty[1]&2&&R&&(M||l[ge])&&ze(),n.$$.dirty[0]&2560&&X&&$.dispatchEvent(new CustomEvent("render",{bubbles:!0,cancelable:!1,composed:!0})),n.$$.dirty[0]&16400&&K?.config&&We(K?.config?.space_id,f)},[s,r,u,a,f,h,y,T,k,$,F,X,R,N,K,be,Ce,ie,I,at,Qe,He,i,A,Le,d,M,P,b,m,V,ve,l,$e,et,pt,ht]}class go extends js{constructor(e){super(),Qs(this,e,ho,uo,eo,{autoscroll:0,version:1,initial_height:2,app_mode:3,is_embed:4,theme_mode:25,control_page_title:5,container:6,info:7,eager:26,mount_css:27,Client:28,worker_proxy:29,space:8,host:30,src:31},null,[-1,-1])}get autoscroll(){return this.$$.ctx[0]}set autoscroll(e){this.$$set({autoscroll:e}),Z()}get version(){return this.$$.ctx[1]}set version(e){this.$$set({version:e}),Z()}get initial_height(){return this.$$.ctx[2]}set initial_height(e){this.$$set({initial_height:e}),Z()}get app_mode(){return this.$$.ctx[3]}set app_mode(e){this.$$set({app_mode:e}),Z()}get is_embed(){return this.$$.ctx[4]}set is_embed(e){this.$$set({is_embed:e}),Z()}get theme_mode(){return this.$$.ctx[25]}set theme_mode(e){this.$$set({theme_mode:e}),Z()}get control_page_title(){return this.$$.ctx[5]}set control_page_title(e){this.$$set({control_page_title:e}),Z()}get container(){return this.$$.ctx[6]}set container(e){this.$$set({container:e}),Z()}get info(){return this.$$.ctx[7]}set info(e){this.$$set({info:e}),Z()}get eager(){return this.$$.ctx[26]}set eager(e){this.$$set({eager:e}),Z()}get mount_css(){return this.$$.ctx[27]}set mount_css(e){this.$$set({mount_css:e}),Z()}get Client(){return this.$$.ctx[28]}set Client(e){this.$$set({Client:e}),Z()}get worker_proxy(){return this.$$.ctx[29]}set worker_proxy(e){this.$$set({worker_proxy:e}),Z()}get space(){return this.$$.ctx[8]}set space(e){this.$$set({space:e}),Z()}get host(){return this.$$.ctx[30]}set host(e){this.$$set({host:e}),Z()}get src(){return this.$$.ctx[31]}set src(e){this.$$set({src:e}),Z()}}const ko=Object.freeze(Object.defineProperty({__proto__:null,default:go},Symbol.toStringTag,{value:"Module"}));export{Ul as C,il as E,Nl as I,ml as L,os as S,ko as a,Eo as c,To as g,yo as o,Ao as p,Cn as s};
//# sourceMappingURL=Index-DB1XLvMK.js.map

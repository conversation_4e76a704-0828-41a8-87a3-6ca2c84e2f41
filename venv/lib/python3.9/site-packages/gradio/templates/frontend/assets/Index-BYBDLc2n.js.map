{"version": 3, "file": "Index-BYBDLc2n.js", "sources": ["../../../../js/annotatedimage/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\n\timport { onMount } from \"svelte\";\n\timport { Block, BlockLabel, Empty, IconButton } from \"@gradio/atoms\";\n\timport { Image, Maximize, Minimize } from \"@gradio/icons\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { type FileData } from \"@gradio/client\";\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: {\n\t\timage: FileData;\n\t\tannotations: { image: FileData; label: string }[] | [];\n\t} | null = null;\n\tlet old_value: {\n\t\timage: FileData;\n\t\tannotations: { image: FileData; label: string }[] | [];\n\t} | null = null;\n\tlet _value: {\n\t\timage: FileData;\n\t\tannotations: { image: FileData; label: string }[];\n\t} | null = null;\n\texport let gradio: Gradio<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t}>;\n\texport let label = gradio.i18n(\"annotated_image.annotated_image\");\n\texport let show_label = true;\n\texport let show_legend = true;\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\texport let color_map: Record<string, string>;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\tlet active: string | null = null;\n\texport let loading_status: LoadingStatus;\n\texport let show_fullscreen_button = true;\n\n\tlet is_full_screen = false;\n\tlet image_container: HTMLElement;\n\n\tonMount(() => {\n\t\tdocument.addEventListener(\"fullscreenchange\", () => {\n\t\t\tis_full_screen = !!document.fullscreenElement;\n\t\t});\n\t});\n\n\tconst toggle_full_screen = async (): Promise<void> => {\n\t\tif (!is_full_screen) {\n\t\t\tawait image_container.requestFullscreen();\n\t\t} else {\n\t\t\tawait document.exitFullscreen();\n\t\t}\n\t};\n\n\t// `value` can be updated before the Promises from `resolve_wasm_src` are resolved.\n\t// In such a case, the resolved values for the old `value` have to be discarded,\n\t// This variable `latest_promise` is used to pick up only the values resolved for the latest `value`.\n\tlet latest_promise: Promise<unknown> | null = null;\n\t$: {\n\t\tif (value !== old_value) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t\tif (value) {\n\t\t\tconst normalized_value = {\n\t\t\t\timage: value.image as FileData,\n\t\t\t\tannotations: value.annotations.map((ann) => ({\n\t\t\t\t\timage: ann.image as FileData,\n\t\t\t\t\tlabel: ann.label\n\t\t\t\t}))\n\t\t\t};\n\t\t\t_value = normalized_value;\n\n\t\t\t// In normal (non-Wasm) Gradio, the `<img>` element should be rendered with the passed values immediately\n\t\t\t// without waiting for `resolve_wasm_src()` to resolve.\n\t\t\t// If it waits, a blank image is displayed until the async task finishes\n\t\t\t// and it leads to undesirable flickering.\n\t\t\t// So set `_value` immediately above, and update it with the resolved values below later.\n\t\t\tconst image_url_promise = resolve_wasm_src(normalized_value.image.url);\n\t\t\tconst annotation_urls_promise = Promise.all(\n\t\t\t\tnormalized_value.annotations.map((ann) =>\n\t\t\t\t\tresolve_wasm_src(ann.image.url)\n\t\t\t\t)\n\t\t\t);\n\t\t\tconst current_promise = Promise.all([\n\t\t\t\timage_url_promise,\n\t\t\t\tannotation_urls_promise\n\t\t\t]);\n\t\t\tlatest_promise = current_promise;\n\t\t\tcurrent_promise.then(([image_url, annotation_urls]) => {\n\t\t\t\tif (latest_promise !== current_promise) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst async_resolved_value: typeof _value = {\n\t\t\t\t\timage: {\n\t\t\t\t\t\t...normalized_value.image,\n\t\t\t\t\t\turl: image_url ?? undefined\n\t\t\t\t\t},\n\t\t\t\t\tannotations: normalized_value.annotations.map((ann, i) => ({\n\t\t\t\t\t\t...ann,\n\t\t\t\t\t\timage: {\n\t\t\t\t\t\t\t...ann.image,\n\t\t\t\t\t\t\turl: annotation_urls[i] ?? undefined\n\t\t\t\t\t\t}\n\t\t\t\t\t}))\n\t\t\t\t};\n\t\t\t\t_value = async_resolved_value;\n\t\t\t});\n\t\t} else {\n\t\t\t_value = null;\n\t\t}\n\t}\n\tfunction handle_mouseover(_label: string): void {\n\t\tactive = _label;\n\t}\n\tfunction handle_mouseout(): void {\n\t\tactive = null;\n\t}\n\n\tfunction handle_click(i: number, value: string): void {\n\t\tgradio.dispatch(\"select\", {\n\t\t\tvalue: label,\n\t\t\tindex: i\n\t\t});\n\t}\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tpadding={false}\n\t{height}\n\t{width}\n\tallow_overflow={false}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t/>\n\t<BlockLabel\n\t\t{show_label}\n\t\tIcon={Image}\n\t\tlabel={label || gradio.i18n(\"image.image\")}\n\t/>\n\n\t<div class=\"container\">\n\t\t{#if _value == null}\n\t\t\t<Empty size=\"large\" unpadded_box={true}><Image /></Empty>\n\t\t{:else}\n\t\t\t<div class=\"image-container\" bind:this={image_container}>\n\t\t\t\t<div class=\"icon-buttons\">\n\t\t\t\t\t{#if !is_full_screen && show_fullscreen_button}\n\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\tIcon={Maximize}\n\t\t\t\t\t\t\tlabel=\"View in full screen\"\n\t\t\t\t\t\t\ton:click={toggle_full_screen}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\n\t\t\t\t\t{#if is_full_screen}\n\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\tIcon={Minimize}\n\t\t\t\t\t\t\tlabel=\"Exit full screen\"\n\t\t\t\t\t\t\ton:click={toggle_full_screen}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\t\t\t\t</div>\n\n\t\t\t\t<img\n\t\t\t\t\tclass=\"base-image\"\n\t\t\t\t\tclass:fit-height={height && !is_full_screen}\n\t\t\t\t\tsrc={_value ? _value.image.url : null}\n\t\t\t\t\talt=\"the base file that is annotated\"\n\t\t\t\t/>\n\t\t\t\t{#each _value ? _value?.annotations : [] as ann, i}\n\t\t\t\t\t<img\n\t\t\t\t\t\talt=\"segmentation mask identifying {label} within the uploaded file\"\n\t\t\t\t\t\tclass=\"mask fit-height\"\n\t\t\t\t\t\tclass:fit-height={!is_full_screen}\n\t\t\t\t\t\tclass:active={active == ann.label}\n\t\t\t\t\t\tclass:inactive={active != ann.label && active != null}\n\t\t\t\t\t\tsrc={ann.image.url}\n\t\t\t\t\t\tstyle={color_map && ann.label in color_map\n\t\t\t\t\t\t\t? null\n\t\t\t\t\t\t\t: `filter: hue-rotate(${Math.round(\n\t\t\t\t\t\t\t\t\t(i * 360) / _value?.annotations.length\n\t\t\t\t\t\t\t\t)}deg);`}\n\t\t\t\t\t/>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t\t{#if show_legend && _value}\n\t\t\t\t<div class=\"legend\">\n\t\t\t\t\t{#each _value.annotations as ann, i}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"legend-item\"\n\t\t\t\t\t\t\tstyle=\"background-color: {color_map && ann.label in color_map\n\t\t\t\t\t\t\t\t? color_map[ann.label] + '88'\n\t\t\t\t\t\t\t\t: `hsla(${Math.round(\n\t\t\t\t\t\t\t\t\t\t(i * 360) / _value.annotations.length\n\t\t\t\t\t\t\t\t\t)}, 100%, 50%, 0.3)`}\"\n\t\t\t\t\t\t\ton:mouseover={() => handle_mouseover(ann.label)}\n\t\t\t\t\t\t\ton:focus={() => handle_mouseover(ann.label)}\n\t\t\t\t\t\t\ton:mouseout={() => handle_mouseout()}\n\t\t\t\t\t\t\ton:blur={() => handle_mouseout()}\n\t\t\t\t\t\t\ton:click={() => handle_click(i, ann.label)}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ann.label}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t{/if}\n\t</div>\n</Block>\n\n<style>\n\t.base-image {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t\theight: auto;\n\t}\n\t.container {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\t.image-container {\n\t\tposition: relative;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tflex-grow: 1;\n\t\twidth: 100%;\n\t\toverflow: hidden;\n\t}\n\t.fit-height {\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: contain;\n\t}\n\t.mask {\n\t\topacity: 0.85;\n\t\ttransition: all 0.2s ease-in-out;\n\t\tposition: absolute;\n\t}\n\t.image-container:hover .mask {\n\t\topacity: 0.3;\n\t}\n\t.mask.active {\n\t\topacity: 1;\n\t}\n\t.mask.inactive {\n\t\topacity: 0;\n\t}\n\t.legend {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: wrap;\n\t\talign-content: center;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: var(--spacing-sm);\n\t\tpadding: var(--spacing-sm);\n\t}\n\t.legend-item {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--spacing-sm);\n\t}\n\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 6px;\n\t\tright: 6px;\n\t\tgap: var(--size-1);\n\t\tz-index: 1;\n\t}\n</style>\n"], "names": ["onMount", "if_block0", "ctx", "create_if_block_3", "create_if_block_2", "i", "if_block2", "create_if_block_1", "src_url_equal", "img", "img_src_value", "attr", "toggle_class", "insert", "target", "div1", "anchor", "append", "div0", "current", "dirty", "Maximize", "Minimize", "img_style_value", "each_value", "ensure_array_like", "div", "t0_value", "set_style", "button", "set_data", "t0", "Image", "blocklabel_changes", "elem_id", "$$props", "elem_classes", "visible", "value", "old_value", "_value", "gradio", "label", "show_label", "show_legend", "height", "width", "color_map", "container", "scale", "min_width", "active", "loading_status", "show_fullscreen_button", "is_full_screen", "image_container", "toggle_full_screen", "latest_promise", "handle_mouseover", "_label", "$$invalidate", "handle_mouseout", "handle_click", "$$value", "ann", "click_handler", "normalized_value", "image_url_promise", "resolve_wasm_src", "annotation_urls_promise", "current_promise", "image_url", "annotation_urls", "async_resolved_value"], "mappings": "40BAGU,CAAA,QAAAA,EAAA,SAAuB,mMA+JtBC,EAAA,CAAAC,OAAkBA,EAAsB,EAAA,GAAAC,GAAAD,CAAA,IAQzCA,EAAc,EAAA,GAAAE,GAAAF,CAAA,MAebA,EAAM,EAAA,EAAGA,OAAQ,qCAAtB,OAAIG,GAAA,qBAgBF,IAAAC,EAAAJ,MAAeA,EAAM,EAAA,GAAAK,GAAAL,CAAA,+NAnBnBM,EAAAC,EAAA,IAAAC,EAAAR,MAASA,EAAM,EAAA,EAAC,MAAM,IAAM,IAAI,GAAAS,EAAAF,EAAA,MAAAC,CAAA,+CADnBE,EAAAH,EAAA,aAAAP,OAAWA,EAAc,EAAA,CAAA,uDArB7CW,EAwCKC,EAAAC,EAAAC,CAAA,EAvCJC,EAgBKF,EAAAG,CAAA,8CAELD,EAKCF,EAAAN,CAAA,iHAtBM,CAAAP,OAAkBA,EAAsB,EAAA,sGAQzCA,EAAc,EAAA,0GAYd,CAAAiB,GAAAC,EAAA,CAAA,EAAA,OAAA,CAAAZ,EAAAC,EAAA,IAAAC,EAAAR,MAASA,EAAM,EAAA,EAAC,MAAM,IAAM,IAAI,oCADnBU,EAAAH,EAAA,aAAAP,OAAWA,EAAc,EAAA,CAAA,mBAIrCA,EAAM,EAAA,EAAGA,OAAQ,kCAAtB,OAAIG,GAAA,EAAA,mHAAJ,OAgBEH,MAAeA,EAAM,EAAA,wQA3CQ,+RAMxBmB,gDAEInB,EAAkB,EAAA,CAAA,uLAMtBoB,6CAEIpB,EAAkB,EAAA,CAAA,iOAaOA,EAAK,CAAA,EAAA,2BAAA,yDAKpCA,EAAG,EAAA,EAAC,MAAM,GAAG,GAAAS,EAAAF,EAAA,MAAAC,CAAA,EACXC,EAAAF,EAAA,QAAAc,EAAArB,EAAa,CAAA,GAAAA,EAAI,EAAA,EAAA,SAASA,EAAA,CAAA,EAC9B,2BACsB,KAAK,MAC1BA,EAAC,EAAA,EAAG,IAAOA,EAAQ,EAAA,GAAA,YAAY,MAAA,CAAA,OAAA,oBAPhBA,EAAc,EAAA,CAAA,eACnBA,EAAM,EAAA,GAAIA,EAAG,EAAA,EAAC,KAAK,EACjBU,EAAAH,EAAA,WAAAP,OAAUA,EAAG,EAAA,EAAC,OAASA,OAAU,IAAI,UALtDW,EAYCC,EAAAL,EAAAO,CAAA,2DAXoCd,EAAK,CAAA,EAAA,kEAKpCA,EAAG,EAAA,EAAC,MAAM,GAAG,gBACXkB,EAAA,CAAA,EAAA,OAAAG,KAAAA,EAAArB,EAAa,CAAA,GAAAA,EAAI,EAAA,EAAA,SAASA,EAAA,CAAA,EAC9B,2BACsB,KAAK,MAC1BA,EAAC,EAAA,EAAG,IAAOA,EAAQ,EAAA,GAAA,YAAY,MAAA,CAAA,wDAPhBA,EAAc,EAAA,CAAA,2BACnBA,EAAM,EAAA,GAAIA,EAAG,EAAA,EAAC,KAAK,cACjBU,EAAAH,EAAA,WAAAP,OAAUA,EAAG,EAAA,EAAC,OAASA,OAAU,IAAI,uCAY/CsB,EAAAC,EAAAvB,MAAO,WAAW,uBAAvB,OAAIG,GAAA,2HADPQ,EAkBKC,EAAAY,EAAAV,CAAA,4EAjBGQ,EAAAC,EAAAvB,MAAO,WAAW,oBAAvB,OAAIG,GAAA,EAAA,kHAAJ,qDAcCsB,EAAAzB,MAAI,MAAK,oMAXgB0B,EAAAC,EAAA,mBAAA3B,EAAa,CAAA,GAAAA,EAAI,EAAA,EAAA,SAASA,EAAA,CAAA,EACjDA,EAAU,CAAA,EAAAA,EAAI,EAAA,EAAA,KAAK,EAAI,aACf,KAAK,MACZA,EAAC,EAAA,EAAG,IAAOA,EAAO,EAAA,EAAA,YAAY,MAAA,CAAA,mBAAA,UALnCW,EAcQC,EAAAe,EAAAb,CAAA,mIADNI,EAAA,CAAA,EAAA,OAAAO,KAAAA,EAAAzB,MAAI,MAAK,KAAA4B,GAAAC,EAAAJ,CAAA,cAXgBC,EAAAC,EAAA,mBAAA3B,EAAa,CAAA,GAAAA,EAAI,EAAA,EAAA,SAASA,EAAA,CAAA,EACjDA,EAAU,CAAA,EAAAA,EAAI,EAAA,EAAA,KAAK,EAAI,aACf,KAAK,MACZA,EAAC,EAAA,EAAG,IAAOA,EAAO,EAAA,EAAA,YAAY,MAAA,CAAA,mBAAA,gPA/D3B,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,+GAIZ8B,GACC,MAAA9B,EAAS,CAAA,GAAAA,EAAO,CAAA,EAAA,KAAK,aAAa,0CAIpC,OAAAA,OAAU,KAAI,wLADpBW,EAmEKC,EAAAY,EAAAV,CAAA,sDA7EQI,EAAA,CAAA,EAAA,GAAA,CAAA,WAAAlB,KAAO,UAAU,EACvBkB,EAAA,CAAA,EAAA,GAAA,CAAA,KAAAlB,KAAO,IAAI,gBACbA,EAAc,EAAA,CAAA,yDAKXkB,EAAA,CAAA,EAAA,KAAAa,EAAA,MAAA/B,EAAS,CAAA,GAAAA,EAAO,CAAA,EAAA,KAAK,aAAa,uZAhBjC,yCAGO,khBAjIL,GAAA,CAAA,QAAAgC,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,EACd,CAAA,MAAAG,EAGA,IAAI,EAAAH,EACXI,EAGO,KACPC,EAGO,MACA,OAAAC,CAGT,EAAAN,EACS,CAAA,MAAAO,EAAQD,EAAO,KAAK,iCAAiC,CAAA,EAAAN,EACrD,CAAA,WAAAQ,EAAa,EAAI,EAAAR,EACjB,CAAA,YAAAS,EAAc,EAAI,EAAAT,GAClB,OAAAU,CAA0B,EAAAV,GAC1B,MAAAW,CAAyB,EAAAX,GACzB,UAAAY,CAAiC,EAAAZ,EACjC,CAAA,UAAAa,EAAY,EAAI,EAAAb,EAChB,CAAA,MAAAc,EAAuB,IAAI,EAAAd,EAC3B,CAAA,UAAAe,EAAgC,MAAS,EAAAf,EAChDgB,EAAwB,MACjB,eAAAC,CAA6B,EAAAjB,EAC7B,CAAA,uBAAAkB,EAAyB,EAAI,EAAAlB,EAEpCmB,EAAiB,GACjBC,EAEJvD,GAAO,IAAA,CACN,SAAS,iBAAiB,mBAAkB,IAAA,MAC3CsD,EAAc,CAAA,CAAK,SAAS,iBAAiB,YAIzCE,GAAkB,SAAA,CAClBF,EAGE,MAAA,SAAS,iBAFT,MAAAC,EAAgB,qBASpB,IAAAE,EAA0C,KAuDrC,SAAAC,EAAiBC,EAAc,CACvCC,EAAA,GAAAT,EAASQ,CAAM,WAEPE,GAAe,CACvBD,EAAA,GAAAT,EAAS,IAAI,WAGLW,EAAazD,EAAWiC,EAAa,CAC7CG,EAAO,SAAS,SAAQ,CACvB,MAAOC,EACP,MAAOrC,CAAA,CAAA,6CAgCiCkD,EAAeQ,wBAmD/BL,EAAiBM,EAAI,KAAK,QAC9BN,EAAiBM,EAAI,KAAK,SACvBH,WACJA,IACCI,GAAA,CAAA5D,EAAA2D,IAAAF,EAAazD,EAAG2D,EAAI,KAAK,soBAtJ1C1B,IAAUC,IACbqB,EAAA,GAAArB,EAAYD,CAAK,EACjBG,EAAO,SAAS,QAAQ,GAErBH,EAAK,OACF4B,EAAgB,CACrB,MAAO5B,EAAM,MACb,YAAaA,EAAM,YAAY,IAAK0B,IAAG,CACtC,MAAOA,EAAI,MACX,MAAOA,EAAI,KAAA,EAAA,GAGbJ,EAAA,GAAApB,EAAS0B,CAAgB,EAOnB,MAAAC,EAAoBC,EAAiBF,EAAiB,MAAM,GAAG,EAC/DG,GAA0B,QAAQ,IACvCH,EAAiB,YAAY,IAAKF,GACjCI,EAAiBJ,EAAI,MAAM,GAAG,CAAA,CAAA,EAG1BM,EAAkB,QAAQ,IAAG,CAClCH,EACAE,EAAA,CAAA,EAEDT,EAAA,GAAAH,EAAiBa,CAAe,EAChCA,EAAgB,KAAO,CAAA,CAAAC,EAAWC,EAAe,IAAA,CAC5C,GAAAf,IAAmBa,eAGjBG,GAAoB,CACzB,MAAK,CACD,GAAAP,EAAiB,MACpB,IAAKK,GAAa,QAEnB,YAAaL,EAAiB,YAAY,IAAG,CAAEF,EAAK3D,MAAC,IACjD2D,EACH,MAAK,CACD,GAAAA,EAAI,MACP,IAAKQ,GAAgBnE,EAAC,GAAK,YAI9BuD,EAAA,GAAApB,EAASiC,EAAoB,SAG9Bb,EAAA,GAAApB,EAAS,IAAI"}
{"version": 3, "mappings": ";g6BAAA,SAASA,GAAEA,EAAEC,EAAEC,EAAEC,EAAE,CAAC,OAAO,IAAID,IAAIA,EAAE,UAAW,SAASE,EAAEC,EAAE,CAAC,SAASC,EAAEN,EAAE,CAAC,GAAG,CAACO,EAAEJ,EAAE,KAAKH,CAAC,CAAC,CAAC,OAAOA,EAAE,CAACK,EAAEL,CAAC,CAAC,CAAC,CAAC,SAASQ,EAAER,EAAE,CAAC,GAAG,CAACO,EAAEJ,EAAE,MAAMH,CAAC,CAAC,CAAC,OAAOA,EAAE,CAACK,EAAEL,CAAC,CAAC,CAAC,CAAC,SAASO,EAAEP,EAAE,CAAC,IAAIC,EAAED,EAAE,KAAKI,EAAEJ,EAAE,KAAK,GAAGC,EAAED,EAAE,MAAMC,aAAaC,EAAED,EAAE,IAAIC,EAAG,SAASF,EAAE,CAACA,EAAEC,CAAC,CAAC,CAAC,GAAI,KAAKK,EAAEE,CAAC,CAAC,CAACD,GAAGJ,EAAEA,EAAE,MAAMH,EAAK,EAAE,GAAG,KAAM,EAAC,CAAG,EAAqD,MAAMC,EAAC,CAAC,aAAa,CAAC,KAAK,UAAU,GAAG,KAAK,GAAG,KAAK,iBAAiB,KAAK,GAAG,KAAK,mBAAmB,CAAC,iBAAiB,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,KAAK,KAAK,UAAU,CAAC,EAAE,IAAI,CAAC,EAAiB,GAAE,KAAK,CAAC,MAAMC,EAAE,IAAI,CAAC,KAAK,oBAAoB,EAAEA,CAAC,EAAE,KAAK,oBAAoB,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,iBAAiB,EAAEA,CAAC,EAAEA,CAAC,CAAC,MAAM,IAAI,KAAK,oBAAoB,EAAE,CAAC,CAAC,CAAC,oBAAoB,EAAE,EAAE,CAAC,IAAI,GAAU,EAAE,KAAK,UAAU,CAAC,KAA1B,MAAuC,IAAT,QAAY,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,KAAK,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,UAAU,CAAE,EAAC,KAAK,KAAK,EAAE,CAAC,KAAK,UAAU,CAAC,GAAG,KAAK,UAAU,CAAC,EAAE,QAASF,GAAGA,EAAE,GAAG,CAAC,CAAG,EAAC,CAAC,MAAME,WAAUD,EAAC,CAAC,YAAY,EAAE,CAAC,QAAQ,KAAK,cAAc,CAAE,EAAC,KAAK,QAAQ,CAAC,CAAC,QAAQ,CAAE,MAAK,EAAE,CAAC,KAAK,WAAW,EAAE,KAAK,OAAQ,EAAC,SAAS,CAAC,KAAK,KAAK,SAAS,EAAE,KAAK,cAAc,QAAS,GAAG,EAAC,CAAI,EAAC,CAAC,MAAME,GAAE,CAAC,aAAa,YAAY,aAAa,YAAY,WAAW,EAAE,MAAMC,WAAUF,EAAC,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAE,EAAC,CAAC,EAAE,CAAC,oBAA2B,EAAE,EAAE,sBAAZ,MAA0C,IAAT,OAAW,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,OAAO,KAAK,KAAK,cAAc,IAAI,CAAC,OAAO,OAAO,EAAE,CAAC,OAAO,IAAIE,GAAE,GAAG,CAAE,EAAC,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,IAAI,aAAa,EAAE,EAAE,wBAAwB,CAAC,EAAED,EAAE,EAAE,eAAgB,EAAC,EAAE,QAAQA,CAAC,EAAE,MAAMC,EAAED,EAAE,kBAAkBE,EAAE,IAAI,aAAaD,CAAC,EAAEE,EAAEF,EAAE,EAAE,WAAW,IAAII,EAAE,MAAMD,EAAE,IAAI,CAACJ,EAAE,uBAAuBE,CAAC,EAAE,KAAK,aAAa,KAAK,WAAW,QAAQ,YAAY,EAAE,KAAK,WAAW,QAAQ,SAAS,GAAG,KAAK,WAAW,KAAK,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAGE,EAAE,sBAAsBD,CAAC,CAAC,EAAE,OAAOA,IAAI,IAAI,CAAC,qBAAqBC,CAAC,EAAW,GAAE,WAAY,EAAU,GAAE,OAAO,CAAC,CAAC,SAASP,EAAE,CAAC,OAAOD,GAAE,KAAK,OAAO,OAAQ,WAAW,CAAC,IAAIA,EAAE,GAAG,CAACA,EAAE,MAAM,UAAU,aAAa,aAAa,CAAC,MAAM,CAAiBC,GAAE,UAAW,CAAC,SAASA,EAAE,QAAQ,CAAC,CAAC,CAAC,OAAOD,EAAE,CAAC,MAAM,IAAI,MAAM,mCAAmCA,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,gBAAgBA,CAAC,EAAE,OAAO,KAAK,cAAc,KAAK,KAAK,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,OAAOA,EAAEA,CAAC,EAAG,CAAC,SAAS,CAAC,KAAK,SAAS,KAAK,OAAO,UAAS,EAAG,QAAS,GAAG,EAAE,KAAM,GAAG,KAAK,OAAO,KAAK,KAAK,cAAc,KAAK,CAAC,eAAeC,EAAE,CAAC,OAAOD,GAAE,KAAK,OAAO,OAAQ,WAAW,CAAC,MAAMA,EAAE,KAAK,SAAS,MAAM,KAAK,SAASC,CAAC,GAAG,EAAE,KAAK,eAAe,IAAI,cAAcD,EAAE,CAAC,SAAS,KAAK,QAAQ,UAAUG,GAAE,KAAMH,GAAG,cAAc,gBAAgBA,CAAC,GAAI,mBAAmB,KAAK,QAAQ,kBAAkB,CAAC,EAAE,KAAK,cAAc,EAAE,KAAK,cAAe,EAAC,MAAMI,EAAE,CAAE,EAAC,EAAE,gBAAgBJ,GAAG,CAACA,EAAE,KAAK,KAAK,GAAGI,EAAE,KAAKJ,EAAE,IAAI,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,IAAIA,EAAE,MAAMC,EAAE,IAAI,KAAKG,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,KAAK,KAAK,aAAaH,CAAC,EAAO,KAAK,QAAQ,sBAAlB,MAAgDD,EAAE,KAAK,cAAf,MAAqCA,IAAT,QAAYA,EAAE,KAAK,IAAI,gBAAgBC,CAAC,CAAC,EAAE,EAAE,EAAE,MAAK,EAAG,KAAK,KAAK,cAAc,CAAC,CAAG,EAAC,aAAa,CAAC,IAAI,EAAE,QAA6B,EAAE,KAAK,iBAAf,MAAwC,IAAT,OAAW,OAAO,EAAE,SAAlE,WAAwE,CAAC,UAAU,CAAC,IAAI,EAAE,QAA0B,EAAE,KAAK,iBAAf,MAAwC,IAAT,OAAW,OAAO,EAAE,SAA/D,QAAqE,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,YAAa,KAAW,EAAE,KAAK,iBAAf,MAAwC,IAAT,QAAY,EAAE,KAAI,EAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,YAAW,KAAa,EAAE,KAAK,iBAAf,MAAwC,IAAT,QAAY,EAAE,MAAO,EAAC,KAAK,KAAK,cAAc,EAAE,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,SAAQ,KAAa,EAAE,KAAK,iBAAf,MAAwC,IAAT,QAAY,EAAE,SAAS,KAAK,KAAK,eAAe,EAAE,CAAC,OAAO,0BAA0B,CAAC,OAAOD,GAAE,KAAK,OAAO,OAAQ,WAAW,CAAC,OAAO,UAAU,aAAa,iBAAkB,EAAC,KAAM,GAAG,EAAE,OAAQA,GAAkBA,EAAE,OAAjB,aAAyB,EAAG,EAAC,SAAS,CAAC,MAAM,QAAS,EAAC,KAAK,cAAe,EAAC,KAAK,SAAS,CAAC,8OCGlqH,CAAAS,iCAAqC,6GAsCtCC,EAAU,yBAAf,OAAIR,GAAA,iKAACQ,EAAU,sBAAf,OAAIR,GAAA,6HAAJ,sDAFgBS,EAAAD,KAAK,qBAAqB,8EAA5CE,GAAsDC,EAAAC,EAAAC,CAAA,kBAApCC,EAAA,GAAAL,OAAAD,KAAK,qBAAqB,OAAAO,GAAAhB,EAAAU,CAAA,wCAGPA,EAAAD,KAAU,MAAK,yCAApCI,EAAA,QAAAI,EAAAR,KAAU,iCAAzBE,GAA4DC,EAAAC,EAAAC,CAAA,kBAAxBC,EAAA,GAAAL,OAAAD,KAAU,MAAK,KAAAO,GAAAhB,EAAAU,CAAA,EAApCK,EAAA,GAAAE,OAAAR,KAAU,wGAJtBA,EAAU,GAAC,SAAW,EAACS,wJAFlBT,EAAU,GAAC,SAAW,UAHjCE,GAYQC,EAAAO,EAAAL,CAAA,oGATGL,EAAU,GAAC,SAAW,2EA/BrB,KAAAW,CAAmB,EAAAC,GACnB,WAAAC,EAAU,IAAAD,EAEf,MAAAE,EAAWf,0HAIhB,QACIgB,EAAW,GACfC,GAAa,yBAAwB,EAAG,KACtCC,GAA0B,CAC1BC,EAAA,EAAAL,EAAaI,CAAO,EACpBA,EAAQ,QAASE,GAAM,CAClBA,EAAO,UACVJ,EAAY,KAAKI,CAAM,IAGzBD,EAAA,EAAAL,EAAaE,CAAW,UAGlBK,EAAG,CACP,MAAAA,aAAe,cAAgBA,EAAI,MAAQ,mBAC9CN,EAAS,QAASH,EAAK,8BAA8B,GAEhDS,soBC2EmCpB,EAAW,mEAAnDE,GAA0DC,EAAAkB,EAAAhB,CAAA,2BAAlBL,EAAW,8CA1CTsB,EAAAtB,KAAK,cAAc,WAazDuB,EAAAvB,KAAK,YAAY,aAcjBwB,EAAAxB,KAAK,YAAY,iBAYsByB,EAAAzB,KAAK,cAAc,kCAE1D,IAAA0B,EAAA1B,OAAWA,EAAuB,IAAAS,GAAAT,CAAA,6ZApClB2B,EAAAC,EAAA,QAAAC,EAAA,gBAAA7B,EAAO,YAAQ,EAAK,qBAAuB,IAAE,kSAVpEE,GAmDKC,EAAA2B,EAAAzB,CAAA,EAlDJ0B,EAgDKD,EAAAE,CAAA,EA/CJD,EAIAC,EAAAC,CAAA,yBAEAF,EAWAC,EAAAJ,CAAA,yBAEAG,EAYAC,EAAAE,CAAA,yBAEAH,EAKAC,EAAAG,CAAA,+BACAJ,EAIAC,EAAAI,CAAA,8LAxC2C,CAAAC,GAAA/B,EAAA,IAAAgB,OAAAtB,KAAK,cAAc,OAAAO,GAAA+B,EAAAhB,CAAA,GAazD,CAAAe,GAAA/B,EAAA,IAAAiB,OAAAvB,KAAK,YAAY,OAAAO,GAAAgC,EAAAhB,CAAA,GARD,CAAAc,GAAA/B,EAAA,GAAAuB,OAAA,gBAAA7B,EAAO,YAAa,uBAAuB,IAAE,qCAsB7D,CAAAqC,GAAA/B,EAAA,IAAAkB,OAAAxB,KAAK,YAAY,OAAAO,GAAAiC,EAAAhB,CAAA,GAYsB,CAAAa,GAAA/B,EAAA,IAAAmB,OAAAzB,KAAK,cAAc,OAAAO,GAAAkC,EAAAhB,CAAA,EAE1DzB,OAAWA,EAAuB,oYAjG7B,OAAA0C,CAAoB,EAAA9B,GACpB,KAAAD,CAAmB,EAAAC,EAE1BC,EAAU,GACV8B,EACAC,EACAC,EACAC,EACAC,GAEO,YAAAC,CAAmB,EAAApC,GACnB,wBAAAqC,CAA4C,EAAArC,EAC5C,QAAAsC,EAAS,EAAK,EAAAtC,4CA0CZ+B,EAAYQ,kBAEP,MAAAC,EAAA,IAAAV,EAAO,2DAIZI,EAAUK,+BAGhBT,EAAO,aACVA,EAAO,gBAAe,EACtBA,EAAO,cAAa,GAGrBA,EAAO,cAAa,6CAKVK,EAAgBI,gCAItBT,EAAO,aACVA,EAAO,gBAAe,EACtBA,EAAO,cAAa,GAGrBA,EAAO,cAAa,6CAMVE,EAAWO,kBAEN,MAAAE,EAAA,IAAAX,EAAO,2DAGZG,EAAYM,kBAEP,MAAAG,EAAA,IAAAZ,EAAO,4RAjFtBA,EAAO,GAAG,eAAc,KAC1BA,EAAO,SAAQ,EAEfxB,EAAA,EAAAyB,EAAa,MAAM,QAAU,OAAMA,CAAA,EACnCzB,EAAA,EAAA4B,EAAW,MAAM,QAAU,OAAMA,CAAA,EACjC5B,EAAA,EAAA0B,EAAY,MAAM,QAAU,QAAOA,CAAA,kBAGjCF,EAAO,GAAG,aAAY,KACpBA,EAAO,aACVA,EAAO,gBAAe,EACtBA,EAAO,cAAa,GAErBA,EAAO,QAAO,EAEdxB,EAAA,EAAAyB,EAAa,MAAM,QAAU,OAAMA,CAAA,EACnCzB,EAAA,EAAA4B,EAAW,MAAM,QAAU,OAAMA,CAAA,EACjC5B,EAAA,EAAA0B,EAAY,MAAM,QAAU,OAAMA,CAAA,MAClCD,EAAa,SAAW,GAAKA,CAAA,kBAG3BD,EAAO,GAAG,eAAc,KAC1BxB,EAAA,EAAA0B,EAAY,MAAM,QAAU,OAAMA,CAAA,EAClC1B,EAAA,EAAA2B,EAAa,MAAM,QAAU,QAAOA,CAAA,EACpC3B,EAAA,EAAA4B,EAAW,MAAM,QAAU,OAAMA,CAAA,EACjC5B,EAAA,GAAA6B,EAAiB,MAAM,QAAU,OAAMA,CAAA,kBAGrCL,EAAO,GAAG,gBAAe,KAC3BxB,EAAA,EAAA0B,EAAY,MAAM,QAAU,QAAOA,CAAA,EACnC1B,EAAA,EAAA2B,EAAa,MAAM,QAAU,OAAMA,CAAA,EACnC3B,EAAA,EAAAyB,EAAa,MAAM,QAAU,OAAMA,CAAA,EACnCzB,EAAA,EAAA4B,EAAW,MAAM,QAAU,OAAMA,CAAA,EACjC5B,EAAA,GAAA6B,EAAiB,MAAM,QAAU,OAAMA,CAAA,u9BCpD/B,SAAAQ,EAAA,SAAuB,2BAEvB,uBAAAxD,WAAqC,wDAgNtCyD,EAAAxD,EAAS,aAAUA,MAAe,GAACyD,GAAAzD,CAAA,yBAGnCA,EAAM,IAAA0D,yMANbxD,GAYKC,EAAA2B,EAAAzB,CAAA,EAXJ0B,EAAiDD,EAAAT,CAAA,kBACjDU,EASKD,EAAAE,CAAA,4CARChC,EAAS,aAAUA,MAAe,0LACTC,EAAA0D,GAAY3D,EAAY,4FAArDE,GAA6DC,EAAAkB,EAAAhB,CAAA,iBAAhCC,EAAA,UAAAL,OAAA0D,GAAY3D,EAAY,UAAAO,GAAAhB,EAAAU,CAAA,kIAKrDC,GAAyDC,EAAAkB,EAAAhB,CAAA,kEAFjCJ,EAAA0D,GAAY3D,EAAO,uFAA3CE,GAAmDC,EAAAkB,EAAAhB,CAAA,iBAA3BC,EAAA,UAAAL,OAAA0D,GAAY3D,EAAO,UAAAO,GAAAhB,EAAAU,CAAA,iGAapB,wBAAAD,KAAiB,wBAC7B,YAAA2D,GAAY3D,EAAO,kNADPM,EAAA,OAAAsD,EAAA,wBAAA5D,KAAiB,yBAC7BM,EAAA,WAAAsD,EAAA,YAAAD,GAAY3D,EAAO,qQAOrBA,EAAkB,0EAKhB,8FANEA,EAAiB,yBAAjBA,EAAiB,qRACrBA,EAAkB,yNADdA,EAAiB,sPA5B5BwD,GAAAxD,EAAU,KAAAA,EAAkB,MAAAA,KAAiB,yBAAuB6D,GAAA7D,CAAA,EAgBrE8D,EAAA9D,QAAwBA,EAAa,KAAA+D,GAAA/D,CAAA,EAUrCgE,EAAAhE,MAAqBA,EAAa,KAAAS,GAAAT,CAAA,qRAlCxCE,GAmDKC,EAAA8D,EAAA5D,CAAA,EAlDJ0B,EAICkC,EAAAjC,CAAA,kBACDD,EAAsEkC,EAAAnC,CAAA,2FAEhE9B,EAAU,KAAAA,EAAkB,MAAAA,KAAiB,+EAgB9CA,QAAwBA,EAAa,2GAUrCA,MAAqBA,EAAa,4PA5N5B,KAAAkE,CAAY,EAAAtD,GACZ,KAAAD,CAAmB,EAAAC,GACnB,cAAAuD,CAGmB,EAAAvD,GACnB,kBAAAwD,CAAsC,EAAAxD,GACtC,iBAAAyD,EAAgB,CAC1B,wBAAyB,KAAAzD,GAEf,mBAAA0D,CAA8B,EAAA1D,EAC9B,UAAA2D,EAAW,EAAI,EAAA3D,EAEtB4D,EACAC,EACAC,EAAU,GAEVC,EACAC,EAEAlC,EACAmC,EAA+B,KAG/BC,EACAC,EACAC,EACAC,EAAU,EACVC,EACAhC,EAAS,GAETiC,EAAe,QAEbC,EAAc,KACnB,cAAcF,CAAQ,EACtBA,EAAW,sBACVD,IAAOA,CAAA,GACL,MAGEnE,EAAWf,cAWRsF,GAAqB,CAIzB,GAHJD,IACAlE,EAAA,GAAAgC,EAAS,EAAI,EACbpC,EAAS,iBAAiB,EACtBuD,EAAiB,wBAAuB,CACvC,IAAAiB,EAAiBV,EACjBU,IAAgBA,EAAe,MAAM,QAAU,UAItC,eAAAC,EAAoBC,EAAU,CAC5CtE,EAAA,GAAA+D,EAAU,CAAC,EACX/D,EAAA,GAAAgC,EAAS,EAAK,EACd,cAAcgC,CAAQ,YAEfO,EAAY,MAASD,EAAK,cAI1BE,EAAqB,UAHP,aAAY,CAC/B,WAAYtB,EAAkB,aAEI,gBAAgBqB,CAAY,EAE3DC,SACGC,GAAcD,CAAY,EAAE,WAAYE,IAAiB,OACxDzB,EAAa,CAAEyB,EAAK,EAAG,QAAQ,QAC/BzB,EAAa,CAAEyB,EAAK,EAAG,gBAAgB,UAEvCtG,EAAC,CACT,QAAQ,MAAMA,CAAC,SAkCXuG,EAAmB,KACpBjB,GAAqB1D,EAAA,GAAA0D,EAAoB,UAAY,GAAEA,CAAA,EACvDJ,IAAgB,QAAWA,EAAY,QAAO,EAC7CI,IACLJ,EAAcsB,GAAW,OAAM,IAC3B1B,EACH,UAAW,GACX,UAAWQ,IAGZ1D,EAAA,EAAAwB,EAAS8B,EAAY,eAAexD,GAAa,OAAM,IACvD0B,EAAO,SAAQ,EACfA,GAAQ,GAAG,aAAc6C,CAAmB,EAC5C7C,GAAQ,GAAG,eAAgB2C,CAAqB,EAChD3C,GAAQ,GAAG,eAAc,KACxB5B,EAAS,iBAAiB,EAC1B,cAAcoE,CAAQ,IAGvBxC,GAAQ,GAAG,aAAe8C,GAAI,CAC7BtE,EAAA,GAAA2D,EAAgB,IAAI,gBAAgBW,CAAI,GAElC,MAAAO,EAAanB,EACboB,EAAYrB,EAEdoB,IAAYA,EAAW,MAAM,QAAU,QACvCC,GAAanB,IAChBmB,EAAU,UAAY,GACtBC,SAKGA,EAAyB,KAC1B,IAAAD,EAAYrB,EACX,CAAAE,IAAkBmB,OACvBvB,EAAoBqB,GAAW,OAAM,CACpC,UAAWE,EACX,IAAKnB,EACF,GAAAT,MAIC8B,EAAiB,MACtBC,EACAC,IAAW,CAEXlF,EAAA,EAAAgD,EAAO,MAAM,QACPmC,EAAc5B,EAAkB,iBAClC4B,GAAW,MACRV,GAAcU,EAAaF,EAAOC,CAAG,EAAE,KAAI,MACzCE,GAAwB,OACxBnC,EAAa,CAAEmC,CAAY,EAAG,QAAQ,QACtCnC,EAAa,CAAEmC,CAAY,EAAG,gBAAgB,EACpD7B,EAAkB,QAAO,EACzBwB,MAGHnF,EAAS,MAAM,GAGhByC,GAAO,KACNsC,IAEA,OAAO,iBAAiB,UAAYvG,GAAC,CAChCA,EAAE,MAAQ,aACbiH,GAAW9B,EAAmB,EAAG,EACvBnF,EAAE,MAAQ,aACpBiH,GAAW9B,EAAiB,GAAM,gDASzBG,EAAmBzB,sDAGfwB,EAAkBxB,sDAIf2B,EAAO3B,4DAQL4B,EAAW5B,yDAkBfsB,EAAiB+B,qZA5I/B9D,GAAQ,GAAG,gBAAe,KAC5B0C,yBAGEX,GAAmB,GAAG,SAAWgC,GAAa,CAChDvF,EAAA,GAAA8D,EAAiByB,CAAQ,EACzB1B,OAAgBA,EAAY,YAAcpB,GAAY8C,CAAQ,EAAA1B,CAAA,uBAG5DN,GAAmB,GACrB,aACCiC,GACA5B,OAAYA,EAAQ,YAAcnB,GAAY+C,CAAW,EAAA5B,CAAA,qBAGxDL,GAAmB,GAAG,QAAO,KAC/B3D,EAAS,OAAO,EAChBI,EAAA,EAAAwD,EAAU,EAAK,sBAGbD,GAAmB,GAAG,OAAM,KAC9B3D,EAAS,MAAM,EACfI,EAAA,EAAAwD,EAAU,EAAI,sBAGZD,GAAmB,GAAG,SAAQ,KAChC3D,EAAS,MAAM,EACfI,EAAA,EAAAwD,EAAU,EAAK,4uCCzHP,SAAAnB,EAAA,SAAuB,sFA6CfvD,EAAS,GAAG,QAAU,MAAM,UAF5CE,GAGCC,EAAAwG,EAAAtG,CAAA,uCADeL,EAAS,GAAG,QAAU,MAAM,wDA6BzC4G,EAAA5G,KAAK,cAAc,0LAVrBE,GAWQC,EAAA0G,EAAAxG,CAAA,EAJP0B,EAEM8E,EAAAC,CAAA,wDACLxG,EAAA,IAAAsG,OAAA5G,KAAK,cAAc,OAAAO,GAAAwG,EAAAH,CAAA,wDAbnB5G,EAAgB,GAAGA,KAAK,aAAa,EAAIA,KAAK,YAAY,uLAVpDA,EAAgB,GAAG,qBAAuB,aAAa,6BAD/DE,GAYQC,EAAA0G,EAAAxG,CAAA,EALP0B,EAGM8E,EAAAG,CAAA,sEACLhH,EAAgB,GAAGA,KAAK,aAAa,EAAIA,KAAK,YAAY,QAAAO,GAAAgC,EAAAhB,CAAA,iBAVpDvB,EAAgB,GAAG,qBAAuB,aAAa,iGAT5DwD,EAAAxD,KAAiB,yBAAuB+D,GAAA/D,CAAA,yBAOvCA,EAAS,GAAAS,qUARhBP,GAuCKC,EAAA2B,EAAAzB,CAAA,wBAhCJ0B,EA+BKD,EAAAE,CAAA,iDArCAhC,KAAiB,kWAnCX,cAAAgG,EAAY,EAAK,EAAApF,EACjB,kBAAAqG,EAAmB,EAAK,EAAArG,GACxB,KAAAsG,CAAgB,EAAAtG,GAChB,OAAA8B,CAAkB,EAAA9B,GAClB,KAAAD,CAAmB,EAAAC,GACnB,kBAAAwD,CAAsC,EAAAxD,GACtC,iBAAAyD,EAAgB,CAC1B,wBAAyB,KAAAzD,EAGtB4D,EACA2C,EAEAvC,EAEA/D,EAAU,GAEd0C,GAAO,KACNsC,YAGKA,EAAmB,KACpBrB,IAAgB,QAAWA,EAAY,QAAO,EAC7CI,IACLJ,EAAcsB,GAAW,OAAM,IAC3B1B,EACH,OAAQ,IACR,UAAWQ,IAGZ1D,EAAA,EAAAiG,EAAiB3C,EAAY,eAAexD,GAAa,OAAM,gDAOnD4D,EAAmBzB,wBAS5BgE,GAAgB,QAAO,EACvBD,YAaAC,GAAgB,SAAQ,EACxBzE,8iDCpEc,sBAAA3C,IAAuB,OAAgB,6FAsQ7CC,EAAoB,GAAGA,KAAM,IAAM,cACnC,oBAHAA,EAAK,6jBAELA,EAAoB,GAAGA,KAAM,IAAM,8jBA7CzC,OAAAA,OAAkB,aAAY,EAyBzBA,OAAkB,SAAQ,0wBAIzBA,EAAW,idA5B2B,EAAI,kBAArBA,EAAK,iDAC/BA,EAAS,+5DARVoH,GACC,MAAApH,EAAkB,eAAYA,OAAU,WACxCA,EAAK,IAAIA,EAAI,IAAC,aAAa,iDAG7BA,EAAK,KAAK,MAAQA,EAAS,qFAqEyBA,EAAK,8PAtE/DE,GAuEKC,EAAAwG,EAAAtG,CAAA,2FA1EGC,EAAA,OAAA+G,EAAA,MAAArH,EAAkB,eAAYA,OAAU,0BACxCA,EAAK,IAAIA,EAAI,IAAC,aAAa,qZAlK5B,MAAAsH,GAAmB,IACnBC,GAAmB,oDAtCd,OAAAf,EAAyB,IAAI,EAAA5F,GAC7B,MAAA4G,CAAa,EAAA5G,GACb,KAAA6G,CAAY,EAAA7G,GACZ,KAAA8G,CAAa,EAAA9G,EACb,YAAA+G,EAAa,EAAI,EAAA/G,EACjB,sBAAAgH,EAAuB,EAAK,EAAAhH,GAC5B,QAAAiH,EAAO,CAIa,aAAc,QAAQ,GAAAjH,EAC1C,SAAAkH,EAAU,EAAK,EAAAlH,EACf,WAAAmH,EAAY,EAAK,EAAAnH,GACjB,KAAAD,CAAmB,EAAAC,GACnB,kBAAAwD,CAAsC,EAAAxD,GACtC,qBAAAoH,EAAoB,IAAApH,GACpB,iBAAAyD,EAAgB,IAAAzD,GAChB,SAAAqH,CAAiB,EAAArH,GACjB,cAAAsH,CAAsC,EAAAtH,GACtC,mBAAA0D,EAAkB,UAClB,UAAAC,EAAW,EAAI,EAAA3D,EACf,eAAAuH,EAA+B,IAAI,EAAAvH,GACnC,OAAAwH,CAAwB,EAAAxH,GACxB,eAAAyH,CAAgC,EAAAzH,EAMvCoF,EAAY,GACZsC,EACApE,EAAO,GACPqE,EACAC,EAAc,GACdC,EAAuC,GACvCC,EAAS,GAITC,EAAY,GACZC,WAKKC,GAAW,CACnBD,EAAe,eACP,sBAA2B,4DAC3B,sBAAuC,2CAI5Cb,GACHc,IAGK,MAAA/H,EAAWf,KAiBXoE,EAAa,MAClB2E,EACAC,IAA6C,CAEzC,IAAAC,EAAkB,SAAKF,EAAO,WAAW,EACvC,MAAAG,SAAYC,GAAa,CAAEF,CAAW,EAAGD,IAAU,QAAQ,EACjE7H,EAAA,EAAAsF,SACQ4B,EAAOa,GAAKxB,EAAM,OAAWU,GAAiB,MAAS,IAAI,OACjE,OAAO,EAEP,CAAC,GACHrH,EAASiI,EAAOvC,CAAK,GAGtB2C,GAAS,KACJpB,GAAaO,GAAYA,EAAS,QAAU,YAC/CA,EAAS,KAAI,mBAIAc,IAAa,KACvBC,MAGHA,EAAM,MAAS,UAAU,aAAa,aAAY,CAAG,MAAO,EAAI,SACxDjI,EAAG,CACN,cAAU,aAAY,CAC1BN,EAAS,QAASH,EAAK,yBAAyB,UAG7C,GAAAS,aAAe,cAAgBA,EAAI,MAAQ,kBAAiB,CAC/DN,EAAS,QAASH,EAAK,8BAA8B,gBAGhDS,EAEH,GAAAiI,GAAU,SACVtB,EAAS,QACH,cAAAuB,EAAe,SAAAC,CAAQ,EAAM,SAAAC,WAC/B,QAAQ,IAAIZ,CAAe,EAC5B,MAAAW,QAAeC,GAAO,GAC5BlB,MAAegB,EAAcD,EAAU,UAAU,WAAW,GAC5Df,EAAS,iBAAiB,gBAAiBmB,EAAY,OAEvDnB,EAAQ,IAAO,cAAce,CAAM,EACnCf,EAAS,iBAAiB,gBAAkBS,GAAK,CAChDJ,EAAa,KAAKI,EAAM,IAAI,IAE7BT,EAAS,iBAAiB,OAAM,UAC/BpH,EAAA,GAAA8E,EAAY,EAAK,QACX7B,EAAcwE,EAAc,QAAQ,QACpCxE,EAAcwE,EAAc,gBAAgB,EAClDA,EAAY,KAGdD,EAAS,IAGK,eAAAe,GAAaV,EAAiB,CACxC,IAAAW,EAAe,MAAAX,EAAM,KAAK,YAAW,EACrCY,EAAO,IAAO,WAAWD,CAAM,KAC9BnB,SACJA,EAAM,IAAO,WAAWmB,EAAO,MAAM,EAAGnC,EAAgB,IACxDoC,MAAc,WAAWD,EAAO,MAAMnC,EAAgB,IAEnDO,EACHU,EAAe,KAAKmB,CAAO,OAEvB,IAAAC,IAAarB,CAAM,EAAE,OAAOC,GAAiBmB,CAAO,GACxDxF,EAAcyF,GAAW,QAAQ,OACjCpB,EAAc,oBAaD9F,GAAM,CACpBxB,EAAA,GAAA8E,EAAY,EAAI,EAChBlF,EAAS,iBAAiB,EACrB4H,SAAcU,KACnBlI,EAAA,GAAAqH,EAAS,MAAS,EACdR,GACHO,EAAS,MAAMhB,EAAgB,WAIxBuC,GAAK,CACb/I,EAAS,SAAU,IAAI,EACvBA,EAAS,OAAO,EAChBI,EAAA,GAAAgD,EAAO,EAAE,EACThD,EAAA,EAAAsF,EAAQ,IAAI,EAGJ,SAAAsD,GAAc,OAAAC,GAAM,CAC5B7I,EAAA,EAAAsF,EAAQuD,CAAM,EACdjJ,EAAS,SAAUiJ,CAAM,EACzBjJ,EAAS,SAAUiJ,CAAM,WAGjB7C,GAAI,CACZhG,EAAA,GAAA8E,EAAY,EAAK,EAEb+B,IACHjH,EAAS,gBAAgB,EACzBwH,EAAS,KAAI,EACTR,GACH5G,EAAA,GAAAuH,EAAuC,EAAI,EAE5CtE,EAAcwE,EAAc,gBAAgB,EAC5C7H,EAAS,OAAO,EAChBI,EAAA,GAAAgD,EAAO,EAAE,2JA4CK,MAAA8F,GAAA,SAAAD,CAAM,IAAOjJ,EAAS,QAASiJ,CAAM,EAanCE,GAAA,IAAA/I,EAAA,GAAAgD,EAAO,MAAM,uiCA9N5BpD,EAAS,OAAQmH,CAAQ,4BA0HrBQ,GAAwCX,IAAY,KAC1D5G,EAAA,GAAAuH,EAAuC,EAAK,EACxCF,GAAUC,GAAc,CACvB,IAAAoB,EAA2B,CAAArB,CAAM,EAAE,OAAOC,CAAc,OAC5DA,EAAc,IACdrE,EAAcyF,EAAW,QAAQ,ulFChKf,QAAArG,IAAS,OAAgB,0EA0LnC,QAAAvD,EAAU,WAAQA,QAAkB,SAAW,SAAW,oBACtDA,EAAQ,IAAG,QAAU,eACzB,kBACO,sMAHPM,EAAA,aAAA4J,EAAA,QAAAlK,EAAU,WAAQA,QAAkB,SAAW,SAAW,sCACtDA,EAAQ,IAAG,QAAU,8XAvCzB,oBACIA,EAAQ,IAAG,QAAU,eACzB,kBACO,mOAFHA,EAAQ,IAAG,QAAU,qXA4Ff,KAAAA,MAAO,sFAAPM,EAAA,aAAA6J,EAAA,KAAAnK,MAAO,0IA3Cb,WAAAA,MAAO,YACb,MAAAA,MAAO,IAAI,EACbA,EAAc,yRAoBH,cAAAA,MAAO,sDAchB,KAAAA,MAAO,sFAILA,EAAM,IAAC,OAAO,sBACNA,EAAM,IAAC,OAAO,mbANpBA,EAAY,kJAnCV,WAAAA,MAAO,YACbM,EAAA,kBAAAN,MAAO,IAAI,aACbA,EAAc,yUAoBHM,EAAA,aAAA8J,EAAA,cAAApK,MAAO,8CAchBM,EAAA,aAAA8J,EAAA,KAAApK,MAAO,gHAILA,EAAM,IAAC,OAAO,wCACNA,EAAM,IAAC,OAAO,iTA/ElB,WAAAA,MAAO,YACb,MAAAA,MAAO,IAAI,EACbA,EAAc,8HAKZ,KAAAA,MAAO,8ZAPD,WAAAA,MAAO,YACbM,EAAA,kBAAAN,MAAO,IAAI,aACbA,EAAc,8BAKZM,EAAA,aAAA+J,EAAA,KAAArK,MAAO,+gBArBVA,EAAW,qUA1IL,oBAAAsK,EAAkB,EAAK,EAAA1J,EACvB,SAAA2J,EAAU,EAAE,EAAA3J,GACZ,aAAA4J,EAAY,IAAA5J,EACZ,SAAA6J,EAAU,EAAI,EAAA7J,GACd,YAAA8J,CAAoB,EAAA9J,EACpB,OAAA4F,EAAyB,IAAI,EAAA5F,GAC7B,QAAAiH,CAIgB,EAAAjH,GAChB,MAAA4G,CAAa,EAAA5G,GACb,KAAA6G,CAAY,EAAA7G,GACZ,WAAA+G,CAAmB,EAAA/G,EACnB,WAAA+J,EAAY,EAAI,EAAA/J,EAChB,OAAAgK,EAAuB,IAAI,EAAAhK,EAC3B,WAAAiK,EAAgC,MAAS,EAAAjK,GACzC,eAAAkK,CAA6B,EAAAlK,EAC7B,UAAAmK,EAAW,EAAK,EAAAnK,EAChB,MAAA8G,EAAO,EAAK,EAAA9G,GACZ,qBAAAgH,CAA6B,EAAAhH,EAC7B,mBAAAoK,EAAoB,EAAK,EAAApK,EACzB,UAAA2D,EAAW,EAAI,EAAA3D,GACf,iBAAAyD,EAAgB,IAAAzD,GAChB,QAAAkH,CAAgB,EAAAlH,GAChB,UAAAmH,CAAkB,EAAAnH,GAClB,OAAAqK,CAkBT,EAAArK,EAEEsK,EAA6B,KAE7BhD,EAEAiD,EAAiC3E,QAM/BlC,EAAkB,KACnB6G,IAAkB,MAAQ3E,IAAU2E,GAIxCjK,EAAA,EAAAsF,EAAQ2E,CAAa,OAalBlD,EAMA7D,EAEAgH,EAAe,aAEnB7H,GAAO,KACN6H,EAAe,iBAAiB,UAAU,eAAe,EAAE,iBAC1D,gBAAgB,EAEjBC,IACAnK,EAAA,GAAAkD,EAAkB,UAAYC,EAAiB,gBAAkB,UAASD,CAAA,EAC1ElD,EAAA,GAAAkD,EAAkB,cACjBC,EAAiB,yBAA2B+G,EAAYhH,CAAA,EACzDlD,EAAA,GAAAkD,EAAkB,cAAgBC,EAAiB,cAAaD,CAAA,EAChElD,EAAA,GAAAkD,EAAkB,WAAaC,EAAiB,aAAe,MAAKD,CAAA,UAiB/D4D,EAAoB,CACzB,MAAO3D,EAAiB,kBACxB,KAAM,GACN,OAAQ,aAGAgH,GAAsB,CAC9B,SAAS,gBAAgB,MAAM,YAC9B,sBACArD,EAAqB,OAASoD,CAAY,EAInC,SAAAE,GAAe,OAAAvB,GAAM,CACtB,MAAAwB,EAAOC,CAAM,EAAIzB,EAAO,SAAS,mBAAmB,EACvD,WAAW,UAAU,EACrB,SAAS,OAAO,EACpB7I,EAAA,EAAA4J,EAAiBA,GAAc,QAC/BA,EAAe,OAASU,EAAiCV,CAAA,MACzDA,EAAe,QAAUf,EAAMe,CAAA,EAC/BG,EAAO,SAASM,EAA8BxB,CAAM,EAGrD0B,GAAW,KACVvK,EAAA,GAAAoJ,EAAkB,EAAK,IAqBC,MAAAoB,EAAA,IAAAT,EAAO,SAAS,eAAgBH,CAAc,KAc1DxL,GAAM2L,EAAO,SAAS,QAAS3L,EAAE,MAAM,KACvCA,GAAM2L,EAAO,SAAS,QAAS3L,EAAE,MAAM,QACnC2L,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,QACxBA,EAAO,SAAS,MAAM,EAoBdU,EAAA,IAAAV,EAAO,SAAS,eAAgBH,CAAc,wCAOvD,OAAAf,CAAM,IAAA7I,EAAA,EAAQsF,EAAQuD,CAAM,OAC5B,OAAAA,KAAM,CACnB7I,EAAA,EAAAsF,EAAQuD,CAAM,EACdkB,EAAO,SAAS,SAAUzE,CAAK,QAEpB,OAAAuD,CAAM,IAAA7I,EAAA,GAAQ+G,EAAW8B,CAAM,SAW5BkB,EAAO,SAAS,MAAM,SACtBA,EAAO,SAAS,MAAM,SACrBA,EAAO,SAAS,OAAO,SACxBA,EAAO,SAAS,MAAM,SACXA,EAAO,SAAS,iBAAiB,SACjCA,EAAO,SAAS,iBAAiB,EACvCW,GAAAtM,GAAM2L,EAAO,SAAS,gBAAgB,SACzCA,EAAO,SAAS,QAAQ,SACzBA,EAAO,SAAS,OAAO,05BA5KlCzE,GAAS2E,IAAkB,MACjCjK,EAAA,GAAAiK,EAAgB3E,CAAK,2BAYjB,KAAK,UAAUA,CAAK,IAAM,KAAK,UAAU0E,CAAS,IACrDhK,EAAA,GAAAgK,EAAY1E,CAAK,EACjByE,EAAO,SAAS,QAAQ,EACnBX,GACJW,EAAO,SAAS,OAAO,0BAOzB,CAAO/C,GAAiBL,QACxBK,EAAgBL,EAAQ,CAAC,iCAmBvBzD,EAAiB,CACnB,OAAQ,GAER,SAAU,EACV,OAAQ,EACR,YAAa,EACb,YAAa,UACH,SAAA2G,EACV,UAAW,GACX,WAAY,GACZ,UAAW,GACX,YAAa", "names": ["e", "t", "i", "s", "r", "n", "o", "d", "a", "createEventDispatcher", "ctx", "t_value", "insert", "target", "option", "anchor", "dirty", "set_data", "option_value_value", "create_if_block", "select", "i18n", "$$props", "micDevices", "dispatch", "tempDevices", "RecordPlugin", "devices", "$$invalidate", "device", "err", "time", "t0_value", "t2_value", "t4_value", "t7_value", "if_block", "attr", "button1", "button1_class_value", "div1", "append", "div0", "button0", "button2", "button3", "button4", "current", "t0", "t2", "t4", "t7", "record", "recordButton", "pauseButton", "resumeButton", "stopButton", "stopButtonPaused", "record_time", "show_recording_waveform", "timing", "$$value", "click_handler", "click_handler_3", "click_handler_4", "onMount", "if_block0", "create_if_block_4", "create_if_block_3", "format_time", "waveformrecordcontrols_changes", "create_if_block_2", "if_block1", "create_if_block_1", "if_block2", "div2", "mode", "dispatch_blob", "waveform_settings", "waveform_options", "handle_reset_value", "editable", "micWaveform", "recordingWaveform", "playing", "recordingContainer", "microphoneContainer", "recordedAudio", "timeRef", "durationRef", "audio_duration", "seconds", "interval", "trimDuration", "start_interval", "record_start_callback", "waveformCanvas", "record_end_callback", "blob", "array_buffer", "audio_buffer", "process_audio", "audio", "create_mic_waveform", "WaveSurfer", "microphone", "recording", "create_recording_waveform", "handle_trim_audio", "start", "end", "decodedData", "trimmedAudio", "skip_audio", "value", "duration", "currentTime", "div", "t1_value", "button", "span1", "t1", "span2", "paused_recording", "stop", "waveformRecord", "Music", "blocklabel_changes", "STREAM_TIMESLICE", "NUM_HEADER_BYTES", "label", "root", "loop", "show_label", "show_download_button", "sources", "pending", "streaming", "trim_region_settings", "dragging", "active_source", "max_file_size", "upload", "stream_handler", "recorder", "header", "pending_stream", "submit_pending_stream_on_pending_end", "inited", "audio_chunks", "module_promises", "get_modules", "blobs", "event", "_audio_blob", "val", "prepare_files", "onDestroy", "prepare_audio", "stream", "MediaRecorder", "register", "connect", "handle_chunk", "buffer", "payload", "blobParts", "clear", "handle_load", "detail", "error_handler", "edit_handler_1", "block_changes", "uploadtext_changes", "interactiveaudio_changes", "staticaudio_changes", "value_is_output", "elem_id", "elem_classes", "visible", "interactive", "container", "scale", "min_width", "loading_status", "autoplay", "show_share_button", "gradio", "old_value", "initial_value", "color_accent", "set_trim_region_colour", "handle_error", "level", "status", "afterUpdate", "clear_status_handler", "clear_status_handler_1", "stop_recording_handler"], "ignoreList": [0], "sources": ["../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/plugins/record.js", "../../../../js/audio/shared/DeviceSelect.svelte", "../../../../js/audio/shared/WaveformRecordControls.svelte", "../../../../js/audio/recorder/AudioRecorder.svelte", "../../../../js/audio/streaming/StreamAudio.svelte", "../../../../js/audio/interactive/InteractiveAudio.svelte", "../../../../js/audio/Index.svelte"], "sourcesContent": ["function e(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{d(s.next(e))}catch(e){n(e)}}function a(e){try{d(s.throw(e))}catch(e){n(e)}}function d(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}d((s=s.apply(e,t||[])).next())}))}\"function\"==typeof SuppressedError&&SuppressedError;class t{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),null==i?void 0:i.once){const i=()=>{this.removeEventListener(e,i),this.removeEventListener(e,t)};return this.addEventListener(e,i),i}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;null===(i=this.listeners[e])||void 0===i||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach((e=>e(...t)))}}class i extends t{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach((e=>e()))}}const s=[\"audio/webm\",\"audio/wav\",\"audio/mpeg\",\"audio/mp4\",\"audio/mp3\"];class r extends i{constructor(e){var t;super(Object.assign(Object.assign({},e),{audioBitsPerSecond:null!==(t=e.audioBitsPerSecond)&&void 0!==t?t:128e3})),this.stream=null,this.mediaRecorder=null}static create(e){return new r(e||{})}renderMicStream(e){const t=new AudioContext,i=t.createMediaStreamSource(e),s=t.createAnalyser();i.connect(s);const r=s.frequencyBinCount,n=new Float32Array(r),o=r/t.sampleRate;let a;const d=()=>{s.getFloatTimeDomainData(n),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load(\"\",[n],o)),a=requestAnimationFrame(d)};return d(),()=>{cancelAnimationFrame(a),null==i||i.disconnect(),null==t||t.close()}}startMic(t){return e(this,void 0,void 0,(function*(){let e;try{e=yield navigator.mediaDevices.getUserMedia({audio:!(null==t?void 0:t.deviceId)||{deviceId:t.deviceId}})}catch(e){throw new Error(\"Error accessing the microphone: \"+e.message)}const i=this.renderMicStream(e);return this.subscriptions.push(this.once(\"destroy\",i)),this.stream=e,e}))}stopMic(){this.stream&&(this.stream.getTracks().forEach((e=>e.stop())),this.stream=null,this.mediaRecorder=null)}startRecording(t){return e(this,void 0,void 0,(function*(){const e=this.stream||(yield this.startMic(t)),i=this.mediaRecorder||new MediaRecorder(e,{mimeType:this.options.mimeType||s.find((e=>MediaRecorder.isTypeSupported(e))),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=i,this.stopRecording();const r=[];i.ondataavailable=e=>{e.data.size>0&&r.push(e.data)},i.onstop=()=>{var e;const t=new Blob(r,{type:i.mimeType});this.emit(\"record-end\",t),!1!==this.options.renderRecordedAudio&&(null===(e=this.wavesurfer)||void 0===e||e.load(URL.createObjectURL(t)))},i.start(),this.emit(\"record-start\")}))}isRecording(){var e;return\"recording\"===(null===(e=this.mediaRecorder)||void 0===e?void 0:e.state)}isPaused(){var e;return\"paused\"===(null===(e=this.mediaRecorder)||void 0===e?void 0:e.state)}stopRecording(){var e;this.isRecording()&&(null===(e=this.mediaRecorder)||void 0===e||e.stop())}pauseRecording(){var e;this.isRecording()&&(null===(e=this.mediaRecorder)||void 0===e||e.pause(),this.emit(\"record-pause\"))}resumeRecording(){var e;this.isPaused()&&(null===(e=this.mediaRecorder)||void 0===e||e.resume(),this.emit(\"record-resume\"))}static getAvailableAudioDevices(){return e(this,void 0,void 0,(function*(){return navigator.mediaDevices.enumerateDevices().then((e=>e.filter((e=>\"audioinput\"===e.kind))))}))}destroy(){super.destroy(),this.stopRecording(),this.stopMic()}}export{r as default};\n", "<script lang=\"ts\">\n\timport RecordPlugin from \"wavesurfer.js/dist/plugins/record.js\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let i18n: I18nFormatter;\n\texport let micDevices: MediaDeviceInfo[] = [];\n\n\tconst dispatch = createEventDispatcher<{\n\t\terror: string;\n\t}>();\n\n\t$: try {\n\t\tlet tempDevices: MediaDeviceInfo[] = [];\n\t\tRecordPlugin.getAvailableAudioDevices().then(\n\t\t\t(devices: MediaDeviceInfo[]) => {\n\t\t\t\tmicDevices = devices;\n\t\t\t\tdevices.forEach((device) => {\n\t\t\t\t\tif (device.deviceId) {\n\t\t\t\t\t\ttempDevices.push(device);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tmicDevices = tempDevices;\n\t\t\t}\n\t\t);\n\t} catch (err) {\n\t\tif (err instanceof DOMException && err.name == \"NotAllowedError\") {\n\t\t\tdispatch(\"error\", i18n(\"audio.allow_recording_access\"));\n\t\t}\n\t\tthrow err;\n\t}\n</script>\n\n<select\n\tclass=\"mic-select\"\n\taria-label=\"Select input device\"\n\tdisabled={micDevices.length === 0}\n>\n\t{#if micDevices.length === 0}\n\t\t<option value=\"\">{i18n(\"audio.no_microphone\")}</option>\n\t{:else}\n\t\t{#each micDevices as micDevice}\n\t\t\t<option value={micDevice.deviceId}>{micDevice.label}</option>\n\t\t{/each}\n\t{/if}\n</select>\n\n<style>\n\t.mic-select {\n\t\theight: var(--size-8);\n\t\tbackground: var(--block-background-fill);\n\t\tpadding: 0px var(--spacing-xxl);\n\t\tborder-radius: var(--radius-full);\n\t\tfont-size: var(--text-md);\n\t\tborder: 1px solid var(--neutral-400);\n\t\tgap: var(--size-1);\n\t}\n\n\tselect {\n\t\ttext-overflow: ellipsis;\n\t\tmax-width: var(--size-40);\n\t}\n\n\t@media (max-width: 375px) {\n\t\tselect {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Pause } from \"@gradio/icons\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport RecordPlugin from \"wavesurfer.js/dist/plugins/record.js\";\n\timport DeviceSelect from \"./DeviceSelect.svelte\";\n\n\texport let record: RecordPlugin;\n\texport let i18n: I18nFormatter;\n\n\tlet micDevices: MediaDeviceInfo[] = [];\n\tlet recordButton: HTMLButtonElement;\n\tlet pauseButton: HTMLButtonElement;\n\tlet resumeButton: HTMLButtonElement;\n\tlet stopButton: HTMLButtonElement;\n\tlet stopButtonPaused: HTMLButtonElement;\n\n\texport let record_time: string;\n\texport let show_recording_waveform: boolean | undefined;\n\texport let timing = false;\n\n\t$: record.on(\"record-start\", () => {\n\t\trecord.startMic();\n\n\t\trecordButton.style.display = \"none\";\n\t\tstopButton.style.display = \"flex\";\n\t\tpauseButton.style.display = \"block\";\n\t});\n\n\t$: record.on(\"record-end\", () => {\n\t\tif (record.isPaused()) {\n\t\t\trecord.resumeRecording();\n\t\t\trecord.stopRecording();\n\t\t}\n\t\trecord.stopMic();\n\n\t\trecordButton.style.display = \"flex\";\n\t\tstopButton.style.display = \"none\";\n\t\tpauseButton.style.display = \"none\";\n\t\trecordButton.disabled = false;\n\t});\n\n\t$: record.on(\"record-pause\", () => {\n\t\tpauseButton.style.display = \"none\";\n\t\tresumeButton.style.display = \"block\";\n\t\tstopButton.style.display = \"none\";\n\t\tstopButtonPaused.style.display = \"flex\";\n\t});\n\n\t$: record.on(\"record-resume\", () => {\n\t\tpauseButton.style.display = \"block\";\n\t\tresumeButton.style.display = \"none\";\n\t\trecordButton.style.display = \"none\";\n\t\tstopButton.style.display = \"flex\";\n\t\tstopButtonPaused.style.display = \"none\";\n\t});\n</script>\n\n<div class=\"controls\">\n\t<div class=\"wrapper\">\n\t\t<button\n\t\t\tbind:this={recordButton}\n\t\t\tclass=\"record record-button\"\n\t\t\ton:click={() => record.startRecording()}>{i18n(\"audio.record\")}</button\n\t\t>\n\n\t\t<button\n\t\t\tbind:this={stopButton}\n\t\t\tclass=\"stop-button {record.isPaused() ? 'stop-button-paused' : ''}\"\n\t\t\ton:click={() => {\n\t\t\t\tif (record.isPaused()) {\n\t\t\t\t\trecord.resumeRecording();\n\t\t\t\t\trecord.stopRecording();\n\t\t\t\t}\n\n\t\t\t\trecord.stopRecording();\n\t\t\t}}>{i18n(\"audio.stop\")}</button\n\t\t>\n\n\t\t<button\n\t\t\tbind:this={stopButtonPaused}\n\t\t\tid=\"stop-paused\"\n\t\t\tclass=\"stop-button-paused\"\n\t\t\ton:click={() => {\n\t\t\t\tif (record.isPaused()) {\n\t\t\t\t\trecord.resumeRecording();\n\t\t\t\t\trecord.stopRecording();\n\t\t\t\t}\n\n\t\t\t\trecord.stopRecording();\n\t\t\t}}>{i18n(\"audio.stop\")}</button\n\t\t>\n\n\t\t<button\n\t\t\taria-label=\"pause\"\n\t\t\tbind:this={pauseButton}\n\t\t\tclass=\"pause-button\"\n\t\t\ton:click={() => record.pauseRecording()}><Pause /></button\n\t\t>\n\t\t<button\n\t\t\tbind:this={resumeButton}\n\t\t\tclass=\"resume-button\"\n\t\t\ton:click={() => record.resumeRecording()}>{i18n(\"audio.resume\")}</button\n\t\t>\n\t\t{#if timing && !show_recording_waveform}\n\t\t\t<time class=\"duration-button duration\">{record_time}</time>\n\t\t{/if}\n\t</div>\n\t<DeviceSelect bind:micDevices {i18n} />\n</div>\n\n<style>\n\t.controls {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.record {\n\t\tmargin-right: var(--spacing-md);\n\t}\n\n\t.stop-button-paused {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--neutral-400);\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t.stop-button-paused::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t}\n\t.stop-button::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t\tanimation: scaling 1800ms infinite;\n\t}\n\n\t.stop-button {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--primary-600);\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t.record-button::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t}\n\n\t.record-button {\n\t\theight: var(--size-8);\n\t\twidth: var(--size-24);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--neutral-400);\n\t}\n\n\t.stop-button:disabled {\n\t\tcursor: not-allowed;\n\t}\n\n\t.record-button:disabled {\n\t\tcursor: not-allowed;\n\t\topacity: 0.5;\n\t}\n\n\t@keyframes scaling {\n\t\t0% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1;\n\t\t}\n\t\t50% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1.2;\n\t\t}\n\t\t100% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1;\n\t\t}\n\t}\n\n\t.pause-button {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tborder: 1px solid var(--neutral-400);\n\t\tborder-radius: var(--radius-3xl);\n\t\tpadding: var(--spacing-md);\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t.resume-button {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tborder: 1px solid var(--neutral-400);\n\t\tborder-radius: var(--radius-3xl);\n\t\tpadding: var(--spacing-xl);\n\t\tline-height: 1px;\n\t\tfont-size: var(--text-md);\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t.duration {\n\t\tdisplay: flex;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tborder: 1px solid var(--neutral-400);\n\t\tborder-radius: var(--radius-3xl);\n\t\tpadding: var(--spacing-md);\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t:global(::part(region)) {\n\t\tborder-radius: var(--radius-md);\n\t\theight: 98% !important;\n\t\tborder: 1px solid var(--trim-region-color);\n\t\tbackground-color: unset;\n\t\tborder-width: 1px 3px;\n\t}\n\n\t:global(::part(region))::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: var(--trim-region-color);\n\t\topacity: 0.2;\n\t\tborder-radius: var(--radius-md);\n\t}\n\n\t:global(::part(region-handle)) {\n\t\twidth: 5px !important;\n\t\tborder: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport WaveSurfer from \"wavesurfer.js\";\n\timport { skip_audio, process_audio } from \"../shared/utils\";\n\timport WSRecord from \"wavesurfer.js/dist/plugins/record.js\";\n\timport WaveformControls from \"../shared/WaveformControls.svelte\";\n\timport WaveformRecordControls from \"../shared/WaveformRecordControls.svelte\";\n\timport RecordPlugin from \"wavesurfer.js/dist/plugins/record.js\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\timport { format_time } from \"@gradio/utils\";\n\n\texport let mode: string;\n\texport let i18n: I18nFormatter;\n\texport let dispatch_blob: (\n\t\tblobs: Uint8Array[] | Blob[],\n\t\tevent: \"stream\" | \"change\" | \"stop_recording\"\n\t) => Promise<void> | undefined;\n\texport let waveform_settings: Record<string, any>;\n\texport let waveform_options: WaveformOptions = {\n\t\tshow_recording_waveform: true\n\t};\n\texport let handle_reset_value: () => void;\n\texport let editable = true;\n\n\tlet micWaveform: WaveSurfer;\n\tlet recordingWaveform: WaveSurfer;\n\tlet playing = false;\n\n\tlet recordingContainer: HTMLDivElement;\n\tlet microphoneContainer: HTMLDivElement;\n\n\tlet record: WSRecord;\n\tlet recordedAudio: string | null = null;\n\n\t// timestamps\n\tlet timeRef: HTMLTimeElement;\n\tlet durationRef: HTMLTimeElement;\n\tlet audio_duration: number;\n\tlet seconds = 0;\n\tlet interval: NodeJS.Timeout;\n\tlet timing = false;\n\t// trimming\n\tlet trimDuration = 0;\n\n\tconst start_interval = (): void => {\n\t\tclearInterval(interval);\n\t\tinterval = setInterval(() => {\n\t\t\tseconds++;\n\t\t}, 1000);\n\t};\n\n\tconst dispatch = createEventDispatcher<{\n\t\tstart_recording: undefined;\n\t\tpause_recording: undefined;\n\t\tstop_recording: undefined;\n\t\tstop: undefined;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tend: undefined;\n\t\tedit: undefined;\n\t}>();\n\n\tfunction record_start_callback(): void {\n\t\tstart_interval();\n\t\ttiming = true;\n\t\tdispatch(\"start_recording\");\n\t\tif (waveform_options.show_recording_waveform) {\n\t\t\tlet waveformCanvas = microphoneContainer;\n\t\t\tif (waveformCanvas) waveformCanvas.style.display = \"block\";\n\t\t}\n\t}\n\n\tasync function record_end_callback(blob: Blob): Promise<void> {\n\t\tseconds = 0;\n\t\ttiming = false;\n\t\tclearInterval(interval);\n\t\ttry {\n\t\t\tconst array_buffer = await blob.arrayBuffer();\n\t\t\tconst context = new AudioContext({\n\t\t\t\tsampleRate: waveform_settings.sampleRate\n\t\t\t});\n\t\t\tconst audio_buffer = await context.decodeAudioData(array_buffer);\n\n\t\t\tif (audio_buffer)\n\t\t\t\tawait process_audio(audio_buffer).then(async (audio: Uint8Array) => {\n\t\t\t\t\tawait dispatch_blob([audio], \"change\");\n\t\t\t\t\tawait dispatch_blob([audio], \"stop_recording\");\n\t\t\t\t});\n\t\t} catch (e) {\n\t\t\tconsole.error(e);\n\t\t}\n\t}\n\n\t$: record?.on(\"record-resume\", () => {\n\t\tstart_interval();\n\t});\n\n\t$: recordingWaveform?.on(\"decode\", (duration: any) => {\n\t\taudio_duration = duration;\n\t\tdurationRef && (durationRef.textContent = format_time(duration));\n\t});\n\n\t$: recordingWaveform?.on(\n\t\t\"timeupdate\",\n\t\t(currentTime: any) =>\n\t\t\ttimeRef && (timeRef.textContent = format_time(currentTime))\n\t);\n\n\t$: recordingWaveform?.on(\"pause\", () => {\n\t\tdispatch(\"pause\");\n\t\tplaying = false;\n\t});\n\n\t$: recordingWaveform?.on(\"play\", () => {\n\t\tdispatch(\"play\");\n\t\tplaying = true;\n\t});\n\n\t$: recordingWaveform?.on(\"finish\", () => {\n\t\tdispatch(\"stop\");\n\t\tplaying = false;\n\t});\n\n\tconst create_mic_waveform = (): void => {\n\t\tif (microphoneContainer) microphoneContainer.innerHTML = \"\";\n\t\tif (micWaveform !== undefined) micWaveform.destroy();\n\t\tif (!microphoneContainer) return;\n\t\tmicWaveform = WaveSurfer.create({\n\t\t\t...waveform_settings,\n\t\t\tnormalize: false,\n\t\t\tcontainer: microphoneContainer\n\t\t});\n\n\t\trecord = micWaveform.registerPlugin(RecordPlugin.create());\n\t\trecord.startMic();\n\t\trecord?.on(\"record-end\", record_end_callback);\n\t\trecord?.on(\"record-start\", record_start_callback);\n\t\trecord?.on(\"record-pause\", () => {\n\t\t\tdispatch(\"pause_recording\");\n\t\t\tclearInterval(interval);\n\t\t});\n\n\t\trecord?.on(\"record-end\", (blob) => {\n\t\t\trecordedAudio = URL.createObjectURL(blob);\n\n\t\t\tconst microphone = microphoneContainer;\n\t\t\tconst recording = recordingContainer;\n\n\t\t\tif (microphone) microphone.style.display = \"none\";\n\t\t\tif (recording && recordedAudio) {\n\t\t\t\trecording.innerHTML = \"\";\n\t\t\t\tcreate_recording_waveform();\n\t\t\t}\n\t\t});\n\t};\n\n\tconst create_recording_waveform = (): void => {\n\t\tlet recording = recordingContainer;\n\t\tif (!recordedAudio || !recording) return;\n\t\trecordingWaveform = WaveSurfer.create({\n\t\t\tcontainer: recording,\n\t\t\turl: recordedAudio,\n\t\t\t...waveform_settings\n\t\t});\n\t};\n\n\tconst handle_trim_audio = async (\n\t\tstart: number,\n\t\tend: number\n\t): Promise<void> => {\n\t\tmode = \"edit\";\n\t\tconst decodedData = recordingWaveform.getDecodedData();\n\t\tif (decodedData)\n\t\t\tawait process_audio(decodedData, start, end).then(\n\t\t\t\tasync (trimmedAudio: Uint8Array) => {\n\t\t\t\t\tawait dispatch_blob([trimmedAudio], \"change\");\n\t\t\t\t\tawait dispatch_blob([trimmedAudio], \"stop_recording\");\n\t\t\t\t\trecordingWaveform.destroy();\n\t\t\t\t\tcreate_recording_waveform();\n\t\t\t\t}\n\t\t\t);\n\t\tdispatch(\"edit\");\n\t};\n\n\tonMount(() => {\n\t\tcreate_mic_waveform();\n\n\t\twindow.addEventListener(\"keydown\", (e) => {\n\t\t\tif (e.key === \"ArrowRight\") {\n\t\t\t\tskip_audio(recordingWaveform, 0.1);\n\t\t\t} else if (e.key === \"ArrowLeft\") {\n\t\t\t\tskip_audio(recordingWaveform, -0.1);\n\t\t\t}\n\t\t});\n\t});\n</script>\n\n<div class=\"component-wrapper\">\n\t<div\n\t\tclass=\"microphone\"\n\t\tbind:this={microphoneContainer}\n\t\tdata-testid=\"microphone-waveform\"\n\t/>\n\t<div bind:this={recordingContainer} data-testid=\"recording-waveform\" />\n\n\t{#if (timing || recordedAudio) && waveform_options.show_recording_waveform}\n\t\t<div class=\"timestamps\">\n\t\t\t<time bind:this={timeRef} class=\"time\">0:00</time>\n\t\t\t<div>\n\t\t\t\t{#if mode === \"edit\" && trimDuration > 0}\n\t\t\t\t\t<time class=\"trim-duration\">{format_time(trimDuration)}</time>\n\t\t\t\t{/if}\n\t\t\t\t{#if timing}\n\t\t\t\t\t<time class=\"duration\">{format_time(seconds)}</time>\n\t\t\t\t{:else}\n\t\t\t\t\t<time bind:this={durationRef} class=\"duration\">0:00</time>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t{/if}\n\n\t{#if microphoneContainer && !recordedAudio}\n\t\t<WaveformRecordControls\n\t\t\tbind:record\n\t\t\t{i18n}\n\t\t\t{timing}\n\t\t\tshow_recording_waveform={waveform_options.show_recording_waveform}\n\t\t\trecord_time={format_time(seconds)}\n\t\t/>\n\t{/if}\n\n\t{#if recordingWaveform && recordedAudio}\n\t\t<WaveformControls\n\t\t\tbind:waveform={recordingWaveform}\n\t\t\tcontainer={recordingContainer}\n\t\t\t{playing}\n\t\t\t{audio_duration}\n\t\t\t{i18n}\n\t\t\t{editable}\n\t\t\tinteractive={true}\n\t\t\t{handle_trim_audio}\n\t\t\tbind:trimDuration\n\t\t\tbind:mode\n\t\t\tshow_redo\n\t\t\t{handle_reset_value}\n\t\t\t{waveform_options}\n\t\t/>\n\t{/if}\n</div>\n\n<style>\n\t.microphone {\n\t\twidth: 100%;\n\t\tdisplay: none;\n\t}\n\n\t.component-wrapper {\n\t\tpadding: var(--size-3);\n\t\twidth: 100%;\n\t}\n\n\t.timestamps {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\twidth: 100%;\n\t\tpadding: var(--size-1) 0;\n\t\tmargin: var(--spacing-md) 0;\n\t}\n\n\t.time {\n\t\tcolor: var(--neutral-400);\n\t}\n\n\t.duration {\n\t\tcolor: var(--neutral-400);\n\t}\n\n\t.trim-duration {\n\t\tcolor: var(--color-accent);\n\t\tmargin-right: var(--spacing-sm);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport WaveSurfer from \"wavesurfer.js\";\n\timport RecordPlugin from \"wavesurfer.js/dist/plugins/record.js\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\timport DeviceSelect from \"../shared/DeviceSelect.svelte\";\n\n\texport let recording = false;\n\texport let paused_recording = false;\n\texport let stop: () => void;\n\texport let record: () => void;\n\texport let i18n: I18nFormatter;\n\texport let waveform_settings: Record<string, any>;\n\texport let waveform_options: WaveformOptions = {\n\t\tshow_recording_waveform: true\n\t};\n\n\tlet micWaveform: WaveSurfer;\n\tlet waveformRecord: RecordPlugin;\n\n\tlet microphoneContainer: HTMLDivElement;\n\n\tlet micDevices: MediaDeviceInfo[] = [];\n\n\tonMount(() => {\n\t\tcreate_mic_waveform();\n\t});\n\n\tconst create_mic_waveform = (): void => {\n\t\tif (micWaveform !== undefined) micWaveform.destroy();\n\t\tif (!microphoneContainer) return;\n\t\tmicWaveform = WaveSurfer.create({\n\t\t\t...waveform_settings,\n\t\t\theight: 100,\n\t\t\tcontainer: microphoneContainer\n\t\t});\n\n\t\twaveformRecord = micWaveform.registerPlugin(RecordPlugin.create());\n\t};\n</script>\n\n<div class=\"mic-wrap\">\n\t{#if waveform_options.show_recording_waveform}\n\t\t<div\n\t\t\tbind:this={microphoneContainer}\n\t\t\tstyle:display={recording ? \"block\" : \"none\"}\n\t\t/>\n\t{/if}\n\t<div class=\"controls\">\n\t\t{#if recording}\n\t\t\t<button\n\t\t\t\tclass={paused_recording ? \"stop-button-paused\" : \"stop-button\"}\n\t\t\t\ton:click={() => {\n\t\t\t\t\twaveformRecord?.stopMic();\n\t\t\t\t\tstop();\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<span class=\"record-icon\">\n\t\t\t\t\t<span class=\"pinger\" />\n\t\t\t\t\t<span class=\"dot\" />\n\t\t\t\t</span>\n\t\t\t\t{paused_recording ? i18n(\"audio.pause\") : i18n(\"audio.stop\")}\n\t\t\t</button>\n\t\t{:else}\n\t\t\t<button\n\t\t\t\tclass=\"record-button\"\n\t\t\t\ton:click={() => {\n\t\t\t\t\twaveformRecord?.startMic();\n\t\t\t\t\trecord();\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<span class=\"record-icon\">\n\t\t\t\t\t<span class=\"dot\" />\n\t\t\t\t</span>\n\t\t\t\t{i18n(\"audio.record\")}\n\t\t\t</button>\n\t\t{/if}\n\n\t\t<DeviceSelect bind:micDevices {i18n} />\n\t</div>\n</div>\n\n<style>\n\t.controls {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.mic-wrap {\n\t\tdisplay: block;\n\t\talign-items: center;\n\t\tmargin: var(--spacing-xl);\n\t}\n\n\t.stop-button-paused {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--neutral-400);\n\t\tmargin-right: 5px;\n\t}\n\n\t.stop-button-paused::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t}\n\n\t.stop-button::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t\tanimation: scaling 1800ms infinite;\n\t}\n\n\t.stop-button {\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--primary-600);\n\t\tmargin-right: 5px;\n\t\tdisplay: flex;\n\t}\n\n\t.record-button::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t}\n\n\t.record-button {\n\t\theight: var(--size-8);\n\t\twidth: var(--size-24);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--neutral-400);\n\t}\n\n\t@keyframes scaling {\n\t\t0% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1;\n\t\t}\n\t\t50% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1.2;\n\t\t}\n\t\t100% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1;\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onD<PERSON><PERSON>, createEventDispatcher } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport { prepare_files, type FileData, type Client } from \"@gradio/client\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { Music } from \"@gradio/icons\";\n\timport AudioPlayer from \"../player/AudioPlayer.svelte\";\n\n\timport type { IBlobEvent, IMediaRecorder } from \"extendable-media-recorder\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport AudioRecorder from \"../recorder/AudioRecorder.svelte\";\n\timport StreamAudio from \"../streaming/StreamAudio.svelte\";\n\timport { SelectSource } from \"@gradio/atoms\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\n\texport let value: null | FileData = null;\n\texport let label: string;\n\texport let root: string;\n\texport let loop: boolean;\n\texport let show_label = true;\n\texport let show_download_button = false;\n\texport let sources:\n\t\t| [\"microphone\"]\n\t\t| [\"upload\"]\n\t\t| [\"microphone\", \"upload\"]\n\t\t| [\"upload\", \"microphone\"] = [\"microphone\", \"upload\"];\n\texport let pending = false;\n\texport let streaming = false;\n\texport let i18n: I18nFormatter;\n\texport let waveform_settings: Record<string, any>;\n\texport let trim_region_settings = {};\n\texport let waveform_options: WaveformOptions = {};\n\texport let dragging: boolean;\n\texport let active_source: \"microphone\" | \"upload\";\n\texport let handle_reset_value: () => void = () => {};\n\texport let editable = true;\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\n\t$: dispatch(\"drag\", dragging);\n\n\t// TODO: make use of this\n\t// export let type: \"normal\" | \"numpy\" = \"normal\";\n\tlet recording = false;\n\tlet recorder: IMediaRecorder;\n\tlet mode = \"\";\n\tlet header: Uint8Array | undefined = undefined;\n\tlet pending_stream: Uint8Array[] = [];\n\tlet submit_pending_stream_on_pending_end = false;\n\tlet inited = false;\n\n\tconst STREAM_TIMESLICE = 500;\n\tconst NUM_HEADER_BYTES = 44;\n\tlet audio_chunks: Blob[] = [];\n\tlet module_promises: [\n\t\tPromise<typeof import(\"extendable-media-recorder\")>,\n\t\tPromise<typeof import(\"extendable-media-recorder-wav-encoder\")>\n\t];\n\n\tfunction get_modules(): void {\n\t\tmodule_promises = [\n\t\t\timport(\"extendable-media-recorder\"),\n\t\t\timport(\"extendable-media-recorder-wav-encoder\")\n\t\t];\n\t}\n\n\tif (streaming) {\n\t\tget_modules();\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData | null;\n\t\tstream: FileData;\n\t\tedit: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tdrag: boolean;\n\t\terror: string;\n\t\tupload: FileData;\n\t\tclear: undefined;\n\t\tstart_recording: undefined;\n\t\tpause_recording: undefined;\n\t\tstop_recording: undefined;\n\t}>();\n\n\tconst dispatch_blob = async (\n\t\tblobs: Uint8Array[] | Blob[],\n\t\tevent: \"stream\" | \"change\" | \"stop_recording\"\n\t): Promise<void> => {\n\t\tlet _audio_blob = new File(blobs, \"audio.wav\");\n\t\tconst val = await prepare_files([_audio_blob], event === \"stream\");\n\t\tvalue = (\n\t\t\t(await upload(val, root, undefined, max_file_size || undefined))?.filter(\n\t\t\t\tBoolean\n\t\t\t) as FileData[]\n\t\t)[0];\n\t\tdispatch(event, value);\n\t};\n\n\tonDestroy(() => {\n\t\tif (streaming && recorder && recorder.state !== \"inactive\") {\n\t\t\trecorder.stop();\n\t\t}\n\t});\n\n\tasync function prepare_audio(): Promise<void> {\n\t\tlet stream: MediaStream | null;\n\n\t\ttry {\n\t\t\tstream = await navigator.mediaDevices.getUserMedia({ audio: true });\n\t\t} catch (err) {\n\t\t\tif (!navigator.mediaDevices) {\n\t\t\t\tdispatch(\"error\", i18n(\"audio.no_device_support\"));\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (err instanceof DOMException && err.name == \"NotAllowedError\") {\n\t\t\t\tdispatch(\"error\", i18n(\"audio.allow_recording_access\"));\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthrow err;\n\t\t}\n\t\tif (stream == null) return;\n\t\tif (streaming) {\n\t\t\tconst [{ MediaRecorder, register }, { connect }] =\n\t\t\t\tawait Promise.all(module_promises);\n\t\t\tawait register(await connect());\n\t\t\trecorder = new MediaRecorder(stream, { mimeType: \"audio/wav\" });\n\t\t\trecorder.addEventListener(\"dataavailable\", handle_chunk);\n\t\t} else {\n\t\t\trecorder = new MediaRecorder(stream);\n\t\t\trecorder.addEventListener(\"dataavailable\", (event) => {\n\t\t\t\taudio_chunks.push(event.data);\n\t\t\t});\n\t\t\trecorder.addEventListener(\"stop\", async () => {\n\t\t\t\trecording = false;\n\t\t\t\tawait dispatch_blob(audio_chunks, \"change\");\n\t\t\t\tawait dispatch_blob(audio_chunks, \"stop_recording\");\n\t\t\t\taudio_chunks = [];\n\t\t\t});\n\t\t}\n\t\tinited = true;\n\t}\n\n\tasync function handle_chunk(event: IBlobEvent): Promise<void> {\n\t\tlet buffer = await event.data.arrayBuffer();\n\t\tlet payload = new Uint8Array(buffer);\n\t\tif (!header) {\n\t\t\theader = new Uint8Array(buffer.slice(0, NUM_HEADER_BYTES));\n\t\t\tpayload = new Uint8Array(buffer.slice(NUM_HEADER_BYTES));\n\t\t}\n\t\tif (pending) {\n\t\t\tpending_stream.push(payload);\n\t\t} else {\n\t\t\tlet blobParts = [header].concat(pending_stream, [payload]);\n\t\t\tdispatch_blob(blobParts, \"stream\");\n\t\t\tpending_stream = [];\n\t\t}\n\t}\n\n\t$: if (submit_pending_stream_on_pending_end && pending === false) {\n\t\tsubmit_pending_stream_on_pending_end = false;\n\t\tif (header && pending_stream) {\n\t\t\tlet blobParts: Uint8Array[] = [header].concat(pending_stream);\n\t\t\tpending_stream = [];\n\t\t\tdispatch_blob(blobParts, \"stream\");\n\t\t}\n\t}\n\n\tasync function record(): Promise<void> {\n\t\trecording = true;\n\t\tdispatch(\"start_recording\");\n\t\tif (!inited) await prepare_audio();\n\t\theader = undefined;\n\t\tif (streaming) {\n\t\t\trecorder.start(STREAM_TIMESLICE);\n\t\t}\n\t}\n\n\tfunction clear(): void {\n\t\tdispatch(\"change\", null);\n\t\tdispatch(\"clear\");\n\t\tmode = \"\";\n\t\tvalue = null;\n\t}\n\n\tfunction handle_load({ detail }: { detail: FileData }): void {\n\t\tvalue = detail;\n\t\tdispatch(\"change\", detail);\n\t\tdispatch(\"upload\", detail);\n\t}\n\n\tfunction stop(): void {\n\t\trecording = false;\n\n\t\tif (streaming) {\n\t\t\tdispatch(\"stop_recording\");\n\t\t\trecorder.stop();\n\t\t\tif (pending) {\n\t\t\t\tsubmit_pending_stream_on_pending_end = true;\n\t\t\t}\n\t\t\tdispatch_blob(audio_chunks, \"stop_recording\");\n\t\t\tdispatch(\"clear\");\n\t\t\tmode = \"\";\n\t\t}\n\t}\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={Music}\n\tfloat={active_source === \"upload\" && value === null}\n\tlabel={label || i18n(\"audio.audio\")}\n/>\n<div class=\"audio-container\">\n\t{#if value === null || streaming}\n\t\t{#if active_source === \"microphone\"}\n\t\t\t<ModifyUpload {i18n} on:clear={clear} absolute={true} />\n\t\t\t{#if streaming}\n\t\t\t\t<StreamAudio\n\t\t\t\t\t{record}\n\t\t\t\t\t{recording}\n\t\t\t\t\t{stop}\n\t\t\t\t\t{i18n}\n\t\t\t\t\t{waveform_settings}\n\t\t\t\t\t{waveform_options}\n\t\t\t\t/>\n\t\t\t{:else}\n\t\t\t\t<AudioRecorder\n\t\t\t\t\tbind:mode\n\t\t\t\t\t{i18n}\n\t\t\t\t\t{editable}\n\t\t\t\t\t{dispatch_blob}\n\t\t\t\t\t{waveform_settings}\n\t\t\t\t\t{waveform_options}\n\t\t\t\t\t{handle_reset_value}\n\t\t\t\t\ton:start_recording\n\t\t\t\t\ton:pause_recording\n\t\t\t\t\ton:stop_recording\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t{:else if active_source === \"upload\"}\n\t\t\t<!-- explicitly listed out audio mimetypes due to iOS bug not recognizing audio/* -->\n\t\t\t<Upload\n\t\t\t\tfiletype=\"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*\"\n\t\t\t\ton:load={handle_load}\n\t\t\t\tbind:dragging\n\t\t\t\ton:error={({ detail }) => dispatch(\"error\", detail)}\n\t\t\t\t{root}\n\t\t\t\t{max_file_size}\n\t\t\t\t{upload}\n\t\t\t\t{stream_handler}\n\t\t\t>\n\t\t\t\t<slot />\n\t\t\t</Upload>\n\t\t{/if}\n\t{:else}\n\t\t<ModifyUpload\n\t\t\t{i18n}\n\t\t\ton:clear={clear}\n\t\t\ton:edit={() => (mode = \"edit\")}\n\t\t\tdownload={show_download_button ? value.url : null}\n\t\t\tabsolute={true}\n\t\t/>\n\n\t\t<AudioPlayer\n\t\t\tbind:mode\n\t\t\t{value}\n\t\t\t{label}\n\t\t\t{i18n}\n\t\t\t{dispatch_blob}\n\t\t\t{waveform_settings}\n\t\t\t{waveform_options}\n\t\t\t{trim_region_settings}\n\t\t\t{handle_reset_value}\n\t\t\t{editable}\n\t\t\t{loop}\n\t\t\tinteractive\n\t\t\ton:stop\n\t\t\ton:play\n\t\t\ton:pause\n\t\t\ton:edit\n\t\t/>\n\t{/if}\n\t<SelectSource {sources} bind:active_source handle_clear={clear} />\n</div>\n\n<style>\n\t.audio-container {\n\t\theight: calc(var(--size-full) - var(--size-6));\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, ShareData } from \"@gradio/utils\";\n\n\timport type { FileData } from \"@gradio/client\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { afterUpdate, onMount } from \"svelte\";\n\n\timport StaticAudio from \"./static/StaticAudio.svelte\";\n\timport InteractiveAudio from \"./interactive/InteractiveAudio.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\timport type { WaveformOptions } from \"./shared/types\";\n\n\texport let value_is_output = false;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let interactive: boolean;\n\texport let value: null | FileData = null;\n\texport let sources:\n\t\t| [\"microphone\"]\n\t\t| [\"upload\"]\n\t\t| [\"microphone\", \"upload\"]\n\t\t| [\"upload\", \"microphone\"];\n\texport let label: string;\n\texport let root: string;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let autoplay = false;\n\texport let loop = false;\n\texport let show_download_button: boolean;\n\texport let show_share_button = false;\n\texport let editable = true;\n\texport let waveform_options: WaveformOptions = {};\n\texport let pending: boolean;\n\texport let streaming: boolean;\n\texport let gradio: Gradio<{\n\t\tinput: never;\n\t\tchange: typeof value;\n\t\tstream: typeof value;\n\t\terror: string;\n\t\twarning: string;\n\t\tedit: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tstart_recording: never;\n\t\tpause_recording: never;\n\t\tstop_recording: never;\n\t\tupload: never;\n\t\tclear: never;\n\t\tshare: ShareData;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\n\tlet old_value: null | FileData = null;\n\n\tlet active_source: \"microphone\" | \"upload\";\n\n\tlet initial_value: null | FileData = value;\n\n\t$: if (value && initial_value === null) {\n\t\tinitial_value = value;\n\t}\n\n\tconst handle_reset_value = (): void => {\n\t\tif (initial_value === null || value === initial_value) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalue = initial_value;\n\t};\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t\tif (!value_is_output) {\n\t\t\t\tgradio.dispatch(\"input\");\n\t\t\t}\n\t\t}\n\t}\n\n\tlet dragging: boolean;\n\n\t$: if (!active_source && sources) {\n\t\tactive_source = sources[0];\n\t}\n\n\tlet waveform_settings: Record<string, any>;\n\n\tlet color_accent = \"darkorange\";\n\n\tonMount(() => {\n\t\tcolor_accent = getComputedStyle(document?.documentElement).getPropertyValue(\n\t\t\t\"--color-accent\"\n\t\t);\n\t\tset_trim_region_colour();\n\t\twaveform_settings.waveColor = waveform_options.waveform_color || \"#9ca3af\";\n\t\twaveform_settings.progressColor =\n\t\t\twaveform_options.waveform_progress_color || color_accent;\n\t\twaveform_settings.mediaControls = waveform_options.show_controls;\n\t\twaveform_settings.sampleRate = waveform_options.sample_rate || 44100;\n\t});\n\n\t$: waveform_settings = {\n\t\theight: 50,\n\n\t\tbarWidth: 2,\n\t\tbarGap: 3,\n\t\tcursorWidth: 2,\n\t\tcursorColor: \"#ddd5e9\",\n\t\tautoplay: autoplay,\n\t\tbarRadius: 10,\n\t\tdragToSeek: true,\n\t\tnormalize: true,\n\t\tminPxPerSec: 20\n\t};\n\n\tconst trim_region_settings = {\n\t\tcolor: waveform_options.trim_region_color,\n\t\tdrag: true,\n\t\tresize: true\n\t};\n\n\tfunction set_trim_region_colour(): void {\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--trim-region-color\",\n\t\t\ttrim_region_settings.color || color_accent\n\t\t);\n\t}\n\n\tfunction handle_error({ detail }: CustomEvent<string>): void {\n\t\tconst [level, status] = detail.includes(\"Invalid file type\")\n\t\t\t? [\"warning\", \"complete\"]\n\t\t\t: [\"error\", \"error\"];\n\t\tloading_status = loading_status || {};\n\t\tloading_status.status = status as LoadingStatus[\"status\"];\n\t\tloading_status.message = detail;\n\t\tgradio.dispatch(level as \"error\" | \"warning\", detail);\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n</script>\n\n{#if !interactive}\n\t<Block\n\t\tvariant={\"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\tallow_overflow={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{visible}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\n\t\t<StaticAudio\n\t\t\ti18n={gradio.i18n}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\t{show_share_button}\n\t\t\t{value}\n\t\t\t{label}\n\t\t\t{loop}\n\t\t\t{waveform_settings}\n\t\t\t{waveform_options}\n\t\t\t{editable}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\t/>\n\t</Block>\n{:else}\n\t<Block\n\t\tvariant={value === null && active_source === \"upload\" ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\tallow_overflow={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{visible}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t\t<InteractiveAudio\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\t{value}\n\t\t\ton:change={({ detail }) => (value = detail)}\n\t\t\ton:stream={({ detail }) => {\n\t\t\t\tvalue = detail;\n\t\t\t\tgradio.dispatch(\"stream\", value);\n\t\t\t}}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\t{root}\n\t\t\t{sources}\n\t\t\t{active_source}\n\t\t\t{pending}\n\t\t\t{streaming}\n\t\t\t{loop}\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\t{handle_reset_value}\n\t\t\t{editable}\n\t\t\tbind:dragging\n\t\t\ton:edit={() => gradio.dispatch(\"edit\")}\n\t\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\t\ton:start_recording={() => gradio.dispatch(\"start_recording\")}\n\t\t\ton:pause_recording={() => gradio.dispatch(\"pause_recording\")}\n\t\t\ton:stop_recording={(e) => gradio.dispatch(\"stop_recording\")}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\t\ton:error={handle_error}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{waveform_settings}\n\t\t\t{waveform_options}\n\t\t\t{trim_region_settings}\n\t\t\tupload={gradio.client.upload}\n\t\t\tstream_handler={gradio.client.stream}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"audio\" />\n\t\t</InteractiveAudio>\n\t</Block>\n{/if}\n"], "file": "assets/index-BmHQU65L.js"}
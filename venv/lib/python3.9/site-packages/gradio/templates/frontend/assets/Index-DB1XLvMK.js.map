{"version": 3, "mappings": ";yFAAA,MAAeA,GAAA,u3BCEFC,GAAY,OAAO,OAAW,IAGpC,IAAIC,GAAMD,GAAY,IAAM,OAAO,YAAY,MAAQ,IAAM,KAAK,MAE9DE,GAAMF,GAAaG,GAAO,sBAAsBA,CAAE,EAAIC,GCLjE,MAAMC,GAAQ,IAAI,IAMlB,SAASC,GAAUL,EAAK,CACvBI,GAAM,QAASE,GAAS,CAClBA,EAAK,EAAEN,CAAG,IACdI,GAAM,OAAOE,CAAI,EACjBA,EAAK,EAAC,EAET,CAAE,EACGF,GAAM,OAAS,GAAGH,GAAII,EAAS,CACpC,CAgBO,SAASE,GAAKC,EAAU,CAE9B,IAAIF,EACJ,OAAIF,GAAM,OAAS,GAAGH,GAAII,EAAS,EAC5B,CACN,QAAS,IAAI,QAASI,GAAY,CACjCL,GAAM,IAAKE,EAAO,CAAE,EAAGE,EAAU,EAAGC,CAAO,EAC9C,CAAG,EACD,OAAQ,CACPL,GAAM,OAAOE,CAAI,CACjB,CACH,CACA,4YCRKI,EAAAC,KAAG,mBAAmB,iBAItBC,EAAAD,KAAG,kBAAkB,8DARyCA,EAAK,6EAKd,GACvD,+GAKab,EAAU,iHAXkBa,EAAK,yTAF/CE,GAiBKC,EAAAC,EAAAC,CAAA,EAhBJC,EAGMF,EAAAG,CAAA,EAFLD,EACAC,EAAAC,CAAA,gBAEDF,EAGMF,EAAAK,CAAA,gBADLH,EAAsDG,EAAAC,CAAA,gBAEvDJ,EAOMF,EAAAO,CAAA,gBALLL,EAIAK,EAAAC,CAAA,qBAb+DZ,EAAK,kDAA5BA,EAAK,mBAI5Ca,EAAA,KAAAd,OAAAC,KAAG,mBAAmB,OAAAc,GAAAC,EAAAhB,CAAA,EAItBc,EAAA,KAAAZ,OAAAD,KAAG,kBAAkB,OAAAc,GAAAE,EAAAf,CAAA,iGAXpBD,EAAO,IAAIA,EAAK,IAAIA,EAAI,IAAAiB,GAAAjB,CAAA,mJARaA,EAAO,kDAJrCkB,GAAAC,EAAA,OAAAnB,OAAYA,EAAQ,mDAETA,EAAO,qBACbA,EAAI,sBAEHA,EAAM,GAAG,UAAYA,EAAc,qBACnCA,EAAO,GAAS,OAAN,GAAY,UARzCE,GAkCKC,EAAAgB,EAAAd,CAAA,EAvBJC,EAEKa,EAAAC,CAAA,yIACApB,EAAO,IAAIA,EAAK,IAAIA,EAAI,mHARaA,EAAO,kDAJrCkB,GAAAC,EAAA,OAAAnB,OAAYA,EAAQ,2EAETA,EAAO,kCACbA,EAAI,6BAEHA,EAAM,GAAG,UAAYA,EAAc,2BACnCA,EAAO,GAAS,OAAN,GAAY,0KApB7B,QAAAqB,CAAuB,EAAAC,GACvB,QAAAC,CAAe,EAAAD,GACf,eAAAE,CAAsB,EAAAF,GACtB,WAAAG,CAAmB,EAAAH,GACnB,SAAAI,CAAiB,EAAAJ,GAEjB,MAAAK,CAAoB,EAAAL,GACpB,QAAAM,CAAgB,EAAAN,GAChB,KAAAO,CAAa,EAAAP,GACb,OAAAQ,CAAe,EAAAR,4CAIfD,EAAOU,swCChBZ,SAASC,GAAUC,EAAqB,CAC1C,IAAAC,EAAQ,CAAC,GAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAG,EAC9CC,EAAI,EACR,KAAOF,EAAM,KAAQE,EAAID,EAAM,OAAS,GAChCD,GAAA,IACPE,IAEG,IAAAC,EAAOF,EAAMC,CAAC,EACV,cAAO,UAAUF,CAAG,EAAIA,EAAMA,EAAI,QAAQ,CAAC,GAAKG,CACzD,CCLO,SAASC,GAAQC,EAAK,CAC5B,OAAO,OAAO,UAAU,SAAS,KAAKA,CAAG,IAAM,eAChD,CCMA,SAASC,GAAYvC,EAAKwC,EAAYC,EAAeC,EAAc,CAClE,GAAI,OAAOD,GAAkB,UAAYJ,GAAQI,CAAa,EAAG,CAEhE,MAAME,EAAQD,EAAeD,EAEvBG,GAAYH,EAAgBD,IAAexC,EAAI,IAAM,EAAI,IACzD6C,EAAS7C,EAAI,KAAK,UAAY2C,EAC9BG,EAAS9C,EAAI,KAAK,QAAU4C,EAC5BG,GAAgBF,EAASC,GAAU9C,EAAI,SACvCgD,GAAKJ,EAAWG,GAAgB/C,EAAI,GAC1C,OAAI,KAAK,IAAIgD,CAAC,EAAIhD,EAAI,KAAK,WAAa,KAAK,IAAI2C,CAAK,EAAI3C,EAAI,KAAK,UAC3D0C,GAEP1C,EAAI,QAAU,GAEPqC,GAAQI,CAAa,EAAI,IAAI,KAAKA,EAAc,QAAO,EAAKO,CAAC,EAAIP,EAAgBO,EAEzF,KAAM,IAAI,MAAM,QAAQP,CAAa,EAErC,OAAOA,EAAc,IAAI,CAACQ,EAAGd,IAC5BI,GAAYvC,EAAKwC,EAAWL,CAAC,EAAGM,EAAcN,CAAC,EAAGO,EAAaP,CAAC,CAAC,CACpE,EACQ,GAAI,OAAOM,GAAkB,SAAU,CAC7C,MAAMS,EAAa,GACnB,UAAWC,KAAKV,EAEfS,EAAWC,CAAC,EAAIZ,GAAYvC,EAAKwC,EAAWW,CAAC,EAAGV,EAAcU,CAAC,EAAGT,EAAaS,CAAC,CAAC,EAGlF,OAAOD,CACT,KACE,OAAM,IAAI,MAAM,iBAAiB,OAAOT,CAAa,SAAS,EAEhE,CAWO,SAASI,GAAOO,EAAOC,EAAO,GAAI,CACxC,MAAMC,EAAQC,GAASH,CAAK,EACtB,CAAE,UAAAI,EAAY,IAAM,QAAAC,EAAU,GAAK,UAAAC,EAAY,GAAM,EAAGL,EAE9D,IAAIM,EAEAhE,EAEAiE,EAEApB,EAAaY,EAEbV,EAAeU,EACfS,EAAW,EACXC,EAAyB,EACzBC,EAAc,GAMlB,SAASC,EAAIC,EAAWZ,EAAO,GAAI,CAClCX,EAAeuB,EACf,MAAMC,EAASN,EAAgB,GAC/B,OAAIR,GAAS,MAAQC,EAAK,MAASR,EAAO,WAAa,GAAKA,EAAO,SAAW,GAC7EkB,EAAc,GACdJ,EAAYtE,GAAG,EACfmD,EAAayB,EACbX,EAAM,IAAKF,EAAQV,GACZ,QAAQ,YACLW,EAAK,OAEfS,EAAyB,IADZT,EAAK,OAAS,GAAO,GAAM,CAACA,EAAK,MACT,IACrCQ,EAAW,GAEPlE,IACJgE,EAAYtE,GAAG,EACf0E,EAAc,GACdpE,EAAOC,GAAMP,GAAQ,CACpB,GAAI0E,EACH,OAAAA,EAAc,GACdpE,EAAO,KACA,GAERkE,EAAW,KAAK,IAAIA,EAAWC,EAAwB,CAAC,EACxD,MAAM9D,EAAM,CACX,SAAA6D,EACA,KAAMhB,EACN,QAAS,GACT,IAAMxD,EAAMsE,GAAa,GAAM,GACpC,EACUT,EAAaX,GAAYvC,EAAKwC,EAAYY,EAAOV,CAAY,EACnE,OAAAiB,EAAYtE,EACZmD,EAAaY,EACbE,EAAM,IAAKF,EAAQF,GACflD,EAAI,UACPL,EAAO,MAED,CAACK,EAAI,OAChB,CAAI,GAEK,IAAI,QAASmE,GAAW,CAC9BxE,EAAK,QAAQ,KAAK,IAAM,CACnBuE,IAAUN,GAAeO,GACjC,CAAI,CACJ,CAAG,EACD,CAED,MAAMtB,EAAS,CACd,IAAAmB,EACA,OAAQ,CAACI,EAAIf,IAASW,EAAII,EAAG1B,EAAcU,CAAK,EAAGC,CAAI,EACvD,UAAWC,EAAM,UACjB,UAAAE,EACA,QAAAC,EACA,UAAAC,CACF,EACC,OAAOb,CACR,2NCpIU,SAAAwB,EAAA,SAAuB,2xBAwCCC,GAAAC,EAAA,yBAAAvE,EAAK,IAAC,EAAO,OAAAA,KAAK,CAAC,qjBAoBnBsE,GAAAE,EAAA,yBAAAxE,EAAQ,IAAC,EAAO,OAAAA,KAAQ,CAAC,mMA1B3DE,GA+CKC,EAAAC,EAAAC,CAAA,EA9CJC,GA6CKF,EAAAqE,CAAA,EAxCJnE,GAmBGmE,EAAAF,CAAA,EAlBFjE,GAICiE,EAAAG,CAAA,EACDpE,GAGCiE,EAAAI,CAAA,EACDrE,GAICiE,EAAAK,CAAA,EACDtE,GAGCiE,EAAAM,CAAA,EAEFvE,GAmBGmE,EAAAD,CAAA,EAlBFlE,GAICkE,EAAAM,CAAA,EACDxE,GAGCkE,EAAAO,CAAA,EACDzE,GAICkE,EAAAQ,CAAA,EACD1E,GAGCkE,EAAAS,CAAA,iBAtC8BX,GAAAC,EAAA,yBAAAvE,EAAK,IAAC,EAAO,OAAAA,KAAK,CAAC,cAoBnBsE,GAAAE,EAAA,yBAAAxE,EAAQ,IAAC,EAAO,OAAAA,KAAQ,CAAC,wFAzD/C,QAAAkF,EAAS,EAAI,EAAA5D,EAElB,MAAA6D,EAAMtC,GAAQ,GAAG,CAAC,uBAClB,MAAAuC,EAASvC,GAAQ,GAAG,CAAC,2BAEvBwC,iBAEWC,GAAO,CACf,cAAQ,IAAG,CAAEH,EAAI,IAAK,KAAK,GAAG,GAAIC,EAAO,IAAM,UAAS,KACxD,cAAQ,IAAG,CAAED,EAAI,IAAM,MAAK,GAAG,GAAIC,EAAO,IAAK,SAAS,KACxD,cAAQ,IAAG,CAAED,EAAI,IAAM,MAAK,CAAC,GAAIC,EAAO,IAAK,OAAO,KACpD,cAAQ,IAAG,CAAED,EAAI,IAAK,KAAK,CAAC,GAAIC,EAAO,IAAM,MAAK,CAAC,oBAG3CG,GAAG,OACXD,EAAO,EACRD,GAAYE,mBAGHC,GAAO,CACf,cAAQ,IAAG,CAAEL,EAAI,IAAK,KAAK,CAAC,GAAIC,EAAO,IAAM,MAAK,CAAC,KAEzDG,IAGDlB,UAAO,KACNmB,IACuB,IAAAH,EAAa,okBCCdrF,EAAK,0CAAZE,GAAoBC,EAAAsF,EAAApF,CAAA,4BAAbL,EAAK,wDAAvBA,EAAU,IAAAiB,GAAAjB,CAAA,wHAEDkB,EAAAd,EAAA,QAAAJ,OAAS,OAAO,EAChBkB,EAAAd,EAAA,QAAAJ,OAAS,OAAO,EACfkB,EAAAd,EAAA,SAAAJ,OAAS,QAAQ,oCAfpBA,EAAK,yBACFA,EAAQ,iBAChBA,EAAK,uHAKEsE,GAAAoB,EAAA,SAAA1F,MAAYA,EAAM,IAAGA,EAAM,IAAG,+BAA+B,oBACxDA,EAAQ,GAAgB,OAAbA,EAAU,GAAS,EAC9BsE,GAAAoB,EAAA,cAAA1F,MAAS,IAAI,UAZjCE,GAsBQC,EAAAuF,EAAArF,CAAA,yBAPPC,GAMKoF,EAAAtF,CAAA,8DAPAJ,EAAU,sEAEDkB,EAAAd,EAAA,QAAAJ,OAAS,OAAO,cAChBkB,EAAAd,EAAA,QAAAJ,OAAS,OAAO,cACfkB,EAAAd,EAAA,SAAAJ,OAAS,QAAQ,8DAfpBA,EAAK,sCACFA,EAAQ,4BAChBA,EAAK,iJAKEsE,GAAAoB,EAAA,SAAA1F,MAAYA,EAAM,IAAGA,EAAM,IAAG,+BAA+B,4BACxDA,EAAQ,GAAgB,OAAbA,EAAU,GAAS,UAC9BsE,GAAAoB,EAAA,cAAA1F,MAAS,IAAI,wIA5BrB,KAAA2F,CAAmB,EAAArE,EACnB,OAAAsE,EAAQ,EAAE,EAAAtE,EACV,YAAAuE,EAAa,EAAK,EAAAvE,EAClB,SAAAwE,EAAU,EAAK,EAAAxE,EACf,MAAAyE,EAAqC,OAAO,EAAAzE,EAC5C,QAAA0E,EAAS,EAAI,EAAA1E,EACb,WAAA2E,EAAY,EAAK,EAAA3E,EACjB,UAAA4E,EAAW,EAAK,EAAA5E,EAChB,UAAA6E,EAAW,EAAK,EAAA7E,EAChB,OAAA8E,EAAQ,+BAA+B,EAAA9E,EACvC,aAAA+E,EAAc,EAAK,EAAA/E,EACnB,YAAAgF,EAAa,gCAAgC,EAAAhF,EAC7C,QAAAiF,EAAS,CAAC,EAAAjF,ogBAClBkF,EAASP,EAAY,sBAAwBG,CAAK,iqECftDlG,GAuBKC,EAAAsE,EAAApE,CAAA,EAZJC,GAOGmE,EAAAgC,CAAA,EAJFnG,GAGCmG,EAAA/B,CAAA,EAEFpE,GAGCmE,EAAAE,CAAA,uGCpBK,MAAM+B,GAAiB,CAC7B,MACA,QACA,OACA,SACA,SACA,OACA,SACA,OACA,OACA,MACD,EAoBaC,GAAe,CAC3B,CAAE,MAAO,MAAO,QAAS,IAAK,UAAW,GAAI,EAC7C,CAAE,MAAO,QAAS,QAAS,IAAK,UAAW,GAAI,EAC/C,CAAE,MAAO,OAAQ,QAAS,IAAK,UAAW,GAAI,EAC9C,CAAE,MAAO,SAAU,QAAS,IAAK,UAAW,GAAI,EAChD,CAAE,MAAO,SAAU,QAAS,IAAK,UAAW,GAAI,EAChD,CAAE,MAAO,OAAQ,QAAS,IAAK,UAAW,GAAI,EAC9C,CAAE,MAAO,SAAU,QAAS,IAAK,UAAW,GAAI,EAChD,CAAE,MAAO,OAAQ,QAAS,IAAK,UAAW,GAAI,EAC9C,CAAE,MAAO,OAAQ,QAAS,IAAK,UAAW,GAAI,EAC9C,CAAE,MAAO,OAAQ,QAAS,IAAK,UAAW,GAAI,CAC/C,EAEMC,GAAY,CACjB,QAAS,UACT,QAAS,eACT,YAAa,cACb,MAAO,OACP,MAAO,OACP,MAAO,CACN,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,QAAS,CACR,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,MAAO,CACN,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,IAAK,CACJ,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,OAAQ,CACP,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,MAAO,CACN,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,OAAQ,CACP,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,MAAO,CACN,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,QAAS,CACR,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,IAAK,CACJ,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,OAAQ,CACP,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,OAAQ,CACP,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,OAAQ,CACP,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,QAAS,CACR,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,CACD,EAEaC,GAASF,GAAa,OAClC,CAACG,EAAK,CAAE,MAAAV,EAAO,QAAAW,EAAS,UAAAC,MAAiB,CACxC,GAAGF,EACH,CAACV,CAAK,EAAG,CACR,QAASQ,GAAUR,CAAK,EAAEW,CAAO,EACjC,UAAWH,GAAUR,CAAK,EAAEY,CAAS,CACtC,IAED,CAAC,CACF,geC5VU,MAAAC,WAAoB,2BA8CpB,WAAAC,WAAyB,2BAKzB,uBAAAC,EAAA,SAAqC,4NA8OxBC,EAAApH,KAAK,cAAc,iCARhCqH,GACC,MAAArH,KAAK,cAAc,WAChB,6OAJZE,EASKC,EAAAC,EAAAC,CAAA,wBACLH,EAAgDC,EAAAsF,EAAApF,CAAA,uDAPvCQ,EAAA,OAAAyG,EAAA,MAAAtH,KAAK,cAAc,cAOP,CAAAuH,GAAA1G,EAAA,OAAAuG,OAAApH,KAAK,cAAc,OAAAc,GAAA0G,EAAAJ,CAAA,yPAnFnCK,EAAAzH,OAAY,WAAaA,EAAgB,KAAAA,OAAkB,QAAM0H,GAAA1H,CAAA,qBAWhEA,EAAQ,UAAA2H,MAWH3H,EAAc,KAAK,MAAQA,OAAe,QAAaA,EAAc,IAAI,EAAC,OAAA4H,GAE1E,GAAA5H,OAAmB,EAAC,OAAA6H,0BAIzB7H,EAAK,IAAA8H,GAAA9H,CAAA,uCAKN,OAAAA,OAAuB,KAAI,EA+BtBA,OAAkB,OAAM,wCAI5BA,EAAK,IAAA+H,GAAA/H,CAAA,qJA7DckB,GAAAd,EAAA,mBAAAJ,OAAY,QAAQ,EAC3BkB,GAAAd,EAAA,YAAAJ,OAAY,SAAS,+BAFvCE,EAyBKC,EAAAC,EAAAC,CAAA,4GA/BAL,OAAY,WAAaA,EAAgB,KAAAA,OAAkB,4IA4B1DA,EAAK,6EArBckB,GAAAd,EAAA,mBAAAJ,OAAY,QAAQ,kBAC3BkB,GAAAd,EAAA,YAAAJ,OAAY,SAAS,sKA4DjCA,EAAK,qRAjEqBgI,EAAA,eAAAhI,EAAa,QAAK,IAAM,GAAG,8FAF1DE,EAGCC,EAAAC,EAAAC,CAAA,UAD8BQ,EAAA,WAAAmH,OAAA,eAAAhI,EAAa,QAAK,IAAM,GAAG,+EAqB5B,cAE9B,6DAHSoH,EAAApH,KAAiB,EAAC,0BADyD,SAC5E,aAAoB,GAAC,MAACA,EAAU,QAAC,IACzC,+DADSa,EAAA,MAAAuG,OAAApH,KAAiB,EAAC,KAAAc,GAAA0G,EAAAJ,CAAA,eAAGpH,EAAU,oEAXhCA,EAAQ,yBAAb,OAAImC,GAAA,qKAACnC,EAAQ,sBAAb,OAAImC,GAAA,6HAAJ,qDAOCiF,EAAApH,MAAE,KAAI,SAAK,6BALPA,EAAC,IAAC,QAAU,KAAIiI,2DAKb,KAAG,8IAAVpH,EAAA,QAAAuG,OAAApH,MAAE,KAAI,KAAAc,GAAA0G,EAAAJ,CAAA,yDAFL,IAAAc,EAAAlG,GAAUhC,EAAE,WAAS,CAAC,kDAAtBa,EAAA,QAAAqH,OAAAlG,GAAUhC,EAAE,WAAS,CAAC,OAAAc,GAAA,EAAAoH,CAAA,iCAFtB,IAAAC,EAAAnG,GAAUhC,EAAE,WAAS,CAAC,WAAIgC,GAAUhC,EAAC,IAAC,MAAM,6BAApB,GAAC,oDAAzBa,EAAA,QAAAsH,OAAAnG,GAAUhC,EAAE,WAAS,CAAC,OAAAc,GAAAsH,EAAAD,CAAA,mBAAInG,GAAUhC,EAAC,IAAC,MAAM,OAAAc,GAAAC,EAAAhB,CAAA,qDAF1CC,EAAC,IAAC,OAAS,MAAIqI,GAAArI,CAAA,kEAAfA,EAAC,IAAC,OAAS,wHAgBCA,EAAG,OAAOA,EAAa,MAAK,sBAA7CA,EAAe,gBAAiC,GAClD,gEADEA,EAAe,yBAAEA,EAAG,OAAOA,EAAa,MAAK,KAAEc,GAAA0G,EAAAJ,CAAA,sEAoCjC,cAAApH,OAAY,SAAS,qEAArBa,EAAA,SAAAyH,EAAA,OAAAtI,OAAY,qIALXuI,EAAA,GAAAvI,MAAsB,GAAG,IAxBnCwI,EAAAxI,MAAY,MAAIyI,GAAAzI,CAAA,gSAFvBE,EA6BKC,EAAAuI,EAAArI,CAAA,EA5BJC,GAmBKoI,EAAAtH,CAAA,yBAELd,GAMKoI,EAAAC,CAAA,EALJrI,GAICqI,EAAAxH,CAAA,mBAzBInB,MAAY,+DAwBFa,EAAA,UAAA0H,OAAA,GAAAvI,MAAsB,GAAG,+FAvBhCA,EAAQ,yBAAb,OAAImC,GAAA,uKAACnC,EAAQ,sBAAb,OAAImC,GAAA,6HAAJ,2DAEKsF,EAAAzH,QAAM,GAAC4I,GAAA,IAGP5I,EAAC,IAAC,MAAQ,MAAI6I,GAAA7I,CAAA,IAGdA,EAAC,IAAC,MAAQ,MAAQA,EAAc,KAAIA,EAAc,IAACA,EAAC,MAAK,MAAI8I,GAAA,EAG7DC,EAAA/I,OAAkB,MAAIgJ,GAAAhJ,CAAA,iLANtBA,EAAC,IAAC,MAAQ,uEAGVA,EAAC,IAAC,MAAQ,MAAQA,EAAc,KAAIA,EAAc,IAACA,EAAC,MAAK,8DAGzDA,OAAkB,oLATX,IAEZ,kDAEE,IAAAkI,EAAAlI,MAAE,KAAI,gDAANa,EAAA,QAAAqH,OAAAlI,MAAE,KAAI,KAAAc,GAAA,EAAAoH,CAAA,sDAE0D,GAElE,yDAEG,KAAOlI,EAAe,IAAAA,QAAM,IAAI,QAAQ,CAAC,+BAAE,GAC9C,wDADG,KAAOA,EAAe,IAAAA,QAAM,IAAI,QAAQ,CAAC,OAAAc,GAAAsH,EAAAD,CAAA,iDAXxCnI,EAAC,IAAC,MAAQ,MAASA,EAAc,KAAIA,EAAc,IAACA,EAAC,MAAK,OAAIiJ,GAAAjJ,CAAA,kEAA9DA,EAAC,IAAC,MAAQ,MAASA,EAAc,KAAIA,EAAc,IAACA,EAAC,MAAK,iNA+B/CA,EAAY,kEAAhCE,EAAoCC,EAAA+I,EAAA7I,CAAA,iEAAhBL,EAAY,gOAtE7B,OAAAA,OAAW,UAAS,EAyEfA,OAAW,QAAO,iEArFfmJ,GAAA/I,EAAA,QAAAgJ,EAAA,QAAApJ,SAAUA,EAAa,qBACvBkB,GAAAd,EAAA,QAAAJ,MAAUA,EAAM,KAAK,YAAcA,OAAkB,QAAQ,EACtDkB,GAAAd,EAAA,cAAAJ,EAAY,gBAC9BA,OAAW,WAAaA,EAAM,KAAK,UACpCA,EACA,KAAAA,OAAkB,SAAS,EACVkB,GAAAd,EAAA,aAAAJ,EAAW,mBAAgBA,OAAkB,MAAM,uCAErDA,EAAQ,IAAG,WAAa,QAAQ,iBACjCA,EAAQ,IAAG,IAAM,iBAAiB,UAVlDE,EAoGKC,EAAAC,EAAAC,CAAA,wMAnGS,CAAAkH,GAAA1G,EAAA,QAAAuI,OAAA,QAAApJ,SAAUA,EAAa,uDACvBkB,GAAAd,EAAA,QAAAJ,MAAUA,EAAM,KAAK,YAAcA,OAAkB,QAAQ,mBACtDkB,GAAAd,EAAA,cAAAJ,EAAY,gBAC9BA,OAAW,WAAaA,EAAM,KAAK,UACpCA,EACA,KAAAA,OAAkB,SAAS,kBACVkB,GAAAd,EAAA,aAAAJ,EAAW,mBAAgBA,OAAkB,MAAM,mEAErDA,EAAQ,IAAG,WAAa,QAAQ,4BACjCA,EAAQ,IAAG,IAAM,iBAAiB,sFAvM7CqJ,GAAK,GAELC,GAAS,GAEE,eAAAC,GACdC,EACAC,EAAyB,GAAI,CAG5B,YAAO,kBAAoB,WAC1B,OAAO,kBAAoB,OAASA,IAAW,QAKjDJ,GAAM,KAAKG,CAAE,GACRF,GAAQA,GAAS,OAAI,cAGpBrC,GAAI,EAEV,sBAAqB,SAChByC,EAAG,CAAI,EAAG,CAAC,UAEN,EAAI,EAAG,EAAIL,GAAM,OAAQ,IAAC,OAG5BM,EAFUN,GAAM,CAAC,EAEH,yBAChB,IAAM,GAAKM,EAAI,IAAM,OAAO,SAAWD,EAAI,CAAC,KAC/CA,EAAI,CAAC,EAAIC,EAAI,IAAM,OAAO,QAC1BD,EAAI,CAAC,EAAI,GAIX,OAAO,SAAW,KAAKA,EAAI,CAAC,EAAI,GAAI,SAAU,QAAQ,GAEtDJ,GAAS,GACTD,GAAK,0DAgBD,MAAAO,EAAWzC,SAEN,KAAA0C,CAAmB,EAAAvI,EACnB,KAAAwI,EAAqB,IAAI,EAAAxI,GACzB,eAAAyI,CAA6B,EAAAzI,GAC7B,WAAA0I,CAAyB,EAAA1I,GACzB,OAAA2I,CAA8D,EAAA3I,EAC9D,kBAAA4I,EAAmB,EAAK,EAAA5I,EACxB,OAAA6I,EAAQ,EAAI,EAAA7I,EACZ,eAAA8I,EAA+C,MAAM,EAAA9I,EACrD,SAAA+I,EAAyB,IAAI,EAAA/I,EAC7B,UAAAgJ,EAAyD,IAAI,EAAAhJ,EAC7D,SAAAiJ,EAAgC,SAAS,EAAAjJ,EACzC,cAAAkJ,EAAe,YAAY,EAAAlJ,EAC3B,UAAAmJ,EAAW,EAAI,EAAAnJ,EACf,aAAAoJ,EAAc,EAAK,EAAApJ,EACnB,QAAAqJ,EAAS,EAAK,EAAArJ,GACd,WAAAsJ,EAAmB,EAAAtJ,EAE1BkI,GAEAqB,GAAS,GACTC,EAAc,EACdC,EAAa,EACbC,EAAyB,KACzBC,EAAgC,KAEhCC,GAA2B,EAC3BC,GAAgD,KAChDC,GACAC,GAAmC,KACnCC,GAAe,SAsCbC,GAAW,KAChBC,EAAA,EAAA1B,EAAM0B,EAAA,GAAAR,EAAUQ,EAAA,GAAAC,EAAgB,IAAI,SACpCX,EAAc,YAAY,IAAG,GAC7BU,EAAA,GAAAT,EAAa,CAAC,EACdF,GAAS,GACTtF,cAGQA,GAAG,CACX,sBAAqB,KACpBiG,EAAA,GAAAT,GAAc,YAAY,MAAQD,GAAe,GAAI,EACjDD,IAAQtF,eAILmG,IAAU,CAClBF,EAAA,GAAAT,EAAa,CAAC,EACdS,EAAA,EAAA1B,EAAM0B,EAAA,GAAAR,EAAUQ,EAAA,GAAAC,EAAgB,IAAI,IAE/BZ,KACLA,GAAS,IAGV3D,GAAS,KACJ2D,IAAQa,OAgBT,IAAAD,EAA+B,+CAmGnBJ,GAAYtJ,mDAqBxB6H,EAAS,cAAc,8CAlFhBJ,GAAEzH,4qBApCR+H,IAAQ,MACX0B,EAAA,EAAA1B,EAAMkB,CAAO,EAEVlB,GAAO,MAAQkB,IAAYlB,SAC9BmB,GAAkB,YAAY,IAAG,EAAKH,GAAe,IAAOhB,CAAG,EAC/D0B,EAAA,GAAAC,EAAgBR,EAAe,QAAQ,CAAC,GACxCO,EAAA,GAAAR,EAAUlB,CAAG,6BApFd0B,EAAA,GAAEN,GACFD,IAAmB,MAAQA,GAAkB,GAAM,CAAAF,EAChD,KACA,KAAK,IAAIA,EAAaE,EAAgB,CAAC,sBACpCX,GAAY,MAClBkB,EAAA,GAAAF,GAAe,EAAK,yBAIhBhB,GAAY,KACfkB,EAAA,GAAAL,GAAiBb,EAAS,IAAKqB,GAAC,IAC3BA,EAAE,OAAS,MAAQA,EAAE,QAAU,KAC3B,OAAAA,EAAE,MAAQA,EAAE,UACTA,EAAE,UAAY,KACjB,OAAAA,EAAE,YAKXH,EAAA,GAAAL,GAAiB,IAAI,EAGlBA,IACHK,EAAA,GAAAJ,GAAsBD,GAAeA,GAAe,OAAS,CAAC,GAC1DE,KACCD,KAAwB,EAC3BI,EAAA,GAAAH,GAAa,MAAM,WAAa,IAAGA,EAAA,EAEnCG,EAAA,GAAAH,GAAa,MAAM,WAAa,QAAOA,EAAA,IAIzCG,EAAA,GAAAJ,GAAsB,MAAS,sBAgC5BnB,IAAW,UACdsB,KAEAG,8BAIClC,IACFU,IACCD,IAAW,WAAaA,IAAW,aACpCV,GAAiBC,GAAIoB,EAAU,qDA0B7BgB,EAAkBb,EAAW,QAAQ,CAAC,6qDC9L1C,2LAEA,KAAM,CACJ,QAAAc,GACA,eAAAC,GACA,SAAAC,GACA,eAAAC,GACA,yBAAAC,EACF,EAAI,OACJ,GAAI,CACF,OAAAC,EACA,KAAAC,GACF,OAAEC,EACF,EAAI,OAEA,CACF,MAAAC,GACA,UAAAC,EACF,EAAI,OAAO,QAAY,KAAe,QAEjCD,KACHA,GAAQ,SAAeE,EAAKC,EAAWC,EAAM,CAC3C,OAAOF,EAAI,MAAMC,EAAWC,CAAI,CACpC,GAGKP,IACHA,EAAS,SAAgBQ,EAAG,CAC1B,OAAOA,CACX,GAGKP,KACHA,GAAO,SAAcO,EAAG,CACtB,OAAOA,CACX,GAGKJ,KACHA,GAAY,SAAmBK,EAAMF,EAAM,CACzC,OAAO,IAAIE,EAAK,GAAGF,CAAI,CAC3B,GAGA,MAAMG,GAAeC,GAAQ,MAAM,UAAU,OAAO,EAC9CC,GAAWD,GAAQ,MAAM,UAAU,GAAG,EACtCE,GAAYF,GAAQ,MAAM,UAAU,IAAI,EACxCG,GAAoBH,GAAQ,OAAO,UAAU,WAAW,EACxDI,GAAiBJ,GAAQ,OAAO,UAAU,QAAQ,EAClDK,GAAcL,GAAQ,OAAO,UAAU,KAAK,EAC5CM,GAAgBN,GAAQ,OAAO,UAAU,OAAO,EAChDO,GAAgBP,GAAQ,OAAO,UAAU,OAAO,EAChDQ,GAAaR,GAAQ,OAAO,UAAU,IAAI,EAC1CS,GAAaT,GAAQ,OAAO,UAAU,IAAI,EAC1CU,GAAkBC,GAAY,SAAS,EAC7C,SAASX,GAAQY,EAAM,CACrB,OAAO,SAAUC,EAAS,CACxB,QAASC,EAAO,UAAU,OAAQlB,EAAO,IAAI,MAAMkB,EAAO,EAAIA,EAAO,EAAI,CAAC,EAAGC,EAAO,EAAGA,EAAOD,EAAMC,IAClGnB,EAAKmB,EAAO,CAAC,EAAI,UAAUA,CAAI,EAGjC,OAAOvB,GAAMoB,EAAMC,EAASjB,CAAI,CACpC,CACA,CACA,SAASe,GAAYC,EAAM,CACzB,OAAO,UAAY,CACjB,QAASI,EAAQ,UAAU,OAAQpB,EAAO,IAAI,MAAMoB,CAAK,EAAGC,EAAQ,EAAGA,EAAQD,EAAOC,IACpFrB,EAAKqB,CAAK,EAAI,UAAUA,CAAK,EAG/B,OAAOxB,GAAUmB,EAAMhB,CAAI,CAC/B,CACA,CAGA,SAASsB,EAAS/J,EAAKgK,EAAOC,EAAmB,CAC/C,IAAIC,EAEJD,GAAqBC,EAAqBD,KAAuB,MAAQC,IAAuB,OAASA,EAAqBlB,GAE1HlB,IAIFA,GAAe9H,EAAK,IAAI,EAG1B,IAAI,EAAIgK,EAAM,OAEd,KAAO,KAAK,CACV,IAAIG,EAAUH,EAAM,CAAC,EAErB,GAAI,OAAOG,GAAY,SAAU,CAC/B,MAAMC,EAAYH,EAAkBE,CAAO,EAEvCC,IAAcD,IAEXpC,GAASiC,CAAK,IACjBA,EAAM,CAAC,EAAII,GAGbD,EAAUC,EAEb,CAEDpK,EAAImK,CAAO,EAAI,EAChB,CAED,OAAOnK,CACT,CAGA,SAASqK,GAAMC,EAAQ,CACrB,MAAMC,EAAYnC,GAAO,IAAI,EAE7B,SAAW,CAACoC,EAAUpL,CAAK,IAAKyI,GAAQyC,CAAM,EAC5CC,EAAUC,CAAQ,EAAIpL,EAGxB,OAAOmL,CACT,CAIA,SAASE,GAAaH,EAAQI,EAAM,CAClC,KAAOJ,IAAW,MAAM,CACtB,MAAMK,EAAO1C,GAAyBqC,EAAQI,CAAI,EAElD,GAAIC,EAAM,CACR,GAAIA,EAAK,IACP,OAAO9B,GAAQ8B,EAAK,GAAG,EAGzB,GAAI,OAAOA,EAAK,OAAU,WACxB,OAAO9B,GAAQ8B,EAAK,KAAK,CAE5B,CAEDL,EAAStC,GAAesC,CAAM,CAC/B,CAED,SAASM,EAAcT,EAAS,CAC9B,eAAQ,KAAK,qBAAsBA,CAAO,EACnC,IACR,CAED,OAAOS,CACT,CAEA,MAAMC,GAAS3C,EAAO,CAAC,IAAK,OAAQ,UAAW,UAAW,OAAQ,UAAW,QAAS,QAAS,IAAK,MAAO,MAAO,MAAO,QAAS,aAAc,OAAQ,KAAM,SAAU,SAAU,UAAW,SAAU,OAAQ,OAAQ,MAAO,WAAY,UAAW,OAAQ,WAAY,KAAM,YAAa,MAAO,UAAW,MAAO,SAAU,MAAO,MAAO,KAAM,KAAM,UAAW,KAAM,WAAY,aAAc,SAAU,OAAQ,SAAU,OAAQ,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,OAAQ,SAAU,SAAU,KAAM,OAAQ,IAAK,MAAO,QAAS,MAAO,MAAO,QAAS,SAAU,KAAM,OAAQ,MAAO,OAAQ,UAAW,OAAQ,WAAY,QAAS,MAAO,OAAQ,KAAM,WAAY,SAAU,SAAU,IAAK,UAAW,MAAO,WAAY,IAAK,KAAM,KAAM,OAAQ,IAAK,OAAQ,UAAW,SAAU,SAAU,QAAS,SAAU,SAAU,OAAQ,SAAU,SAAU,QAAS,MAAO,UAAW,MAAO,QAAS,QAAS,KAAM,WAAY,WAAY,QAAS,KAAM,QAAS,OAAQ,KAAM,QAAS,KAAM,IAAK,KAAM,MAAO,QAAS,KAAK,CAAC,EAEz+B4C,GAAQ5C,EAAO,CAAC,MAAO,IAAK,WAAY,cAAe,eAAgB,eAAgB,gBAAiB,mBAAoB,SAAU,WAAY,OAAQ,OAAQ,UAAW,SAAU,OAAQ,IAAK,QAAS,WAAY,QAAS,QAAS,OAAQ,iBAAkB,SAAU,OAAQ,WAAY,QAAS,OAAQ,UAAW,UAAW,WAAY,iBAAkB,OAAQ,OAAQ,QAAS,SAAU,SAAU,OAAQ,WAAY,QAAS,OAAQ,QAAS,OAAQ,OAAO,CAAC,EACnd6C,GAAa7C,EAAO,CAAC,UAAW,gBAAiB,sBAAuB,cAAe,mBAAoB,oBAAqB,oBAAqB,iBAAkB,eAAgB,UAAW,UAAW,UAAW,UAAW,UAAW,iBAAkB,UAAW,UAAW,cAAe,eAAgB,WAAY,eAAgB,qBAAsB,cAAe,SAAU,cAAc,CAAC,EAK/Y8C,GAAgB9C,EAAO,CAAC,UAAW,gBAAiB,SAAU,UAAW,YAAa,mBAAoB,iBAAkB,gBAAiB,gBAAiB,gBAAiB,QAAS,YAAa,OAAQ,eAAgB,YAAa,UAAW,gBAAiB,SAAU,MAAO,aAAc,UAAW,KAAK,CAAC,EACtT+C,GAAW/C,EAAO,CAAC,OAAQ,WAAY,SAAU,UAAW,QAAS,SAAU,KAAM,aAAc,gBAAiB,KAAM,KAAM,QAAS,UAAW,WAAY,QAAS,OAAQ,KAAM,SAAU,QAAS,SAAU,OAAQ,OAAQ,UAAW,SAAU,MAAO,QAAS,MAAO,SAAU,aAAc,aAAa,CAAC,EAGtTgD,GAAmBhD,EAAO,CAAC,UAAW,cAAe,aAAc,WAAY,YAAa,UAAW,UAAW,SAAU,SAAU,QAAS,YAAa,aAAc,iBAAkB,cAAe,MAAM,CAAC,EAClNiD,GAAOjD,EAAO,CAAC,OAAO,CAAC,EAEvBkD,GAAOlD,EAAO,CAAC,SAAU,SAAU,QAAS,MAAO,iBAAkB,eAAgB,uBAAwB,WAAY,aAAc,UAAW,SAAU,UAAW,cAAe,cAAe,UAAW,OAAQ,QAAS,QAAS,QAAS,OAAQ,UAAW,WAAY,eAAgB,SAAU,cAAe,WAAY,WAAY,UAAW,MAAO,WAAY,0BAA2B,wBAAyB,WAAY,YAAa,UAAW,eAAgB,OAAQ,MAAO,UAAW,SAAU,SAAU,OAAQ,OAAQ,WAAY,KAAM,YAAa,YAAa,QAAS,OAAQ,QAAS,OAAQ,OAAQ,UAAW,OAAQ,MAAO,MAAO,YAAa,QAAS,SAAU,MAAO,YAAa,WAAY,QAAS,OAAQ,QAAS,UAAW,aAAc,SAAU,OAAQ,UAAW,UAAW,cAAe,cAAe,SAAU,UAAW,UAAW,aAAc,WAAY,MAAO,WAAY,MAAO,WAAY,OAAQ,OAAQ,UAAW,aAAc,QAAS,WAAY,QAAS,OAAQ,QAAS,OAAQ,UAAW,QAAS,MAAO,SAAU,OAAQ,QAAS,UAAW,WAAY,QAAS,YAAa,OAAQ,SAAU,SAAU,QAAS,QAAS,QAAS,MAAM,CAAC,EACxqCzH,GAAMyH,EAAO,CAAC,gBAAiB,aAAc,WAAY,qBAAsB,SAAU,gBAAiB,gBAAiB,UAAW,gBAAiB,iBAAkB,QAAS,OAAQ,KAAM,QAAS,OAAQ,gBAAiB,YAAa,YAAa,QAAS,sBAAuB,8BAA+B,gBAAiB,kBAAmB,KAAM,KAAM,IAAK,KAAM,KAAM,kBAAmB,YAAa,UAAW,UAAW,MAAO,WAAY,YAAa,MAAO,OAAQ,eAAgB,YAAa,SAAU,cAAe,cAAe,gBAAiB,cAAe,YAAa,mBAAoB,eAAgB,aAAc,eAAgB,cAAe,KAAM,KAAM,KAAM,KAAM,aAAc,WAAY,gBAAiB,oBAAqB,SAAU,OAAQ,KAAM,kBAAmB,KAAM,MAAO,IAAK,KAAM,KAAM,KAAM,KAAM,UAAW,YAAa,aAAc,WAAY,OAAQ,eAAgB,iBAAkB,eAAgB,mBAAoB,iBAAkB,QAAS,aAAc,aAAc,eAAgB,eAAgB,cAAe,cAAe,mBAAoB,YAAa,MAAO,OAAQ,QAAS,SAAU,OAAQ,MAAO,OAAQ,aAAc,SAAU,WAAY,UAAW,QAAS,SAAU,cAAe,SAAU,WAAY,cAAe,OAAQ,aAAc,sBAAuB,mBAAoB,eAAgB,SAAU,gBAAiB,sBAAuB,iBAAkB,IAAK,KAAM,KAAM,SAAU,OAAQ,OAAQ,cAAe,YAAa,UAAW,SAAU,SAAU,QAAS,OAAQ,kBAAmB,mBAAoB,mBAAoB,eAAgB,cAAe,eAAgB,cAAe,aAAc,eAAgB,mBAAoB,oBAAqB,iBAAkB,kBAAmB,oBAAqB,iBAAkB,SAAU,eAAgB,QAAS,eAAgB,iBAAkB,WAAY,UAAW,UAAW,YAAa,mBAAoB,cAAe,kBAAmB,iBAAkB,aAAc,OAAQ,KAAM,KAAM,UAAW,SAAU,UAAW,aAAc,UAAW,aAAc,gBAAiB,gBAAiB,QAAS,eAAgB,OAAQ,eAAgB,mBAAoB,mBAAoB,IAAK,KAAM,KAAM,QAAS,IAAK,KAAM,KAAM,IAAK,YAAY,CAAC,EAC3wEmD,GAASnD,EAAO,CAAC,SAAU,cAAe,QAAS,WAAY,QAAS,eAAgB,cAAe,aAAc,aAAc,QAAS,MAAO,UAAW,eAAgB,WAAY,QAAS,QAAS,SAAU,OAAQ,KAAM,UAAW,SAAU,gBAAiB,SAAU,SAAU,iBAAkB,YAAa,WAAY,cAAe,UAAW,UAAW,gBAAiB,WAAY,WAAY,OAAQ,WAAY,WAAY,aAAc,UAAW,SAAU,SAAU,cAAe,gBAAiB,uBAAwB,YAAa,YAAa,aAAc,WAAY,iBAAkB,iBAAkB,YAAa,UAAW,QAAS,OAAO,CAAC,EAC7pBoD,GAAMpD,EAAO,CAAC,aAAc,SAAU,cAAe,YAAa,aAAa,CAAC,EAEhFqD,GAAgBpD,GAAK,2BAA2B,EAEhDqD,GAAWrD,GAAK,uBAAuB,EACvCsD,GAActD,GAAK,eAAe,EAClCuD,GAAYvD,GAAK,4BAA4B,EAE7CwD,GAAYxD,GAAK,gBAAgB,EAEjCyD,GAAiBzD,GAAK,2FAC5B,EACM0D,GAAoB1D,GAAK,uBAAuB,EAChD2D,GAAkB3D,GAAK,6DAC7B,EACM4D,GAAe5D,GAAK,SAAS,EAEnC,IAAI6D,GAA2B,OAAO,OAAO,CAC3C,UAAW,KACX,cAAeT,GACf,SAAUC,GACV,YAAaC,GACb,UAAWC,GACX,UAAWC,GACX,eAAgBC,GAChB,kBAAmBC,GACnB,gBAAiBC,GACjB,aAAcC,EAChB,CAAC,EAED,MAAME,GAAY,IAAM,OAAO,OAAW,IAAc,KAAO,OAWzDC,GAA4B,SAAmCC,EAAcC,EAAmB,CACpG,GAAI,OAAOD,GAAiB,UAAY,OAAOA,EAAa,cAAiB,WAC3E,OAAO,KAMT,IAAIE,EAAS,KACb,MAAMC,EAAY,wBAEdF,GAAqBA,EAAkB,aAAaE,CAAS,IAC/DD,EAASD,EAAkB,aAAaE,CAAS,GAGnD,MAAMC,EAAa,aAAeF,EAAS,IAAMA,EAAS,IAE1D,GAAI,CACF,OAAOF,EAAa,aAAaI,EAAY,CAC3C,WAAWnB,EAAM,CACf,OAAOA,CACR,EAED,gBAAgBoB,EAAW,CACzB,OAAOA,CACR,CAEP,CAAK,CACF,MAAW,CAIV,eAAQ,KAAK,uBAAyBD,EAAa,wBAAwB,EACpE,IACR,CACH,EAEA,SAASE,IAAkB,CACzB,IAAIC,EAAS,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAIT,GAAS,EAE1F,MAAMU,EAAYC,GAAQH,GAAgBG,CAAI,EAe9C,GARAD,EAAU,QAAU,QAMpBA,EAAU,QAAU,GAEhB,CAACD,GAAU,CAACA,EAAO,UAAYA,EAAO,SAAS,WAAa,EAG9D,OAAAC,EAAU,YAAc,GACjBA,EAGT,MAAME,EAAmBH,EAAO,SAC1BI,EAAgBD,EAAiB,cACvC,GAAI,CACF,SAAAE,CACD,EAAGL,EACJ,KAAM,CACJ,iBAAAM,EACA,oBAAAC,EACA,KAAAC,EACA,QAAAC,EACA,WAAAC,EACA,aAAAC,EAAeX,EAAO,cAAgBA,EAAO,gBAC7C,gBAAAY,EACA,UAAAC,EACA,aAAApB,CACD,EAAGO,EACEc,EAAmBL,EAAQ,UAC3BM,EAAYhD,GAAa+C,EAAkB,WAAW,EACtDE,EAAiBjD,GAAa+C,EAAkB,aAAa,EAC7DG,EAAgBlD,GAAa+C,EAAkB,YAAY,EAC3DI,EAAgBnD,GAAa+C,EAAkB,YAAY,EAOjE,GAAI,OAAOP,GAAwB,WAAY,CAC7C,MAAMY,EAAWd,EAAS,cAAc,UAAU,EAE9Cc,EAAS,SAAWA,EAAS,QAAQ,gBACvCd,EAAWc,EAAS,QAAQ,cAE/B,CAED,IAAIC,EACAC,EAAY,GAChB,KAAM,CACJ,eAAAC,EACA,mBAAAC,GACA,uBAAAC,GACA,qBAAAC,EACD,EAAGpB,EACE,CACJ,WAAAqB,CACD,EAAGvB,EACJ,IAAIwB,EAAQ,GAKZ1B,EAAU,YAAc,OAAO9E,IAAY,YAAc,OAAO+F,GAAkB,YAAcI,GAAkBA,EAAe,qBAAuB,OACxJ,KAAM,CACJ,cAAAzC,EACA,SAAAC,EACA,YAAAC,GACA,UAAAC,GACA,UAAAC,GACA,kBAAAE,GACA,gBAAAC,EACD,EAAGE,GACJ,GAAI,CACF,eAAgBsC,EACjB,EAAGtC,GAQAuC,EAAe,KACnB,MAAMC,GAAuBzE,EAAS,GAAI,CAAC,GAAGc,GAAQ,GAAGC,GAAO,GAAGC,GAAY,GAAGE,GAAU,GAAGE,EAAI,CAAC,EAGpG,IAAIsD,EAAe,KACnB,MAAMC,EAAuB3E,EAAS,CAAE,EAAE,CAAC,GAAGqB,GAAM,GAAG3K,GAAK,GAAG4K,GAAQ,GAAGC,EAAG,CAAC,EAQ9E,IAAIqD,EAA0B,OAAO,KAAK,OAAO,OAAO,KAAM,CAC5D,aAAc,CACZ,SAAU,GACV,aAAc,GACd,WAAY,GACZ,MAAO,IACR,EACD,mBAAoB,CAClB,SAAU,GACV,aAAc,GACd,WAAY,GACZ,MAAO,IACR,EACD,+BAAgC,CAC9B,SAAU,GACV,aAAc,GACd,WAAY,GACZ,MAAO,EACR,CACF,EAAC,EAGEC,GAAc,KAGdC,EAAc,KAGdC,GAAkB,GAGlBC,GAAkB,GAGlBC,GAA0B,GAI1BC,GAA2B,GAK3BC,GAAqB,GAGrBC,GAAiB,GAGjBC,GAAa,GAIbC,GAAa,GAMbC,GAAa,GAIbC,GAAsB,GAItBC,GAAsB,GAKtBC,GAAe,GAefC,GAAuB,GAC3B,MAAMC,EAA8B,gBAGpC,IAAIC,EAAe,GAIfC,EAAW,GAGXC,EAAe,GAGfC,GAAkB,KACtB,MAAMC,GAA0BjG,EAAS,CAAE,EAAE,CAAC,iBAAkB,QAAS,WAAY,OAAQ,gBAAiB,OAAQ,SAAU,OAAQ,KAAM,KAAM,KAAM,KAAM,QAAS,UAAW,WAAY,WAAY,YAAa,SAAU,QAAS,MAAO,WAAY,QAAS,QAAS,QAAS,KAAK,CAAC,EAGhS,IAAIkG,GAAgB,KACpB,MAAMC,GAAwBnG,EAAS,CAAE,EAAE,CAAC,QAAS,QAAS,MAAO,SAAU,QAAS,OAAO,CAAC,EAGhG,IAAIoG,GAAsB,KAC1B,MAAMC,GAA8BrG,EAAS,GAAI,CAAC,MAAO,QAAS,MAAO,KAAM,QAAS,OAAQ,UAAW,cAAe,OAAQ,UAAW,QAAS,QAAS,QAAS,OAAO,CAAC,EAC1KsG,GAAmB,qCACnBC,GAAgB,6BAChBC,GAAiB,+BAGvB,IAAIC,GAAYD,GACZE,GAAiB,GAGjBC,GAAqB,KACzB,MAAMC,GAA6B5G,EAAS,GAAI,CAACsG,GAAkBC,GAAeC,EAAc,EAAGtH,EAAc,EAGjH,IAAI2H,GACJ,MAAMC,GAA+B,CAAC,wBAAyB,WAAW,EACpEC,GAA4B,YAClC,IAAI7G,EAGA8G,GAAS,KAKb,MAAMC,GAAcjE,EAAS,cAAc,MAAM,EAE3CkE,GAAoB,SAA2BC,EAAW,CAC9D,OAAOA,aAAqB,QAAUA,aAAqB,QAC/D,EASQC,GAAe,SAAsBC,EAAK,CAC9C,GAAI,EAAAL,IAAUA,KAAWK,GAuKzB,KAjKI,CAACA,GAAO,OAAOA,GAAQ,YACzBA,EAAM,IAKRA,EAAM/G,GAAM+G,CAAG,EACfR,GACAC,GAA6B,QAAQO,EAAI,iBAAiB,IAAM,GAAKR,GAAoBE,GAA4BF,GAAoBQ,EAAI,kBAE7InH,EAAoB2G,KAAsB,wBAA0B3H,GAAiBD,GAGrFuF,EAAe,iBAAkB6C,EAAMrH,EAAS,GAAIqH,EAAI,aAAcnH,CAAiB,EAAIuE,GAC3FC,EAAe,iBAAkB2C,EAAMrH,EAAS,GAAIqH,EAAI,aAAcnH,CAAiB,EAAIyE,EAC3FgC,GAAqB,uBAAwBU,EAAMrH,EAAS,GAAIqH,EAAI,mBAAoBnI,EAAc,EAAI0H,GAC1GR,GAAsB,sBAAuBiB,EAAMrH,EAASM,GAAM+F,EAA2B,EAC7FgB,EAAI,kBACJnH,CACC,EACCmG,GACFH,GAAgB,sBAAuBmB,EAAMrH,EAASM,GAAM6F,EAAqB,EACjFkB,EAAI,kBACJnH,CACC,EACCiG,GACFH,GAAkB,oBAAqBqB,EAAMrH,EAAS,GAAIqH,EAAI,gBAAiBnH,CAAiB,EAAI+F,GACpGpB,GAAc,gBAAiBwC,EAAMrH,EAAS,GAAIqH,EAAI,YAAanH,CAAiB,EAAI,GACxF4E,EAAc,gBAAiBuC,EAAMrH,EAAS,GAAIqH,EAAI,YAAanH,CAAiB,EAAI,GACxF6F,EAAe,iBAAkBsB,EAAMA,EAAI,aAAe,GAC1DtC,GAAkBsC,EAAI,kBAAoB,GAE1CrC,GAAkBqC,EAAI,kBAAoB,GAE1CpC,GAA0BoC,EAAI,yBAA2B,GAEzDnC,GAA2BmC,EAAI,2BAA6B,GAE5DlC,GAAqBkC,EAAI,oBAAsB,GAE/CjC,GAAiBiC,EAAI,gBAAkB,GAEvC9B,GAAa8B,EAAI,YAAc,GAE/B7B,GAAsB6B,EAAI,qBAAuB,GAEjD5B,GAAsB4B,EAAI,qBAAuB,GAEjD/B,GAAa+B,EAAI,YAAc,GAE/B3B,GAAe2B,EAAI,eAAiB,GAEpC1B,GAAuB0B,EAAI,sBAAwB,GAEnDxB,EAAewB,EAAI,eAAiB,GAEpCvB,EAAWuB,EAAI,UAAY,GAE3B9C,GAAmB8C,EAAI,oBAAsBxF,GAC7C4E,GAAYY,EAAI,WAAab,GAC7B5B,EAA0ByC,EAAI,yBAA2B,GAErDA,EAAI,yBAA2BH,GAAkBG,EAAI,wBAAwB,YAAY,IAC3FzC,EAAwB,aAAeyC,EAAI,wBAAwB,cAGjEA,EAAI,yBAA2BH,GAAkBG,EAAI,wBAAwB,kBAAkB,IACjGzC,EAAwB,mBAAqByC,EAAI,wBAAwB,oBAGvEA,EAAI,yBAA2B,OAAOA,EAAI,wBAAwB,gCAAmC,YACvGzC,EAAwB,+BAAiCyC,EAAI,wBAAwB,gCAGnFlC,KACFH,GAAkB,IAGhBQ,KACFD,GAAa,IAKXQ,IACFvB,EAAexE,EAAS,GAAI,CAAC,GAAGoB,EAAI,CAAC,EACrCsD,EAAe,GAEXqB,EAAa,OAAS,KACxB/F,EAASwE,EAAc1D,EAAM,EAC7Bd,EAAS0E,EAAcrD,EAAI,GAGzB0E,EAAa,MAAQ,KACvB/F,EAASwE,EAAczD,EAAK,EAC5Bf,EAAS0E,EAAchO,EAAG,EAC1BsJ,EAAS0E,EAAcnD,EAAG,GAGxBwE,EAAa,aAAe,KAC9B/F,EAASwE,EAAcxD,EAAU,EACjChB,EAAS0E,EAAchO,EAAG,EAC1BsJ,EAAS0E,EAAcnD,EAAG,GAGxBwE,EAAa,SAAW,KAC1B/F,EAASwE,EAActD,EAAQ,EAC/BlB,EAAS0E,EAAcpD,EAAM,EAC7BtB,EAAS0E,EAAcnD,EAAG,IAM1B8F,EAAI,WACF7C,IAAiBC,KACnBD,EAAelE,GAAMkE,CAAY,GAGnCxE,EAASwE,EAAc6C,EAAI,SAAUnH,CAAiB,GAGpDmH,EAAI,WACF3C,IAAiBC,IACnBD,EAAepE,GAAMoE,CAAY,GAGnC1E,EAAS0E,EAAc2C,EAAI,SAAUnH,CAAiB,GAGpDmH,EAAI,mBACNrH,EAASoG,GAAqBiB,EAAI,kBAAmBnH,CAAiB,EAGpEmH,EAAI,kBACFrB,KAAoBC,KACtBD,GAAkB1F,GAAM0F,EAAe,GAGzChG,EAASgG,GAAiBqB,EAAI,gBAAiBnH,CAAiB,GAK9D2F,IACFrB,EAAa,OAAO,EAAI,IAKtBY,IACFpF,EAASwE,EAAc,CAAC,OAAQ,OAAQ,MAAM,CAAC,EAK7CA,EAAa,QACfxE,EAASwE,EAAc,CAAC,OAAO,CAAC,EAChC,OAAOK,GAAY,OAGjBwC,EAAI,qBAAsB,CAC5B,GAAI,OAAOA,EAAI,qBAAqB,YAAe,WACjD,MAAM7H,GAAgB,6EAA6E,EAGrG,GAAI,OAAO6H,EAAI,qBAAqB,iBAAoB,WACtD,MAAM7H,GAAgB,kFAAkF,EAI1GuE,EAAqBsD,EAAI,qBAEzBrD,EAAYD,EAAmB,WAAW,EAAE,CAClD,MAEUA,IAAuB,SACzBA,EAAqB5B,GAA0BC,EAAcW,CAAa,GAIxEgB,IAAuB,MAAQ,OAAOC,GAAc,WACtDA,EAAYD,EAAmB,WAAW,EAAE,GAM5C5F,GACFA,EAAOkJ,CAAG,EAGZL,GAASK,EACb,EAEQC,GAAiCtH,EAAS,GAAI,CAAC,KAAM,KAAM,KAAM,KAAM,OAAO,CAAC,EAC/EuH,GAA0BvH,EAAS,GAAI,CAAC,gBAAiB,OAAQ,QAAS,gBAAgB,CAAC,EAK3FwH,GAA+BxH,EAAS,GAAI,CAAC,QAAS,QAAS,OAAQ,IAAK,QAAQ,CAAC,EAKrFyH,GAAezH,EAAS,CAAE,EAAEe,EAAK,EACvCf,EAASyH,GAAczG,EAAU,EACjChB,EAASyH,GAAcxG,EAAa,EACpC,MAAMyG,GAAkB1H,EAAS,CAAE,EAAEkB,EAAQ,EAC7ClB,EAAS0H,GAAiBvG,EAAgB,EAU1C,MAAMwG,GAAuB,SAA8BvH,EAAS,CAClE,IAAIwH,EAAS/D,EAAczD,CAAO,GAG9B,CAACwH,GAAU,CAACA,EAAO,WACrBA,EAAS,CACP,aAAcnB,GACd,QAAS,UACjB,GAGI,MAAMoB,EAAU5I,GAAkBmB,EAAQ,OAAO,EAC3C0H,EAAgB7I,GAAkB2I,EAAO,OAAO,EAEtD,OAAKjB,GAAmBvG,EAAQ,YAAY,EAIxCA,EAAQ,eAAiBmG,GAIvBqB,EAAO,eAAiBpB,GACnBqB,IAAY,MAMjBD,EAAO,eAAiBtB,GACnBuB,IAAY,QAAUC,IAAkB,kBAAoBR,GAA+BQ,CAAa,GAK1G,EAAQL,GAAaI,CAAO,EAGjCzH,EAAQ,eAAiBkG,GAIvBsB,EAAO,eAAiBpB,GACnBqB,IAAY,OAKjBD,EAAO,eAAiBrB,GACnBsB,IAAY,QAAUN,GAAwBO,CAAa,EAK7D,EAAQJ,GAAgBG,CAAO,EAGpCzH,EAAQ,eAAiBoG,GAIvBoB,EAAO,eAAiBrB,IAAiB,CAACgB,GAAwBO,CAAa,GAI/EF,EAAO,eAAiBtB,IAAoB,CAACgB,GAA+BQ,CAAa,EACpF,GAKF,CAACJ,GAAgBG,CAAO,IAAML,GAA6BK,CAAO,GAAK,CAACJ,GAAaI,CAAO,GAIjG,GAAAhB,KAAsB,yBAA2BF,GAAmBvG,EAAQ,YAAY,GA5DnF,EAqEb,EAQQ2H,GAAe,SAAsBC,EAAM,CAC/ChJ,GAAU4D,EAAU,QAAS,CAC3B,QAASoF,CACf,CAAK,EAED,GAAI,CAEFA,EAAK,WAAW,YAAYA,CAAI,CACjC,MAAW,CACVA,EAAK,OAAM,CACZ,CACL,EASQC,GAAmB,SAA0BC,EAAMF,EAAM,CAC7D,GAAI,CACFhJ,GAAU4D,EAAU,QAAS,CAC3B,UAAWoF,EAAK,iBAAiBE,CAAI,EACrC,KAAMF,CACd,CAAO,CACF,MAAW,CACVhJ,GAAU4D,EAAU,QAAS,CAC3B,UAAW,KACX,KAAMoF,CACd,CAAO,CACF,CAID,GAFAA,EAAK,gBAAgBE,CAAI,EAErBA,IAAS,MAAQ,CAACxD,EAAawD,CAAI,EACrC,GAAI3C,IAAcC,GAChB,GAAI,CACFuC,GAAaC,CAAI,CAC3B,MAAoB,CAAE,KAEd,IAAI,CACFA,EAAK,aAAaE,EAAM,EAAE,CACpC,MAAoB,CAAE,CAGtB,EASQC,GAAgB,SAAuBrV,EAAO,CAElD,IAAIsV,EACAC,EAEJ,GAAI/C,GACFxS,EAAQ,oBAAsBA,MACzB,CAEL,MAAMwV,GAAUnJ,GAAYrM,EAAO,aAAa,EAChDuV,EAAoBC,IAAWA,GAAQ,CAAC,CACzC,CAEGzB,KAAsB,yBAA2BJ,KAAcD,KAEjE1T,EAAQ,iEAAmEA,EAAQ,kBAGrF,MAAMyV,EAAexE,EAAqBA,EAAmB,WAAWjR,CAAK,EAAIA,EAMjF,GAAI2T,KAAcD,GAChB,GAAI,CACF4B,EAAM,IAAI5E,EAAW,EAAC,gBAAgB+E,EAAc1B,EAAiB,CAC7E,MAAkB,CAAE,CAKhB,GAAI,CAACuB,GAAO,CAACA,EAAI,gBAAiB,CAChCA,EAAMnE,EAAe,eAAewC,GAAW,WAAY,IAAI,EAE/D,GAAI,CACF2B,EAAI,gBAAgB,UAAY1B,GAAiB1C,EAAYuE,CAC9D,MAAW,CACX,CACF,CAED,MAAMC,EAAOJ,EAAI,MAAQA,EAAI,gBAQ7B,OANItV,GAASuV,GACXG,EAAK,aAAaxF,EAAS,eAAeqF,CAAiB,EAAGG,EAAK,WAAW,CAAC,GAAK,IAAI,EAKtF/B,KAAcD,GACTpC,GAAqB,KAAKgE,EAAKhD,GAAiB,OAAS,MAAM,EAAE,CAAC,EAGpEA,GAAiBgD,EAAI,gBAAkBI,CAClD,EASQC,GAAkB,SAAyB5F,EAAM,CACrD,OAAOqB,GAAmB,KAAKrB,EAAK,eAAiBA,EAAMA,EAC3DQ,EAAW,aAAeA,EAAW,aAAeA,EAAW,UAAW,KAAM,EAAK,CACzF,EASQqF,GAAe,SAAsBC,EAAK,CAC9C,OAAOA,aAAepF,IAAoB,OAAOoF,EAAI,UAAa,UAAY,OAAOA,EAAI,aAAgB,UAAY,OAAOA,EAAI,aAAgB,YAAc,EAAEA,EAAI,sBAAsBrF,IAAiB,OAAOqF,EAAI,iBAAoB,YAAc,OAAOA,EAAI,cAAiB,YAAc,OAAOA,EAAI,cAAiB,UAAY,OAAOA,EAAI,cAAiB,YAAc,OAAOA,EAAI,eAAkB,WACrZ,EASQC,GAAU,SAAiBrI,EAAQ,CACvC,OAAO,OAAO4C,GAAS,SAAW5C,aAAkB4C,EAAO5C,GAAU,OAAOA,GAAW,UAAY,OAAOA,EAAO,UAAa,UAAY,OAAOA,EAAO,UAAa,QACzK,EAWQsI,GAAe,SAAsBC,EAAYC,EAAaC,EAAM,CACnE1E,EAAMwE,CAAU,GAIrBjK,GAAayF,EAAMwE,CAAU,EAAGG,GAAQ,CACtCA,EAAK,KAAKrG,EAAWmG,EAAaC,EAAMhC,EAAM,CACpD,CAAK,CACL,EAaQkC,GAAoB,SAA2BH,EAAa,CAChE,IAAII,EAOJ,GAJAN,GAAa,yBAA0BE,EAAa,IAAI,EAIpDL,GAAaK,CAAW,EAC1B,OAAAhB,GAAagB,CAAW,EAEjB,GAKT,MAAMlB,EAAU3H,EAAkB6I,EAAY,QAAQ,EAUtD,GAPAF,GAAa,sBAAuBE,EAAa,CAC/C,QAAAlB,EACA,YAAarD,CACnB,CAAK,EAIGuE,EAAY,iBAAmB,CAACH,GAAQG,EAAY,iBAAiB,IAAM,CAACH,GAAQG,EAAY,OAAO,GAAK,CAACH,GAAQG,EAAY,QAAQ,iBAAiB,IAAMxJ,GAAW,UAAWwJ,EAAY,SAAS,GAAKxJ,GAAW,UAAWwJ,EAAY,WAAW,EAC/P,OAAAhB,GAAagB,CAAW,EAEjB,GAKT,GAAI,CAACvE,EAAaqD,CAAO,GAAKhD,GAAYgD,CAAO,EAAG,CAElD,GAAI,CAAChD,GAAYgD,CAAO,GAAKuB,GAAwBvB,CAAO,IACtDjD,EAAwB,wBAAwB,QAAUrF,GAAWqF,EAAwB,aAAciD,CAAO,GAClHjD,EAAwB,wBAAwB,UAAYA,EAAwB,aAAaiD,CAAO,GAAG,MAAO,GAKxH,GAAIhC,GAAgB,CAACG,GAAgB6B,CAAO,EAAG,CAC7C,MAAMwB,EAAaxF,EAAckF,CAAW,GAAKA,EAAY,WACvDO,EAAa1F,EAAcmF,CAAW,GAAKA,EAAY,WAE7D,GAAIO,GAAcD,EAAY,CAC5B,MAAME,GAAaD,EAAW,OAE9B,QAASlV,EAAImV,GAAa,EAAGnV,GAAK,EAAG,EAAEA,EACrCiV,EAAW,aAAa3F,EAAU4F,EAAWlV,CAAC,EAAG,EAAI,EAAGuP,EAAeoF,CAAW,CAAC,CAEtF,CACF,CAED,OAAAhB,GAAagB,CAAW,EAEjB,EACR,CAYD,OARIA,aAAuB3F,GAAW,CAACuE,GAAqBoB,CAAW,IAQlElB,IAAY,YAAcA,IAAY,YAActI,GAAW,uBAAwBwJ,EAAY,SAAS,GAC/GhB,GAAagB,CAAW,EAEjB,KAKL5D,IAAsB4D,EAAY,WAAa,IAEjDI,EAAUJ,EAAY,YACtBI,EAAU/J,GAAc+J,EAAS3H,EAAe,GAAG,EACnD2H,EAAU/J,GAAc+J,EAAS1H,EAAU,GAAG,EAC9C0H,EAAU/J,GAAc+J,EAASzH,GAAa,GAAG,EAE7CqH,EAAY,cAAgBI,IAC9BnK,GAAU4D,EAAU,QAAS,CAC3B,QAASmG,EAAY,UAAW,CAC1C,CAAS,EACDA,EAAY,YAAcI,IAM9BN,GAAa,wBAAyBE,EAAa,IAAI,EAEhD,GACX,EAYQS,GAAoB,SAA2BC,EAAOC,EAAQrU,EAAO,CAEzE,GAAIqQ,KAAiBgE,IAAW,MAAQA,IAAW,UAAYrU,KAAS2N,GAAY3N,KAAS4R,IAC3F,MAAO,GAQT,GAAI,EAAAjC,IAAmB,CAACF,EAAY4E,CAAM,GAAKnK,GAAWoC,GAAW+H,CAAM,IAAU,GAAI,EAAA3E,IAAmBxF,GAAWqC,GAAW8H,CAAM,IAAU,GAAI,CAAChF,EAAagF,CAAM,GAAK5E,EAAY4E,CAAM,GAC/L,GAGA,EAAAN,GAAwBK,CAAK,IAAM7E,EAAwB,wBAAwB,QAAUrF,GAAWqF,EAAwB,aAAc6E,CAAK,GAAK7E,EAAwB,wBAAwB,UAAYA,EAAwB,aAAa6E,CAAK,KAAO7E,EAAwB,8BAA8B,QAAUrF,GAAWqF,EAAwB,mBAAoB8E,CAAM,GAAK9E,EAAwB,8BAA8B,UAAYA,EAAwB,mBAAmB8E,CAAM,IAE1fA,IAAW,MAAQ9E,EAAwB,iCAAmCA,EAAwB,wBAAwB,QAAUrF,GAAWqF,EAAwB,aAAcvP,CAAK,GAAKuP,EAAwB,wBAAwB,UAAYA,EAAwB,aAAavP,CAAK,IACvS,MAAO,WAIA,CAAA+Q,GAAoBsD,CAAM,GAAU,GAAI,CAAAnK,GAAWgF,GAAkBnF,GAAc/J,EAAO0M,GAAiB,EAAE,CAAC,GAAU,GAAK,GAAA2H,IAAW,OAASA,IAAW,cAAgBA,IAAW,SAAWD,IAAU,UAAYpK,GAAchK,EAAO,OAAO,IAAM,GAAK6Q,GAAcuD,CAAK,IAAU,GAAI,EAAAxE,IAA2B,CAAC1F,GAAWuC,GAAmB1C,GAAc/J,EAAO0M,GAAiB,EAAE,CAAC,IAAU,GAAI1M,EAC1Z,MAAO,QAGT,MAAO,EACX,EASQ+T,GAA0B,SAAiCvB,EAAS,CACxE,OAAOA,EAAQ,QAAQ,GAAG,EAAI,CAClC,EAaQ8B,GAAsB,SAA6BZ,EAAa,CACpE,IAAI3N,EACA/F,EACAqU,EACAE,EAGJf,GAAa,2BAA4BE,EAAa,IAAI,EAE1D,KAAM,CACJ,WAAAc,EACD,EAAGd,EAGJ,GAAI,CAACc,GACH,OAGF,MAAMC,EAAY,CAChB,SAAU,GACV,UAAW,GACX,SAAU,GACV,kBAAmBpF,CACzB,EAII,IAHAkF,EAAIC,GAAW,OAGRD,KAAK,CACVxO,EAAOyO,GAAWD,CAAC,EACnB,KAAM,CACJ,KAAA1B,GACA,aAAA6B,EACD,EAAG3O,EAyBJ,GAxBA/F,EAAQ6S,KAAS,QAAU9M,EAAK,MAAQkE,GAAWlE,EAAK,KAAK,EAC7DsO,EAASxJ,EAAkBgI,EAAI,EAG/B4B,EAAU,SAAWJ,EACrBI,EAAU,UAAYzU,EACtByU,EAAU,SAAW,GACrBA,EAAU,cAAgB,OAE1BjB,GAAa,wBAAyBE,EAAae,CAAS,EAE5DzU,EAAQyU,EAAU,UAGdA,EAAU,gBAMd7B,GAAiBC,GAAMa,CAAW,EAI9B,CAACe,EAAU,UACb,SAKF,GAAI,CAAC5E,IAA4B3F,GAAW,OAAQlK,CAAK,EAAG,CAC1D4S,GAAiBC,GAAMa,CAAW,EAElC,QACD,CAIG5D,KACF9P,EAAQ+J,GAAc/J,EAAOmM,EAAe,GAAG,EAC/CnM,EAAQ+J,GAAc/J,EAAOoM,EAAU,GAAG,EAC1CpM,EAAQ+J,GAAc/J,EAAOqM,GAAa,GAAG,GAK/C,MAAM+H,GAAQvJ,EAAkB6I,EAAY,QAAQ,EAEpD,GAAKS,GAAkBC,GAAOC,EAAQrU,CAAK,EAkB3C,IAVIsQ,KAAyB+D,IAAW,MAAQA,IAAW,UAEzDzB,GAAiBC,GAAMa,CAAW,EAGlC1T,EAAQuQ,EAA8BvQ,GAKpC0O,GAAsB,OAAO3B,GAAiB,UAAY,OAAOA,EAAa,kBAAqB,YACjG,CAAA2H,GACF,OAAQ3H,EAAa,iBAAiBqH,GAAOC,CAAM,EAAC,CAClD,IAAK,cACH,CACErU,EAAQ0O,EAAmB,WAAW1O,CAAK,EAC3C,KACD,CAEH,IAAK,mBACH,CACEA,EAAQ0O,EAAmB,gBAAgB1O,CAAK,EAChD,KACD,CACJ,CAML,GAAI,CACE0U,GACFhB,EAAY,eAAegB,GAAc7B,GAAM7S,CAAK,EAGpD0T,EAAY,aAAab,GAAM7S,CAAK,EAGtC0J,GAAS6D,EAAU,OAAO,CAClC,MAAkB,CAAE,EACf,CAIDiG,GAAa,0BAA2BE,EAAa,IAAI,CAC7D,EAQQiB,GAAqB,SAASA,EAAmBC,EAAU,CAC/D,IAAIC,EAEJ,MAAMC,EAAiB1B,GAAgBwB,CAAQ,EAM/C,IAFApB,GAAa,0BAA2BoB,EAAU,IAAI,EAE/CC,EAAaC,EAAe,YAEjCtB,GAAa,yBAA0BqB,EAAY,IAAI,EAInD,CAAAhB,GAAkBgB,CAAU,IAM5BA,EAAW,mBAAmBjH,GAChC+G,EAAmBE,EAAW,OAAO,EAKvCP,GAAoBO,CAAU,GAKhCrB,GAAa,yBAA0BoB,EAAU,IAAI,CACzD,EAWE,OAAArH,EAAU,SAAW,SAAU9P,EAAO,CACpC,IAAIuU,EAAM,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAC1EmB,EACA4B,EACArB,EACAsB,EAaJ,GARA3D,GAAiB,CAAC5T,EAEd4T,KACF5T,EAAQ,SAKN,OAAOA,GAAU,UAAY,CAAC8V,GAAQ9V,CAAK,EAC7C,GAAI,OAAOA,EAAM,UAAa,YAG5B,GAFAA,EAAQA,EAAM,WAEV,OAAOA,GAAU,SACnB,MAAM0M,GAAgB,iCAAiC,MAGzD,OAAMA,GAAgB,4BAA4B,EAMtD,GAAI,CAACoD,EAAU,YACb,OAAO9P,EAkBT,GAbKuS,IACH+B,GAAaC,CAAG,EAKlBzE,EAAU,QAAU,GAGhB,OAAO9P,GAAU,WACnBgT,EAAW,IAGTA,GAEF,GAAIhT,EAAM,SAAU,CAClB,MAAM+U,GAAU3H,EAAkBpN,EAAM,QAAQ,EAEhD,GAAI,CAAC0R,EAAaqD,EAAO,GAAKhD,GAAYgD,EAAO,EAC/C,MAAMrI,GAAgB,yDAAyD,CAElF,UACQ1M,aAAiBqQ,EAG1BqF,EAAOL,GAAc,SAAS,EAC9BiC,EAAe5B,EAAK,cAAc,WAAW1V,EAAO,EAAI,EAEpDsX,EAAa,WAAa,GAAKA,EAAa,WAAa,QAGlDA,EAAa,WAAa,OADnC5B,EAAO4B,EAKP5B,EAAK,YAAY4B,CAAY,MAE1B,CAEL,GAAI,CAAC7E,IAAc,CAACJ,IAAsB,CAACC,IAC3CtS,EAAM,QAAQ,GAAG,IAAM,GACrB,OAAOiR,GAAsB0B,GAAsB1B,EAAmB,WAAWjR,CAAK,EAAIA,EAQ5F,GAHA0V,EAAOL,GAAcrV,CAAK,EAGtB,CAAC0V,EACH,OAAOjD,GAAa,KAAOE,GAAsBzB,EAAY,EAEhE,CAIGwE,GAAQlD,IACVyC,GAAaS,EAAK,UAAU,EAK9B,MAAM8B,GAAe7B,GAAgB3C,EAAWhT,EAAQ0V,CAAI,EAI5D,KAAOO,EAAcuB,GAAa,YAE5BpB,GAAkBH,CAAW,IAM7BA,EAAY,mBAAmB9F,GACjC+G,GAAmBjB,EAAY,OAAO,EAKxCY,GAAoBZ,CAAW,GAKjC,GAAIjD,EACF,OAAOhT,EAKT,GAAIyS,GAAY,CACd,GAAIC,GAGF,IAFA6E,EAAalG,GAAuB,KAAKqE,EAAK,aAAa,EAEpDA,EAAK,YAEV6B,EAAW,YAAY7B,EAAK,UAAU,OAGxC6B,EAAa7B,EAGf,OAAI9D,EAAa,YAAcA,EAAa,iBAQ1C2F,EAAahG,EAAW,KAAKvB,EAAkBuH,EAAY,EAAI,GAG1DA,CACR,CAED,IAAIE,EAAiBnF,GAAiBoD,EAAK,UAAYA,EAAK,UAG5D,OAAIpD,IAAkBZ,EAAa,UAAU,GAAKgE,EAAK,eAAiBA,EAAK,cAAc,SAAWA,EAAK,cAAc,QAAQ,MAAQjJ,GAAWyC,GAAcwG,EAAK,cAAc,QAAQ,IAAI,IAC/L+B,EAAiB,aAAe/B,EAAK,cAAc,QAAQ,KAAO;AAAA,EAAQ+B,GAKxEpF,KACFoF,EAAiBnL,GAAcmL,EAAgB/I,EAAe,GAAG,EACjE+I,EAAiBnL,GAAcmL,EAAgB9I,EAAU,GAAG,EAC5D8I,EAAiBnL,GAAcmL,EAAgB7I,GAAa,GAAG,GAG1DqC,GAAsB0B,GAAsB1B,EAAmB,WAAWwG,CAAc,EAAIA,CACvG,EASE3H,EAAU,UAAY,SAAUyE,EAAK,CACnCD,GAAaC,CAAG,EAEhBhC,GAAa,EACjB,EAQEzC,EAAU,YAAc,UAAY,CAClCoE,GAAS,KACT3B,GAAa,EACjB,EAaEzC,EAAU,iBAAmB,SAAU4H,EAAKpP,EAAM/F,EAAO,CAElD2R,IACHI,GAAa,CAAE,GAGjB,MAAMqC,EAAQvJ,EAAkBsK,CAAG,EAC7Bd,EAASxJ,EAAkB9E,CAAI,EACrC,OAAOoO,GAAkBC,EAAOC,EAAQrU,CAAK,CACjD,EAUEuN,EAAU,QAAU,SAAUkG,EAAY2B,EAAc,CAClD,OAAOA,GAAiB,aAI5BnG,EAAMwE,CAAU,EAAIxE,EAAMwE,CAAU,GAAK,GACzC9J,GAAUsF,EAAMwE,CAAU,EAAG2B,CAAY,EAC7C,EAWE7H,EAAU,WAAa,SAAUkG,EAAY,CAC3C,GAAIxE,EAAMwE,CAAU,EAClB,OAAO/J,GAASuF,EAAMwE,CAAU,CAAC,CAEvC,EASElG,EAAU,YAAc,SAAUkG,EAAY,CACxCxE,EAAMwE,CAAU,IAClBxE,EAAMwE,CAAU,EAAI,GAE1B,EAQElG,EAAU,eAAiB,UAAY,CACrC0B,EAAQ,EACZ,EAES1B,CACT,CAEG,IAAC8H,GAAShI,GAAe,ECrlD5B,iBAAAiI,kBAAuC,kCAGjCC,GAA2B,2BAE1B,SAASC,GAAsBC,EAAgC,CACrEH,GAAWC,GAA0BE,CAAW,CACjD,CAEO,SAASC,IAAiD,CAChE,OAAOC,GAAWJ,EAAwB,CAC3C,CCVA,IAAIK,GAAe,IAAM,CACvB,MAAMC,EAAkB,SAAS,cAAc,MAAM,EACrDA,EAAgB,KAAO,0JACvBA,EAAgB,IAAM,aACtB,MAAMC,EAAW,SAAS,cAAc,MAAM,EAC9CA,EAAS,KAAO,uFAChBA,EAAS,IAAM,aACf,SAAS,KAAK,YAAYD,CAAe,EACzC,SAAS,KAAK,YAAYC,CAAQ,CACpC,EAGIC,GAAM,IAAM,CACd,MAAMxP,EAAM,SAAS,cAAc,KAAK,EACxC,OAAAA,EAAI,MAAM,gBAAkB,0CAC5BA,EAAI,MAAM,OAAS,oBACnBA,EAAI,MAAM,aAAe,UACzBA,EAAI,MAAM,UAAY,8BACtBA,EAAI,MAAM,MAAQ,UAClBA,EAAI,MAAM,QAAU,OACpBA,EAAI,MAAM,cAAgB,MAC1BA,EAAI,MAAM,WAAa,SACvBA,EAAI,MAAM,OAAS,OACnBA,EAAI,MAAM,eAAiB,gBAC3BA,EAAI,MAAM,SAAW,SACrBA,EAAI,MAAM,SAAW,QACrBA,EAAI,MAAM,MAAQ,SAClBA,EAAI,MAAM,IAAM,SAChBA,EAAI,MAAM,MAAQ,OAClBA,EAAI,MAAM,OAAS,KACnBA,EAAI,MAAM,YAAc,OACxBA,EAAI,aAAa,KAAM,0BAA0B,EACjD,OAAO,WAAW,oBAAoB,EAAE,iBAAiB,SAAW,GAAM,CACpE,EAAE,QACJA,EAAI,MAAM,QAAU,OAEpBA,EAAI,MAAM,QAAU,MAE1B,CAAG,EACMA,CACT,EAGIyP,GAAgB,IAAM,CACxB,MAAMC,EAAQ,SAAS,gBAAgB,6BAA8B,KAAK,EAC1EA,EAAM,aAAa,QAAS,4BAA4B,EACxDA,EAAM,aAAa,aAAc,8BAA8B,EAC/DA,EAAM,aAAa,cAAe,MAAM,EACxCA,EAAM,aAAa,YAAa,OAAO,EACvCA,EAAM,aAAa,OAAQ,KAAK,EAChCA,EAAM,aAAa,QAAS,KAAK,EACjCA,EAAM,aAAa,SAAU,KAAK,EAClCA,EAAM,aAAa,sBAAuB,eAAe,EACzDA,EAAM,aAAa,UAAW,WAAW,EACzCA,EAAM,aAAa,OAAQ,cAAc,EACzC,MAAMC,EAAO,SAAS,gBAAgB,6BAA8B,MAAM,EAC1E,OAAAA,EAAK,aACH,IACA,gwCACJ,EACED,EAAM,YAAYC,CAAI,EACfD,CACT,EAGIE,GAAW,CAAC5X,EAAO9B,IAAa,CAClC,MAAM8J,EAAM,SAAS,cAAc,KAAK,EACxC,OAAAA,EAAI,aAAa,KAAM,wBAAwB,EAC/CA,EAAI,MAAM,QAAU,OACpBA,EAAI,MAAM,cAAgB,MAC1BA,EAAI,MAAM,WAAa,SACvBA,EAAI,MAAM,eAAiB,SAC3BA,EAAI,MAAM,SAAW,OACrBA,EAAI,MAAM,YAAc,OACxBA,EAAI,MAAM,aAAe,OACzBA,EAAI,MAAM,OAAS,OACnBA,EAAI,MAAM,OAAS,UACnBA,EAAI,MAAM,MAAQ,UAClBA,EAAI,MAAM,mBAAqB,OAC/BA,EAAI,MAAM,mBAAqB,MAC/BA,EAAI,MAAM,yBAA2B,cACrCA,EAAI,YAAYyP,GAAa,CAAE,EAC/BzP,EAAI,iBAAiB,QAAU6P,GAAM,CACnCA,EAAE,eAAc,EAChBA,EAAE,gBAAe,EACjB3Z,GACJ,CAAG,EACD8J,EAAI,iBAAiB,aAAc,IAAM,CACvCA,EAAI,MAAM,MAAQ,SACtB,CAAG,EACDA,EAAI,iBAAiB,aAAc,IAAM,CACvCA,EAAI,MAAM,MAAQ,SACtB,CAAG,EACMA,CACT,EAGI8P,GAASC,GAAU,CACrB,MAAMvK,EAAO,SAAS,cAAc,GAAG,EACvC,OAAAA,EAAK,MAAM,OAAS,IACpBA,EAAK,MAAM,QAAU,IACrBA,EAAK,MAAM,MAAQ,UACnBA,EAAK,MAAM,SAAW,OACtBA,EAAK,MAAM,WAAa,8BACxBA,EAAK,MAAM,QAAU,UACrBA,EAAK,MAAM,WAAa,oBACxBA,EAAK,MAAM,WAAa,MACxBA,EAAK,aAAeuK,GAAwB,GAAG,WACxCvK,CACT,EAGIwK,GAAQ,IAAM,CAChB,MAAMC,EAAQ,SAAS,gBAAgB,6BAA8B,KAAK,EAC1EA,EAAM,aAAa,QAAS,4BAA4B,EACxDA,EAAM,aAAa,aAAc,8BAA8B,EAC/DA,EAAM,aAAa,cAAe,MAAM,EACxCA,EAAM,aAAa,YAAa,OAAO,EACvCA,EAAM,aAAa,OAAQ,KAAK,EAChCA,EAAM,aAAa,QAAS,KAAK,EACjCA,EAAM,aAAa,SAAU,KAAK,EAClCA,EAAM,aAAa,sBAAuB,eAAe,EACzDA,EAAM,aAAa,UAAW,WAAW,EACzCA,EAAM,aAAa,OAAQ,SAAS,EACpC,MAAMN,EAAO,SAAS,gBAAgB,6BAA8B,MAAM,EAC1E,OAAAA,EAAK,aACH,IACA,kUACJ,EACEM,EAAM,YAAYN,CAAI,EACfM,CACT,EAGIC,GAAQlY,GAAU,CACpB,MAAMgI,EAAM,SAAS,cAAc,GAAG,EACtC,OAAAA,EAAI,aAAa,OAAQ,iCAAiChI,EAAM,EAAE,EAAE,EACpEgI,EAAI,aAAa,MAAO,qBAAqB,EAC7CA,EAAI,aAAa,SAAU,QAAQ,EACnCA,EAAI,MAAM,OAAS,oBACnBA,EAAI,MAAM,aAAe,MACzBA,EAAI,MAAM,QAAU,OACpBA,EAAI,MAAM,cAAgB,MAC1BA,EAAI,MAAM,WAAa,SACvBA,EAAI,MAAM,OAAS,aACnBA,EAAI,MAAM,SAAW,OACrBA,EAAI,MAAM,YAAc,MACxBA,EAAI,MAAM,eAAiB,OAC3BA,EAAI,YAAYgQ,GAAK,CAAE,EACvBhQ,EAAI,YAAY8P,GAAM9X,EAAM,KAAK,CAAC,EAC3BgI,CACT,EAGImQ,GAAUC,GAAa,CACzB,MAAM5L,EAAU,SAAS,cAAc,KAAK,EAC5C,OAAAA,EAAQ,IAAM,oCAAoC4L,CAAQ,UAC1D5L,EAAQ,MAAM,MAAQ,WACtBA,EAAQ,MAAM,OAAS,WACvBA,EAAQ,MAAM,aAAe,MAC7BA,EAAQ,MAAM,KAAO,OACrBA,EAAQ,MAAM,YAAc,WACrBA,CACT,EAGI6L,GAAaC,GAAO,CACtB,KAAM,CAAChX,EAAGiX,CAAS,EAAID,EAAG,MAAM,GAAG,EAC7B9L,EAAU,SAAS,cAAc,GAAG,EAC1C,OAAAA,EAAQ,aAAa,OAAQ,iCAAiC8L,CAAE,EAAE,EAClE9L,EAAQ,aAAa,MAAO,qBAAqB,EACjDA,EAAQ,aAAa,SAAU,QAAQ,EACvCA,EAAQ,MAAM,MAAQ,UACtBA,EAAQ,MAAM,eAAiB,OAC/BA,EAAQ,MAAM,WAAa,MAC3BA,EAAQ,MAAM,SAAW,OACzBA,EAAQ,MAAM,WAAa,OAC3BA,EAAQ,MAAM,KAAO,OACrBA,EAAQ,MAAM,WAAa,4BAC3BA,EAAQ,iBAAiB,YAAa,IAAM,CAC1CA,EAAQ,MAAM,MAAQ,SAC1B,CAAG,EACDA,EAAQ,iBAAiB,WAAY,IAAM,CACzCA,EAAQ,MAAM,MAAQ,SAC1B,CAAG,EACDA,EAAQ,YAAc+L,EACf/L,CACT,EAGIgM,GAAa,IAAM,CACrB,MAAMC,EAAa,SAAS,cAAc,KAAK,EAC/C,OAAAA,EAAW,MAAM,WAAa,UAC9BA,EAAW,MAAM,YAAc,UAC/BA,EAAW,MAAM,MAAQ,UACzBA,EAAW,YAAc,IAClBA,CACT,EAGIC,GAAYN,GAAa,CAC3B,MAAM5L,EAAU,SAAS,cAAc,GAAG,EAC1C,OAAAA,EAAQ,aAAa,OAAQ,0BAA0B4L,CAAQ,EAAE,EACjE5L,EAAQ,aAAa,MAAO,qBAAqB,EACjDA,EAAQ,aAAa,SAAU,QAAQ,EACvCA,EAAQ,MAAM,MAAQ,qBACtBA,EAAQ,MAAM,eAAiB,OAC/BA,EAAQ,MAAM,WAAa,MAC3BA,EAAQ,MAAM,SAAW,OACzBA,EAAQ,MAAM,WAAa,OAC3BA,EAAQ,MAAM,KAAO,OACrBA,EAAQ,MAAM,WAAa,8BAC3BA,EAAQ,iBAAiB,YAAa,IAAM,CAC1CA,EAAQ,MAAM,MAAQ,SAC1B,CAAG,EACDA,EAAQ,iBAAiB,WAAY,IAAM,CACzCA,EAAQ,MAAM,MAAQ,oBAC1B,CAAG,EACDA,EAAQ,YAAc4L,EACf5L,CACT,EAGImM,GAAW3Y,GAAU,CACvB,MAAMuV,EAAU,SAAS,cAAc,KAAK,EAC5C,OAAAA,EAAQ,MAAM,QAAU,OACxBA,EAAQ,MAAM,cAAgB,MAC9BA,EAAQ,MAAM,WAAa,SAC3BA,EAAQ,MAAM,eAAiB,SAC/BA,EAAQ,MAAM,YAAc,oBAC5BA,EAAQ,MAAM,aAAe,OAC7BA,EAAQ,MAAM,OAAS,OACvBA,EAAQ,YAAY4C,GAAOnY,EAAM,MAAM,CAAC,EACxCuV,EAAQ,YAAYmD,GAAS1Y,EAAM,MAAM,CAAC,EAC1CuV,EAAQ,YAAYiD,GAAU,CAAE,EAChCjD,EAAQ,YAAY8C,GAAUrY,EAAM,EAAE,CAAC,EACvCuV,EAAQ,YAAY2C,GAAKlY,CAAK,CAAC,EACxBuV,CACT,EAGI9K,GAAUzK,GAAU,CACtB,MAAMgI,EAAMwP,KACNoB,EAAiB,IAAM5Q,EAAI,MAAM,QAAU,OACjD,OAAAA,EAAI,YAAY2Q,GAAQ3Y,CAAK,CAAC,EAC9BgI,EAAI,YAAY4P,GAAS5X,EAAO4Y,CAAc,CAAC,EACxC5Q,CACT,EAGI6Q,GAAY,MAAOC,GAAa,CAClC,GAAI,CAGF,OADa,MADI,MAAM,MAAM,qCAAqCA,CAAQ,EAAE,GAChD,MAE7B,MAAe,CACd,OAAO,IACR,CACH,EAGIC,GAAS,CAACvM,EAASwM,IAAY,CAQjC,GAAI,SAAS,OAAS,KACpB,OAAO,QAAQ,MAAM,uBAAuB,EAE9C,SAAS,KAAK,YAAYxM,CAAO,CACnC,EAGA,eAAeyM,GAAKC,EAAcF,EAAS,CACzC,IAAIG,EAAIC,EACR,GAAI,SAAW,OAAQ,OAAO,QAAQ,MAAM,iDAAiD,EAS7F,GARiC,OAAO,QACrCA,GAAMD,EAAK,OAAO,WAAa,KAAO,OAASA,EAAG,kBAAoB,KAAOC,EAAK,CACjF,EAAG,OAAO,SAAS,QACpB,CACL,EAAI,KAAMC,GAAW,CACjB,IAAIC,EACJ,QAASA,EAAM,IAAI,IAAID,CAAM,IAAM,KAAO,OAASC,EAAI,UAAY,wBACvE,CAAG,EAC6B,OAC9BjC,KACA,IAAIrX,EACJ,GAAI,OAAOkZ,GAAiB,UAE1B,GADAlZ,EAAQ,MAAM6Y,GAAUK,CAAY,EAChClZ,IAAU,KAAM,OAAO,QAAQ,MAAM,iBAAiB,OAE1DA,EAAQkZ,EAEV,MAAMK,EAAsB9O,GAAOzK,CAAK,EACxC,OAAA+Y,GAAOQ,CAA4B,EAC5B,CACL,QAASA,CACb,CACA,CACA,IAAIC,GAAO,CAACxZ,EAAOgZ,IAAYC,GAAKjZ,CAAc,4bC1OxC,SAAA0C,GAAS,sBAAA8C,GAAuB,UAAAD,EAAA,SAAyB,oFAuXrDlH,EAAQ,UACXA,EAAa,UACd,kBACS,gBACJ,iBACC,2BAEPA,EAAE,kLAPGA,EAAQ,0BACXA,EAAa,8DAMfA,EAAE,wdAWHE,GAAAC,EAAAwL,EAAAtL,CAAA,wCAPCmI,EAAAxI,QAAoB,OAAKiJ,GAAA,2GAS1B/I,GAAAC,EAAAC,EAAAC,CAAA,qEAkBC6H,EAAAlI,MAAG,4BAA4B,yEAAKE,GAAAC,EAAAwL,EAAAtL,CAAA,kBAApCQ,EAAA,YAAAqH,OAAAlI,MAAG,4BAA4B,OAAAc,GAAAsa,EAAAlT,CAAA,4EAbjC,SACM,iBAOP,iCACgC,OAC/B,oBACF,EATuCiB,GAAAkS,EAAA,OAAAC,EAAA,iCAAAtb,+BAA8BA,EAAkB,IAAC,MACrFA,EAAQ,6BACOA,EAAkB,IAAC,YAClCA,OAAQ,OACR,SAAS,2EAKTE,GAAAC,EAAAwL,EAAAtL,CAAA,UAVKC,GASNqL,EAAA0P,CAAA,0BARqCxa,EAAA,SAAAya,OAAA,iCAAAtb,+BAA8BA,EAAkB,IAAC,MACrFA,EAAQ,6BACOA,EAAkB,IAAC,YAClCA,OAAQ,OACR,SAAS,sEARDA,EAAM,KAAE,SAAW,IAAE,uBAC3B,OAAAA,EAAO,aAAW,eAAiBA,EAAO,aAAW,WAAaA,MAAO,oBAAmByI,0LAgB9FvI,GAAAC,EAAAC,EAAAC,CAAA,EAjBJC,GAA8CF,EAAAuL,CAAA,EAA3CrL,GAAwCqL,EAAA4P,CAAA,yDAA/Bvb,EAAM,KAAE,SAAW,IAAE,KAAAc,GAAAsH,EAAAD,CAAA,2IA8B9BnI,EAAM,kBACIA,EAAQ,IAAIA,EAAM,IAAC,yBACrBA,EAAiB,uCAErBA,EAAO,oCAKDA,EAAQ,4OADAA,EAAc,iCAAdA,EAAc,6PARhCA,EAAM,8BACIA,EAAQ,IAAIA,EAAM,IAAC,sCACrBA,EAAiB,0DAErBA,EAAO,qDAKDA,EAAQ,8MADAA,EAAc,iKAhBtB,aAAAA,MAAO,aACf,KAAAA,MAAO,cACHA,EAAK,qFAFDa,EAAA,UAAA2a,EAAA,aAAAxb,MAAO,cACfa,EAAA,UAAA2a,EAAA,KAAAxb,MAAO,4BACHA,EAAK,2JAjDXA,EAAa,MAAK,WAAaA,EAAa,MAAK,UAAO,EAAOA,EAAM,KAAIA,EAAM,KAAE,gBAAayb,GAAAzb,CAAA,8CA6C/FA,EAAM,KAAE,eAAiBA,EAAK,MAOzBA,EAAM,KAAIA,EAAM,KAAIA,EAAS,sJApDjCA,EAAa,MAAK,WAAaA,EAAa,MAAK,UAAO,EAAOA,EAAM,KAAIA,EAAM,KAAE,icAV9E,QAAAA,MAAaA,EAAQ,iBAEtB,OAAAA,MAASA,EAAI,+CAIb,OAAAA,QAAkB,sBACdA,EAAM,KAAE,YAAc,sMAPzBa,EAAA,QAAA6a,EAAA,QAAA1b,MAAaA,EAAQ,+BAEtBa,EAAA,SAAA6a,EAAA,OAAA1b,MAASA,EAAI,sFAIba,EAAA,WAAA6a,EAAA,OAAA1b,QAAkB,qCACdA,EAAM,KAAE,YAAc,yMAlZ9B,IAAAia,eAEK0B,IAAyB,CAI3BC,QAAerY,GAAQ,IAEvBsY,MAAU,IAEVC,EAAQ,IAAO,qBAA6BjQ,GAAA,CACjDA,EAAQ,QAAckQ,GAAA,CACjB,GAAAA,EAAM,eAAc,CACnB,IAAAC,EAA0BH,EAAI,IAAIE,EAAM,MAAwB,EAChEC,IAAQ,QACXJ,EAAa,OAAQK,IAAY,IAAAA,EAAI,CAAAD,CAAa,EAAG,EAAI,SAKpD,SAAAE,EAASC,EAAa3S,EAAkB,CAC5CqS,EAAA,IAAIrS,EAAI2S,CAAG,EACfL,EAAS,QAAQtS,CAAE,SAGX,SAAA0S,EAAU,UAAWN,EAAa,WAGtC,MAAAA,GAAeD,GAAyB,EA+F/B,eAAAS,GACdC,EAA0B,IAEtBA,EAAW,CACR,MAAAC,MAAa,UACbC,EAAmB,MAAM,KAC9BD,EAAO,gBAAgBD,EAAa,WAAW,EAAE,KAAK,QAAQ,KAG3DE,EACM,QAAAC,KAAgBD,EAAgB,CACpC,IAAAE,EAAa,SAAS,cAAcD,EAAa,OAAO,KAC5D,MAAM,KAAKA,EAAa,UAAU,EAAE,QAASrT,GAAI,CAChDsT,EAAW,aAAatT,EAAK,KAAMA,EAAK,KAAK,IAE9CsT,EAAW,YAAcD,EAAa,YAGrCC,EAAW,SAAW,QACtBA,EAAW,aAAa,UAAU,GAK5B,MAAAC,EAHc,MAAM,KACzB,SAAS,KAAK,qBAAqB,MAAM,OAEd,KAAQlT,GAElCA,EAAG,aAAa,UAAU,GACzBiT,EAAW,aAAa,UAAU,GAClC,CAAAjT,EAAG,YAAYiT,CAAU,MAGxBC,EAAO,CACD,cAAK,aAAaD,EAAYC,CAAO,YAKvC,cAAK,YAAYD,CAAU,2EArH/BE,KAEH,MAAA/S,EAAWzC,SAEN,WAAAyD,CAAmB,EAAAtJ,GACnB,QAAAC,CAAe,EAAAD,GACf,eAAAE,CAAsB,EAAAF,GACtB,SAAAsb,CAAiB,EAAAtb,GACjB,SAAAI,CAAiB,EAAAJ,EACjB,YAAAub,EAA+B,QAAQ,EAAAvb,GACvC,mBAAAwb,CAA2B,EAAAxb,GAC3B,UAAAyb,CAAkB,EAAAzb,GAClB,KAAAO,CAAa,EAAAP,GACb,MAAA0b,CAAc,EAAA1b,EACrB2b,EAGO,WAAAC,EAAsCC,EAAiB,EAAA7b,GACvD,OAAA8b,CAAyB,EAAA9b,EACzB,cAAA+b,EAAwC,QAAS/b,EACxD+b,IACHzE,GAAsByE,CAAY,EAErBA,EAAA,iBAAiB,kBAAyBC,GAAA,CACtD9R,EAAA,GAAAhB,GAAgB8S,EAAsB,OAAS,KAAK,SAI3C,MAAA3b,CAAoB,EAAAL,GACpB,KAAAic,CAAmB,EAAAjc,GACnB,IAAAkc,EAAkB,EAAAlc,EAEzB6a,GAAMlC,KAENwD,GACH,UAEGpc,EACAqc,EAAQ,GACRC,EAAkB,GAClBC,EACApT,GAAeqT,EAAG,gBAAgB,EAAI,MACtCC,GACAC,GAMAC,GAA+C,KACpC,eAAAC,GAAiBC,EAAyB,CACpDA,IACHF,GAAsBG,GACrBD,EACA3c,EACAyc,IAAuB,MAAS,GAG5B,MAAAd,EACLU,EAAO,KAAO,gBAAkBA,EAAO,WACvC,SAAS,IAAI,EAETA,EAAO,mBAEN,QAAQ,IACbA,EAAO,YAAY,IAAeQ,GAEhCA,EAAW,WAAW,OAAO,GAAKA,EAAW,WAAW,QAAQ,EAEzDlB,EAAUkB,EAAY,SAAS,IAAI,EAGpC,MAAMR,EAAO,KAAO,IAAMQ,CAAU,EACzC,KAAmBC,KAAS,KAC5B,QAAMH,GAAU,CAChBC,GAAWD,EAAY3c,CAAO,MAgD1B,SAAA+c,GAAkBne,EAAsB,CAC1C,MAAAoe,EAAc,OAAO,kBAAoB,UAE3C,IAAAC,KACAD,EACcC,EAAA,aAGX,MAAAC,OADU,IAAI,OAAO,SAAS,UAAQ,EACC,aAAa,IACzD,SAAS,EAEVD,EAAiB3B,GAAc4B,IAAkB,SAG9C,OAAAD,IAAmB,QAAUA,IAAmB,QACnDE,GAAYve,EAAQqe,CAAc,EAElCA,EAAiBG,EAAkBxe,CAAM,EAEnCqe,EAGC,SAAAG,EAAkBxe,EAAsB,CAC1C,MAAAye,EAAQC,IACd,QACG,WAAW,8BAA8B,GACzC,iBAAiB,SAAUA,CAAa,WAElCA,GAAa,CACjB,IAAAC,EAA2B,QAAQ,aACtC,8BAA8B,EAC7B,QACC,OACA,QAEH,OAAAJ,GAAYve,EAAQ2e,CAAM,EACnBA,EAED,OAAAF,EAGC,SAAAF,GAAYve,EAAwBye,EAAuB,OAC7DG,EAAqBrd,EAAWvB,EAAO,cAAiB,SAAS,KACjE6e,EAAatd,EAAWvB,EAASA,EAAO,cAC9C6e,EAAW,MAAM,WAAa,8BAC1BJ,IAAU,OACMG,EAAA,UAAU,IAAI,MAAM,EAEpBA,EAAA,UAAU,OAAO,MAAM,MAIxC9U,EAAM,CACT,QAAS,GACT,YAAa,UACb,OAAQ,WACR,OAAQ,YAGLgV,EACAC,EAAY,GACP,SAAAC,GAAcC,EAAoB,CAC1C5T,EAAA,GAAAvB,EAASmV,CAAO,QAGXC,EAAkB,OAAO,eAE/Bhb,GAAO,eACNyZ,GAAoBQ,GAAkBjd,CAAO,SAGvCie,EAAc,OAAO,wBAetB,GAbLvB,GACyBsB,IAAoB,MAElC,2BAAAC,GAAgB,SAAWA,EAAc,IACjD,GACC/B,GAAQ5b,GAAS6b,IAAO,SAAS,OAErChS,EAAA,GAAAyT,EAAY,MAAA7B,EAAO,QAAQW,GAAO,CACjC,gBAAiBoB,GACjB,gBAAiB,GACjB,QAAS,OAAQ,MAAO,SAAU,QAAQ,KAGtC,CAAAF,EAAI,OACE,gBAAM,8BAA8B,OAG/CrB,EAASqB,EAAI,MAAM,EACnB,OAAO,iBAAmBrB,EAAO,cAEjC3T,EAAM,CACL,QAAS,GACT,YAAa,WACb,OAAQ,UACR,OAAQ,YAGH,MAAAgU,GAAiBL,EAAO,GAAG,EAC3B,MAAAxB,GAAqBwB,EAAO,IAAI,EACtCpS,EAAA,GAAA0T,EAAY,EAAI,EAChB,OAAO,aAAetB,EAAO,SAE7BhU,EAAS,QAAQ,EAEbgU,EAAO,UACV,sBACS,KAAAL,GAAI,IAAS,IAAIQ,EAAO,MAC5BwB,EAAG,IAAO,IAAG,UAAWhC,CAAI,eAC1BN,EAAA,IAAO,YAAYsC,CAAG,EACrBtC,EAAA,iBAAiB,cAAgBzD,GAAC,CACxCgG,GAAe,sBAAuB,OAAO,EAE7C,QAAQ,MAAM,KAAK,MAAMhG,EAAE,IAAI,KAEzByD,EAAA,iBAAiB,eAAiBK,GAAK,CAQxC,GAPL2B,EAAI,MAAK,EACTzT,EAAA,GAAAyT,EAAY,MAAA7B,EAAO,QAAQW,GAAO,CACjC,gBAAiBoB,GACjB,gBAAiB,GACjB,QAAS,OAAQ,MAAO,SAAU,QAAQ,KAGtC,CAAAF,EAAI,OACE,gBAAM,8BAA8B,OAG/CrB,EAASqB,EAAI,MAAM,EACnB,OAAO,iBAAmBrB,EAAO,SAC3B,MAAAK,GAAiBL,EAAO,GAAG,KAEhC,OAaD,IAAA6B,GAEAC,kBAEWC,IAAU,MACxBF,IAAM,MAAAG,GAAA,WAAiB,sBAAqB,gEAAG,OAAO,iBAExCC,IAAS,MACvBH,IAAK,MAAAE,GAAA,WAAiB,qBAAoB,kFAAG,OAAO,WAG5CE,IAAS,CACblC,EAAO,mBACI+B,WAWVI,GAAkB,CACvB,eAAc,CACb,YAAalC,EAAG,oBAAoB,EACpC,aAAcA,EAAG,qBAAqB,EACtC,YAAaA,EAAG,oBAAoB,EACpC,cAAeA,EAAG,sBAAsB,EACxC,OAAQA,EAAG,qBAAqB,GAEjC,MAAMmC,EAAkB,CAChB,0BAAmBnC,EAAG,0BAA0B,IAExD,YAAYmC,EAAoBC,EAAY,QACpC,mBAAkB;AAAA;AAAA;AAAA;AAAA,oEAEvB,KAAK,eAAeD,CAAK,GAAK,UAC/B;AAAA;AAAA,6FAAmGC,CAAI;AAAA;AAAA,YAKtG,IAAAT,GAEJnb,GAAO,UACOuX,GAAA,SAASO,GAAK9a,CAAO,IAc/B,IAAA6e,GAEW,eAAAC,GACd1F,EACA/Y,EAAiB,IAEb+Y,GAAQ,CAAK/Y,GAAY,OAAO,OAAS,OAAO,IAAG,CAClDwe,KACHA,GAAY,OAAM,EACJA,GAAA,QAET,MAAAE,EAAM,MAASjF,GAAKV,CAAQ,EAC9B2F,IAAQF,GAAcE,EAAO,UAInClZ,GAAS,KACRgZ,IAAa,OAAM,yEA8EkBV,GAAApc,8oBA5X/Bwa,GAAQ,QACLA,EAAO,2BAsNhBpS,EAAA,GAAEiS,GACD,CAAAC,GAASzT,EAAO,cAAgB,QAC9B,UACC,CAAAyT,GAASzT,EAAO,cAAgB,QAChC,QACAA,EAAO,WAAW,0CAEpB2T,IAAWZ,GAASqD,EAAclE,EAAG,IAAM2D,GAAS,sBAoDhDnC,GACNtc,EAAQ,cACH,gBAAY,UACf,QAAS,GACT,WAAY,GACZ,SAAU,2BAKV4d,GAAK,QAAUkB,GAAmBlB,GAAK,QAAQ,SAAUvd,CAAQ", "names": ["space_logo", "is_client", "now", "raf", "cb", "noop", "tasks", "run_tasks", "task", "loop", "callback", "fulfill", "t2_value", "ctx", "t7_value", "insert", "target", "div", "anchor", "append", "span0", "a0", "span1", "a1", "span3", "a2", "dirty", "set_data", "t2", "t7", "create_if_block", "toggle_class", "div1", "div0", "wrapper", "$$props", "version", "initial_height", "fill_width", "is_embed", "space", "display", "info", "loaded", "$$value", "pretty_si", "num", "units", "i", "unit", "is_date", "obj", "tick_spring", "last_value", "current_value", "target_value", "delta", "velocity", "spring", "damper", "acceleration", "d", "_", "next_value", "k", "value", "opts", "store", "writable", "stiffness", "damping", "precision", "last_time", "current_token", "inv_mass", "inv_mass_recovery_rate", "cancel_task", "set", "new_value", "token", "fulfil", "fn", "onMount", "set_style", "g0", "g1", "svg", "path0", "path1", "path2", "path3", "path4", "path5", "path6", "path7", "margin", "top", "bottom", "dismounted", "animate", "run", "loading", "span", "button", "Icon", "label", "show_label", "pending", "size", "padded", "highlight", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "color", "transparent", "background", "offset", "_color", "g", "ordered_colors", "color_values", "tw_colors", "colors", "acc", "primary", "secondary", "tick", "onDestroy", "createEventDispatcher", "t1_value", "Clear", "iconbutton_changes", "current", "t1", "if_block0", "create_if_block_16", "create_if_block_11", "create_if_block_14", "create_if_block_15", "create_if_block_10", "create_if_block_1", "style_transform", "create_if_block_13", "t_value", "t0_value", "t0", "create_if_block_12", "loader_changes", "style_width", "if_block", "create_if_block_3", "div3", "div2", "create_if_block_8", "create_if_block_7", "create_if_block_6", "if_block3", "create_if_block_5", "create_if_block_4", "p_1", "attr", "div_class_value", "items", "called", "scroll_into_view", "el", "enable", "min", "box", "dispatch", "i18n", "eta", "queue_position", "queue_size", "status", "scroll_to_output", "timer", "show_progress", "message", "progress", "variant", "loading_text", "absolute", "translucent", "border", "autoscroll", "_timer", "timer_start", "timer_diff", "old_eta", "eta_from_start", "eta_level", "progress_level", "last_progress_level", "progress_bar", "show_eta_bar", "start_timer", "$$invalidate", "formatted_eta", "stop_timer", "p", "formatted_timer", "entries", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "freeze", "seal", "create", "apply", "construct", "fun", "thisValue", "args", "x", "Func", "arrayForEach", "unapply", "arrayPop", "arrayPush", "stringToLowerCase", "stringToString", "stringMatch", "stringReplace", "stringIndexOf", "stringTrim", "regExpTest", "typeErrorCreate", "unconstruct", "func", "thisArg", "_len", "_key", "_len2", "_key2", "addToSet", "array", "transformCaseFunc", "_transformCaseFunc", "element", "lcElement", "clone", "object", "newObject", "property", "lookupGetter", "prop", "desc", "fallback<PERSON><PERSON><PERSON>", "html$1", "svg$1", "svgFilters", "svgDisallowed", "mathMl$1", "mathMlDisallowed", "text", "html", "mathMl", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "EXPRESSIONS", "getGlobal", "_createTrustedTypesPolicy", "trustedTypes", "purifyHostElement", "suffix", "ATTR_NAME", "policyName", "scriptUrl", "createDOMPurify", "window", "DOMPurify", "root", "originalDocument", "currentScript", "document", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "getNextSibling", "getChildNodes", "getParentNode", "template", "trustedTypesPolicy", "emptyHTML", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "hooks", "IS_ALLOWED_URI$1", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "CUSTOM_ELEMENT_HANDLING", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "_parseConfig", "cfg", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "parentTagName", "_forceRemove", "node", "_removeAttribute", "name", "_initDocument", "doc", "leadingWhitespace", "matches", "dirtyPayload", "body", "_createIterator", "_isClobbered", "elm", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "content", "_basicCustomElementTest", "parentNode", "childNodes", "childCount", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "l", "attributes", "hookEvent", "namespaceURI", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "importedNode", "returnNode", "nodeIterator", "serializedHTML", "tag", "hookFunction", "purify", "setContext", "WORKER_PROXY_CONTEXT_KEY", "setWorkerProxyContext", "workerProxy", "getWorkerProxyContext", "getContext", "inject_fonts", "source_sans_pro", "ibm_mono", "Box", "ArrowCollapse", "arrow", "path", "Collapse", "e", "Count", "count", "Heart", "heart", "Like", "Avatar", "username", "Namespace", "id", "spaceName", "Separation", "separation", "Username", "Content", "handleCollapse", "get_space", "space_id", "inject", "options", "main", "initialSpace", "_a", "_b", "origin", "_a2", "mini_header_element", "init", "t", "a", "a_href_value", "strong", "login_changes", "create_if_block_2", "embed_changes", "create_intersection_store", "intersecting", "els", "observer", "entry", "_el", "s", "register", "_id", "add_custom_html_head", "head_string", "parser", "parsed_head_html", "head_element", "newElement", "matched", "setupi18n", "app_mode", "theme_mode", "control_page_title", "container", "eager", "stream", "mount_css", "default_mount_css", "Client", "worker_proxy", "event", "host", "src", "loader_status", "ready", "render_complete", "config", "$_", "active_theme_mode", "api_url", "css_text_stylesheet", "mount_custom_css", "css_string", "prefix_css", "stylesheet", "response", "handle_theme_mode", "force_light", "new_theme_mode", "url_color_mode", "apply_theme", "sync_system_theme", "theme", "update_scheme", "_theme", "dark_class_element", "bg_element", "app", "css_ready", "handle_status", "_status", "gradio_dev_mode", "server_port", "url", "new_message_fn", "Blocks", "<PERSON><PERSON>", "get_blocks", "__vitePreload", "get_login", "load_demo", "discussion_message", "error", "site", "spaceheader", "mount_space_header", "header", "$intersecting"], "ignoreList": [1, 2, 5, 6, 12, 14], "sources": ["../../../../js/core/src/images/spaces.svg", "../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/environment.js", "../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/loop.js", "../../../../js/core/src/Embed.svelte", "../../../../js/statustracker/static/utils.ts", "../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/motion/utils.js", "../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/motion/spring.js", "../../../../js/statustracker/static/Loader.svelte", "../../../../js/atoms/src/IconButton.svelte", "../../../../js/icons/src/Clear.svelte", "../../../../js/theme/src/colors.ts", "../../../../js/statustracker/static/index.svelte", "../../../../node_modules/.pnpm/dompurify@3.0.3/node_modules/dompurify/dist/purify.es.js", "../../../../js/wasm/svelte/context.ts", "../../../../node_modules/.pnpm/@huggingface+space-header@1.0.3/node_modules/@huggingface/space-header/dist/browser/index.mjs", "../../../../js/spa/src/Index.svelte"], "sourcesContent": ["export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='10'%20height='10'%20fill='none'%3e%3cpath%20fill='%23FF3270'%20d='M1.93%206.03v2.04h2.04V6.03H1.93Z'/%3e%3cpath%20fill='%23861FFF'%20d='M6.03%206.03v2.04h2.04V6.03H6.03Z'/%3e%3cpath%20fill='%23097EFF'%20d='M1.93%201.93v2.04h2.04V1.93H1.93Z'/%3e%3cpath%20fill='%23000'%20fill-rule='evenodd'%20d='M.5%201.4c0-.5.4-.9.9-.9h3.1a.9.9%200%200%201%20.87.67A2.44%202.44%200%200%201%209.5%202.95c0%20.65-.25%201.24-.67%***********.67.46.67.88v3.08c0%20.5-.4.91-.9.91H1.4a.9.9%200%200%201-.9-.9V1.4Zm1.43.53v2.04h2.04V1.93H1.93Zm0%206.14V6.03h2.04v2.04H1.93Zm4.1%200V6.03h2.04v2.04H6.03Zm0-5.12a1.02%201.02%200%201%201%202.04%200%201.02%201.02%200%200%201-2.04%200Z'%20clip-rule='evenodd'/%3e%3cpath%20fill='%23FFD702'%20d='M7.05%201.93a1.02%201.02%200%201%200%200%202.04%201.02%201.02%200%200%200%200-2.04Z'/%3e%3c/svg%3e\"", "import { noop } from './utils.js';\n\nexport const is_client = typeof window !== 'undefined';\n\n/** @type {() => number} */\nexport let now = is_client ? () => window.performance.now() : () => Date.now();\n\nexport let raf = is_client ? (cb) => requestAnimationFrame(cb) : noop;\n\n// used internally for testing\n/** @returns {void} */\nexport function set_now(fn) {\n\tnow = fn;\n}\n\n/** @returns {void} */\nexport function set_raf(fn) {\n\traf = fn;\n}\n", "import { raf } from './environment.js';\n\nconst tasks = new Set();\n\n/**\n * @param {number} now\n * @returns {void}\n */\nfunction run_tasks(now) {\n\ttasks.forEach((task) => {\n\t\tif (!task.c(now)) {\n\t\t\ttasks.delete(task);\n\t\t\ttask.f();\n\t\t}\n\t});\n\tif (tasks.size !== 0) raf(run_tasks);\n}\n\n/**\n * For testing purposes only!\n * @returns {void}\n */\nexport function clear_loops() {\n\ttasks.clear();\n}\n\n/**\n * Creates a new task that runs on each raf frame\n * until it returns a falsy value or is aborted\n * @param {import('./private.js').TaskCallback} callback\n * @returns {import('./private.js').Task}\n */\nexport function loop(callback) {\n\t/** @type {import('./private.js').TaskEntry} */\n\tlet task;\n\tif (tasks.size === 0) raf(run_tasks);\n\treturn {\n\t\tpromise: new Promise((fulfill) => {\n\t\t\ttasks.add((task = { c: callback, f: fulfill }));\n\t\t}),\n\t\tabort() {\n\t\t\ttasks.delete(task);\n\t\t}\n\t};\n}\n", "<script lang=\"ts\">\n\timport space_logo from \"./images/spaces.svg\";\n\timport { _ } from \"svelte-i18n\";\n\texport let wrapper: HTMLDivElement;\n\texport let version: string;\n\texport let initial_height: string;\n\texport let fill_width: boolean;\n\texport let is_embed: boolean;\n\n\texport let space: string | null;\n\texport let display: boolean;\n\texport let info: boolean;\n\texport let loaded: boolean;\n</script>\n\n<div\n\tbind:this={wrapper}\n\tclass:app={!display && !is_embed}\n\tclass:fill_width\n\tclass:embed-container={display}\n\tclass:with-info={info}\n\tclass=\"gradio-container gradio-container-{version}\"\n\tstyle:min-height={loaded ? \"initial\" : initial_height}\n\tstyle:flex-grow={!display ? \"1\" : \"auto\"}\n\tdata-iframe-height\n>\n\t<div class=\"main\">\n\t\t<slot />\n\t</div>\n\t{#if display && space && info}\n\t\t<div class=\"info\">\n\t\t\t<span>\n\t\t\t\t<a href=\"https://huggingface.co/spaces/{space}\" class=\"title\">{space}</a\n\t\t\t\t>\n\t\t\t</span>\n\t\t\t<span>\n\t\t\t\t{$_(\"common.built_with\")}\n\t\t\t\t<a class=\"gradio\" href=\"https://gradio.app\">Gradio</a>.\n\t\t\t</span>\n\t\t\t<span>\n\t\t\t\t{$_(\"common.hosted_on\")}\n\t\t\t\t<a class=\"hf\" href=\"https://huggingface.co/spaces\"\n\t\t\t\t\t><span class=\"space-logo\">\n\t\t\t\t\t\t<img src={space_logo} alt=\"Hugging Face Space\" />\n\t\t\t\t\t</span> Spaces</a\n\t\t\t\t>\n\t\t\t</span>\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.gradio-container {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\tpadding: 0;\n\t\tmin-height: 1px;\n\t\toverflow: hidden;\n\t\tcolor: var(--button-secondary-text-color);\n\t}\n\n\t.embed-container {\n\t\tmargin: var(--size-4) 0px;\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t\tborder-radius: var(--embed-radius);\n\t}\n\n\t.with-info {\n\t\tpadding-bottom: var(--size-7);\n\t}\n\n\t.embed-container > .main {\n\t\tpadding: var(--size-4);\n\t}\n\n\t.app > .main {\n\t\tdisplay: flex;\n\t\tflex-grow: 1;\n\t\tflex-direction: column;\n\t}\n\n\t.app {\n\t\tposition: relative;\n\t\tmargin: auto;\n\t\tpadding: var(--size-4) var(--size-8);\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t@media (--screen-sm) {\n\t\t.app:not(.fill_width) {\n\t\t\tmax-width: 640px;\n\t\t}\n\t}\n\t@media (--screen-md) {\n\t\t.app:not(.fill_width) {\n\t\t\tmax-width: 768px;\n\t\t}\n\t}\n\t@media (--screen-lg) {\n\t\t.app:not(.fill_width) {\n\t\t\tmax-width: 1024px;\n\t\t}\n\t}\n\t@media (--screen-xl) {\n\t\t.app:not(.fill_width) {\n\t\t\tmax-width: 1280px;\n\t\t}\n\t}\n\t@media (--screen-xxl) {\n\t\t.app:not(.fill_width) {\n\t\t\tmax-width: 1536px;\n\t\t}\n\t}\n\n\t.info {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tjustify-content: flex-start;\n\t\tborder-top: 1px solid var(--button-secondary-border-color);\n\t\tpadding: var(--size-1) var(--size-5);\n\t\twidth: 100%;\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-md);\n\t\twhite-space: nowrap;\n\t}\n\n\t.info > span {\n\t\tword-wrap: break-word;\n\t\t-break: keep-all;\n\t\tdisplay: block;\n\t\tword-break: keep-all;\n\t}\n\n\t.info > span:nth-child(1) {\n\t\tmargin-right: 4px;\n\t\tmin-width: 0px;\n\t\tmax-width: max-content;\n\t\toverflow: hidden;\n\t\tcolor: var(--body-text-color);\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.info > span:nth-child(2) {\n\t\tmargin-right: 3px;\n\t}\n\n\t.info > span:nth-child(2),\n\t.info > span:nth-child(3) {\n\t\twidth: max-content;\n\t}\n\n\t.info > span:nth-child(3) {\n\t\talign-self: flex-end;\n\t\tjustify-self: flex-end;\n\t\tmargin-left: auto;\n\t\ttext-align: right;\n\t}\n\n\t.info > span:nth-child(1) {\n\t\tflex-shrink: 9;\n\t}\n\n\t.hidden-title {\n\t\tposition: absolute;\n\t\tleft: var(--size-5);\n\t\topacity: 0;\n\t\tbackground: var(--button-secondary-background-fill);\n\t\tpadding-right: 4px;\n\t}\n\n\t.info a {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.title {\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.hf {\n\t\tmargin-left: 5px;\n\t}\n\n\t.space-logo img {\n\t\tdisplay: inline-block;\n\t\tmargin-bottom: 4px;\n\t\theight: 12px;\n\t}\n\n\ta:hover {\n\t\ttext-decoration: underline;\n\t}\n</style>\n", "export function pretty_si(num: number): string {\n\tlet units = [\"\", \"k\", \"M\", \"G\", \"T\", \"P\", \"E\", \"Z\"];\n\tlet i = 0;\n\twhile (num > 1000 && i < units.length - 1) {\n\t\tnum /= 1000;\n\t\ti++;\n\t}\n\tlet unit = units[i];\n\treturn (Number.isInteger(num) ? num : num.toFixed(1)) + unit;\n}\n", "/**\n * @param {any} obj\n * @returns {boolean}\n */\nexport function is_date(obj) {\n\treturn Object.prototype.toString.call(obj) === '[object Date]';\n}\n", "import { writable } from '../store/index.js';\nimport { loop, now } from '../internal/index.js';\nimport { is_date } from './utils.js';\n\n/**\n * @template T\n * @param {import('./private.js').TickContext<T>} ctx\n * @param {T} last_value\n * @param {T} current_value\n * @param {T} target_value\n * @returns {T}\n */\nfunction tick_spring(ctx, last_value, current_value, target_value) {\n\tif (typeof current_value === 'number' || is_date(current_value)) {\n\t\t// @ts-ignore\n\t\tconst delta = target_value - current_value;\n\t\t// @ts-ignore\n\t\tconst velocity = (current_value - last_value) / (ctx.dt || 1 / 60); // guard div by 0\n\t\tconst spring = ctx.opts.stiffness * delta;\n\t\tconst damper = ctx.opts.damping * velocity;\n\t\tconst acceleration = (spring - damper) * ctx.inv_mass;\n\t\tconst d = (velocity + acceleration) * ctx.dt;\n\t\tif (Math.abs(d) < ctx.opts.precision && Math.abs(delta) < ctx.opts.precision) {\n\t\t\treturn target_value; // settled\n\t\t} else {\n\t\t\tctx.settled = false; // signal loop to keep ticking\n\t\t\t// @ts-ignore\n\t\t\treturn is_date(current_value) ? new Date(current_value.getTime() + d) : current_value + d;\n\t\t}\n\t} else if (Array.isArray(current_value)) {\n\t\t// @ts-ignore\n\t\treturn current_value.map((_, i) =>\n\t\t\ttick_spring(ctx, last_value[i], current_value[i], target_value[i])\n\t\t);\n\t} else if (typeof current_value === 'object') {\n\t\tconst next_value = {};\n\t\tfor (const k in current_value) {\n\t\t\t// @ts-ignore\n\t\t\tnext_value[k] = tick_spring(ctx, last_value[k], current_value[k], target_value[k]);\n\t\t}\n\t\t// @ts-ignore\n\t\treturn next_value;\n\t} else {\n\t\tthrow new Error(`Cannot spring ${typeof current_value} values`);\n\t}\n}\n\n/**\n * The spring function in Svelte creates a store whose value is animated, with a motion that simulates the behavior of a spring. This means when the value changes, instead of transitioning at a steady rate, it \"bounces\" like a spring would, depending on the physics parameters provided. This adds a level of realism to the transitions and can enhance the user experience.\n *\n * https://svelte.dev/docs/svelte-motion#spring\n * @template [T=any]\n * @param {T} [value]\n * @param {import('./private.js').SpringOpts} [opts]\n * @returns {import('./public.js').Spring<T>}\n */\nexport function spring(value, opts = {}) {\n\tconst store = writable(value);\n\tconst { stiffness = 0.15, damping = 0.8, precision = 0.01 } = opts;\n\t/** @type {number} */\n\tlet last_time;\n\t/** @type {import('../internal/private.js').Task} */\n\tlet task;\n\t/** @type {object} */\n\tlet current_token;\n\t/** @type {T} */\n\tlet last_value = value;\n\t/** @type {T} */\n\tlet target_value = value;\n\tlet inv_mass = 1;\n\tlet inv_mass_recovery_rate = 0;\n\tlet cancel_task = false;\n\t/**\n\t * @param {T} new_value\n\t * @param {import('./private.js').SpringUpdateOpts} opts\n\t * @returns {Promise<void>}\n\t */\n\tfunction set(new_value, opts = {}) {\n\t\ttarget_value = new_value;\n\t\tconst token = (current_token = {});\n\t\tif (value == null || opts.hard || (spring.stiffness >= 1 && spring.damping >= 1)) {\n\t\t\tcancel_task = true; // cancel any running animation\n\t\t\tlast_time = now();\n\t\t\tlast_value = new_value;\n\t\t\tstore.set((value = target_value));\n\t\t\treturn Promise.resolve();\n\t\t} else if (opts.soft) {\n\t\t\tconst rate = opts.soft === true ? 0.5 : +opts.soft;\n\t\t\tinv_mass_recovery_rate = 1 / (rate * 60);\n\t\t\tinv_mass = 0; // infinite mass, unaffected by spring forces\n\t\t}\n\t\tif (!task) {\n\t\t\tlast_time = now();\n\t\t\tcancel_task = false;\n\t\t\ttask = loop((now) => {\n\t\t\t\tif (cancel_task) {\n\t\t\t\t\tcancel_task = false;\n\t\t\t\t\ttask = null;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tinv_mass = Math.min(inv_mass + inv_mass_recovery_rate, 1);\n\t\t\t\tconst ctx = {\n\t\t\t\t\tinv_mass,\n\t\t\t\t\topts: spring,\n\t\t\t\t\tsettled: true,\n\t\t\t\t\tdt: ((now - last_time) * 60) / 1000\n\t\t\t\t};\n\t\t\t\tconst next_value = tick_spring(ctx, last_value, value, target_value);\n\t\t\t\tlast_time = now;\n\t\t\t\tlast_value = value;\n\t\t\t\tstore.set((value = next_value));\n\t\t\t\tif (ctx.settled) {\n\t\t\t\t\ttask = null;\n\t\t\t\t}\n\t\t\t\treturn !ctx.settled;\n\t\t\t});\n\t\t}\n\t\treturn new Promise((fulfil) => {\n\t\t\ttask.promise.then(() => {\n\t\t\t\tif (token === current_token) fulfil();\n\t\t\t});\n\t\t});\n\t}\n\t/** @type {import('./public.js').Spring<T>} */\n\tconst spring = {\n\t\tset,\n\t\tupdate: (fn, opts) => set(fn(target_value, value), opts),\n\t\tsubscribe: store.subscribe,\n\t\tstiffness,\n\t\tdamping,\n\t\tprecision\n\t};\n\treturn spring;\n}\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport { spring } from \"svelte/motion\";\n\n\texport let margin = true;\n\n\tconst top = spring([0, 0]);\n\tconst bottom = spring([0, 0]);\n\n\tlet dismounted: boolean;\n\n\tasync function animate(): Promise<void> {\n\t\tawait Promise.all([top.set([125, 140]), bottom.set([-125, -140])]);\n\t\tawait Promise.all([top.set([-125, 140]), bottom.set([125, -140])]);\n\t\tawait Promise.all([top.set([-125, 0]), bottom.set([125, -0])]);\n\t\tawait Promise.all([top.set([125, 0]), bottom.set([-125, 0])]);\n\t}\n\n\tasync function run(): Promise<void> {\n\t\tawait animate();\n\t\tif (!dismounted) run();\n\t}\n\n\tasync function loading(): Promise<void> {\n\t\tawait Promise.all([top.set([125, 0]), bottom.set([-125, 0])]);\n\n\t\trun();\n\t}\n\n\tonMount(() => {\n\t\tloading();\n\t\treturn (): boolean => (dismounted = true);\n\t});\n</script>\n\n<div class:margin>\n\t<svg\n\t\tviewBox=\"-1200 -1200 3000 3000\"\n\t\tfill=\"none\"\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t>\n\t\t<g style=\"transform: translate({$top[0]}px, {$top[1]}px);\">\n\t\t\t<path\n\t\t\t\td=\"M255.926 0.754768L509.702 139.936V221.027L255.926 81.8465V0.754768Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t\tfill-opacity=\"0.4\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M509.69 139.936L254.981 279.641V361.255L509.69 221.55V139.936Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M0.250138 139.937L254.981 279.641V361.255L0.250138 221.55V139.937Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t\tfill-opacity=\"0.4\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M255.923 0.232622L0.236328 139.936V221.55L255.923 81.8469V0.232622Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t/>\n\t\t</g>\n\t\t<g style=\"transform: translate({$bottom[0]}px, {$bottom[1]}px);\">\n\t\t\t<path\n\t\t\t\td=\"M255.926 141.5L509.702 280.681V361.773L255.926 222.592V141.5Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t\tfill-opacity=\"0.4\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M509.69 280.679L254.981 420.384V501.998L509.69 362.293V280.679Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M0.250138 280.681L254.981 420.386V502L0.250138 362.295V280.681Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t\tfill-opacity=\"0.4\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M255.923 140.977L0.236328 280.68V362.294L255.923 222.591V140.977Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t/>\n\t\t</g>\n\t</svg>\n</div>\n\n<style>\n\tsvg {\n\t\twidth: var(--size-20);\n\t\theight: var(--size-20);\n\t}\n\n\tsvg path {\n\t\tfill: var(--loader-color);\n\t}\n\n\tdiv {\n\t\tz-index: var(--layer-2);\n\t}\n\n\t.margin {\n\t\tmargin: var(--size-4);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { type ComponentType } from \"svelte\";\n\texport let Icon: ComponentType;\n\texport let label = \"\";\n\texport let show_label = false;\n\texport let pending = false;\n\texport let size: \"small\" | \"large\" | \"medium\" = \"small\";\n\texport let padded = true;\n\texport let highlight = false;\n\texport let disabled = false;\n\texport let hasPopup = false;\n\texport let color = \"var(--block-label-text-color)\";\n\texport let transparent = false;\n\texport let background = \"var(--background-fill-primary)\";\n\texport let offset = 0;\n\t$: _color = highlight ? \"var(--color-accent)\" : color;\n</script>\n\n<button\n\t{disabled}\n\ton:click\n\taria-label={label}\n\taria-haspopup={hasPopup}\n\ttitle={label}\n\tclass:pending\n\tclass:padded\n\tclass:highlight\n\tclass:transparent\n\tstyle:color={!disabled && _color ? _color : \"var(--block-label-text-color)\"}\n\tstyle:--bg-color={!disabled ? background : \"auto\"}\n\tstyle:margin-left={offset + \"px\"}\n>\n\t{#if show_label}<span>{label}</span>{/if}\n\t<div\n\t\tclass:small={size === \"small\"}\n\t\tclass:large={size === \"large\"}\n\t\tclass:medium={size === \"medium\"}\n\t>\n\t\t<Icon />\n\t</div>\n</button>\n\n<style>\n\tbutton {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: 1px;\n\t\tz-index: var(--layer-2);\n\t\t/* background: var(--background-fill-primary); */\n\t\tborder-radius: var(--radius-sm);\n\t\tcolor: var(--block-label-text-color);\n\t\tborder: 1px solid transparent;\n\t}\n\n\tbutton[disabled] {\n\t\topacity: 0.5;\n\t\tbox-shadow: none;\n\t}\n\n\tbutton[disabled]:hover {\n\t\tcursor: not-allowed;\n\t\t/* border: 1px solid var(--button-secondary-border-color); */\n\t\t/* padding: 2px; */\n\t}\n\n\t.padded {\n\t\tpadding: 2px;\n\t\tbackground: var(--bg-color);\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t}\n\n\tbutton:hover,\n\tbutton.highlight {\n\t\tcursor: pointer;\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.padded:hover {\n\t\tborder: 2px solid var(--button-secondary-border-color-hover);\n\t\tpadding: 1px;\n\t\tcolor: var(--block-label-text-color);\n\t}\n\n\tspan {\n\t\tpadding: 0px 1px;\n\t\tfont-size: 10px;\n\t}\n\n\tdiv {\n\t\tpadding: 2px;\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t}\n\n\t.small {\n\t\twidth: 14px;\n\t\theight: 14px;\n\t}\n\n\t.medium {\n\t\twidth: 20px;\n\t\theight: 20px;\n\t}\n\n\t.large {\n\t\twidth: 22px;\n\t\theight: 22px;\n\t}\n\n\t.pending {\n\t\tanimation: flash 0.5s infinite;\n\t}\n\n\t@keyframes flash {\n\t\t0% {\n\t\t\topacity: 0.5;\n\t\t}\n\t\t50% {\n\t\t\topacity: 1;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0.5;\n\t\t}\n\t}\n\n\t.transparent {\n\t\tbackground: transparent;\n\t\tborder: none;\n\t\tbox-shadow: none;\n\t}\n</style>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tversion=\"1.1\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\txml:space=\"preserve\"\n\tstroke=\"currentColor\"\n\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;stroke-linecap:round;stroke-linejoin:round;\"\n>\n\t<g\n\t\ttransform=\"matrix(1.14096,-0.140958,-0.140958,1.14096,-0.0559523,0.0559523)\"\n\t>\n\t\t<path\n\t\t\td=\"M18,6L6.087,17.913\"\n\t\t\tstyle=\"fill:none;fill-rule:nonzero;stroke-width:2px;\"\n\t\t/>\n\t</g>\n\t<path\n\t\td=\"M4.364,4.364L19.636,19.636\"\n\t\tstyle=\"fill:none;fill-rule:nonzero;stroke-width:2px;\"\n\t/>\n</svg>\n", "// import tw_colors from \"tailwindcss/colors\";\n\nexport const ordered_colors = [\n\t\"red\",\n\t\"green\",\n\t\"blue\",\n\t\"yellow\",\n\t\"purple\",\n\t\"teal\",\n\t\"orange\",\n\t\"cyan\",\n\t\"lime\",\n\t\"pink\"\n] as const;\ninterface ColorPair {\n\tprimary: string;\n\tsecondary: string;\n}\n\ninterface Colors {\n\tred: ColorPair;\n\tgreen: ColorPair;\n\tblue: ColorPair;\n\tyellow: ColorPair;\n\tpurple: ColorPair;\n\tteal: ColorPair;\n\torange: ColorPair;\n\tcyan: ColorPair;\n\tlime: ColorPair;\n\tpink: ColorPair;\n}\n\n// https://play.tailwindcss.com/ZubQYya0aN\nexport const color_values = [\n\t{ color: \"red\", primary: 600, secondary: 100 },\n\t{ color: \"green\", primary: 600, secondary: 100 },\n\t{ color: \"blue\", primary: 600, secondary: 100 },\n\t{ color: \"yellow\", primary: 500, secondary: 100 },\n\t{ color: \"purple\", primary: 600, secondary: 100 },\n\t{ color: \"teal\", primary: 600, secondary: 100 },\n\t{ color: \"orange\", primary: 600, secondary: 100 },\n\t{ color: \"cyan\", primary: 600, secondary: 100 },\n\t{ color: \"lime\", primary: 500, secondary: 100 },\n\t{ color: \"pink\", primary: 600, secondary: 100 }\n] as const;\n\nconst tw_colors = {\n\tinherit: \"inherit\",\n\tcurrent: \"currentColor\",\n\ttransparent: \"transparent\",\n\tblack: \"#000\",\n\twhite: \"#fff\",\n\tslate: {\n\t\t50: \"#f8fafc\",\n\t\t100: \"#f1f5f9\",\n\t\t200: \"#e2e8f0\",\n\t\t300: \"#cbd5e1\",\n\t\t400: \"#94a3b8\",\n\t\t500: \"#64748b\",\n\t\t600: \"#475569\",\n\t\t700: \"#334155\",\n\t\t800: \"#1e293b\",\n\t\t900: \"#0f172a\",\n\t\t950: \"#020617\"\n\t},\n\tgray: {\n\t\t50: \"#f9fafb\",\n\t\t100: \"#f3f4f6\",\n\t\t200: \"#e5e7eb\",\n\t\t300: \"#d1d5db\",\n\t\t400: \"#9ca3af\",\n\t\t500: \"#6b7280\",\n\t\t600: \"#4b5563\",\n\t\t700: \"#374151\",\n\t\t800: \"#1f2937\",\n\t\t900: \"#111827\",\n\t\t950: \"#030712\"\n\t},\n\tzinc: {\n\t\t50: \"#fafafa\",\n\t\t100: \"#f4f4f5\",\n\t\t200: \"#e4e4e7\",\n\t\t300: \"#d4d4d8\",\n\t\t400: \"#a1a1aa\",\n\t\t500: \"#71717a\",\n\t\t600: \"#52525b\",\n\t\t700: \"#3f3f46\",\n\t\t800: \"#27272a\",\n\t\t900: \"#18181b\",\n\t\t950: \"#09090b\"\n\t},\n\tneutral: {\n\t\t50: \"#fafafa\",\n\t\t100: \"#f5f5f5\",\n\t\t200: \"#e5e5e5\",\n\t\t300: \"#d4d4d4\",\n\t\t400: \"#a3a3a3\",\n\t\t500: \"#737373\",\n\t\t600: \"#525252\",\n\t\t700: \"#404040\",\n\t\t800: \"#262626\",\n\t\t900: \"#171717\",\n\t\t950: \"#0a0a0a\"\n\t},\n\tstone: {\n\t\t50: \"#fafaf9\",\n\t\t100: \"#f5f5f4\",\n\t\t200: \"#e7e5e4\",\n\t\t300: \"#d6d3d1\",\n\t\t400: \"#a8a29e\",\n\t\t500: \"#78716c\",\n\t\t600: \"#57534e\",\n\t\t700: \"#44403c\",\n\t\t800: \"#292524\",\n\t\t900: \"#1c1917\",\n\t\t950: \"#0c0a09\"\n\t},\n\tred: {\n\t\t50: \"#fef2f2\",\n\t\t100: \"#fee2e2\",\n\t\t200: \"#fecaca\",\n\t\t300: \"#fca5a5\",\n\t\t400: \"#f87171\",\n\t\t500: \"#ef4444\",\n\t\t600: \"#dc2626\",\n\t\t700: \"#b91c1c\",\n\t\t800: \"#991b1b\",\n\t\t900: \"#7f1d1d\",\n\t\t950: \"#450a0a\"\n\t},\n\torange: {\n\t\t50: \"#fff7ed\",\n\t\t100: \"#ffedd5\",\n\t\t200: \"#fed7aa\",\n\t\t300: \"#fdba74\",\n\t\t400: \"#fb923c\",\n\t\t500: \"#f97316\",\n\t\t600: \"#ea580c\",\n\t\t700: \"#c2410c\",\n\t\t800: \"#9a3412\",\n\t\t900: \"#7c2d12\",\n\t\t950: \"#431407\"\n\t},\n\tamber: {\n\t\t50: \"#fffbeb\",\n\t\t100: \"#fef3c7\",\n\t\t200: \"#fde68a\",\n\t\t300: \"#fcd34d\",\n\t\t400: \"#fbbf24\",\n\t\t500: \"#f59e0b\",\n\t\t600: \"#d97706\",\n\t\t700: \"#b45309\",\n\t\t800: \"#92400e\",\n\t\t900: \"#78350f\",\n\t\t950: \"#451a03\"\n\t},\n\tyellow: {\n\t\t50: \"#fefce8\",\n\t\t100: \"#fef9c3\",\n\t\t200: \"#fef08a\",\n\t\t300: \"#fde047\",\n\t\t400: \"#facc15\",\n\t\t500: \"#eab308\",\n\t\t600: \"#ca8a04\",\n\t\t700: \"#a16207\",\n\t\t800: \"#854d0e\",\n\t\t900: \"#713f12\",\n\t\t950: \"#422006\"\n\t},\n\tlime: {\n\t\t50: \"#f7fee7\",\n\t\t100: \"#ecfccb\",\n\t\t200: \"#d9f99d\",\n\t\t300: \"#bef264\",\n\t\t400: \"#a3e635\",\n\t\t500: \"#84cc16\",\n\t\t600: \"#65a30d\",\n\t\t700: \"#4d7c0f\",\n\t\t800: \"#3f6212\",\n\t\t900: \"#365314\",\n\t\t950: \"#1a2e05\"\n\t},\n\tgreen: {\n\t\t50: \"#f0fdf4\",\n\t\t100: \"#dcfce7\",\n\t\t200: \"#bbf7d0\",\n\t\t300: \"#86efac\",\n\t\t400: \"#4ade80\",\n\t\t500: \"#22c55e\",\n\t\t600: \"#16a34a\",\n\t\t700: \"#15803d\",\n\t\t800: \"#166534\",\n\t\t900: \"#14532d\",\n\t\t950: \"#052e16\"\n\t},\n\temerald: {\n\t\t50: \"#ecfdf5\",\n\t\t100: \"#d1fae5\",\n\t\t200: \"#a7f3d0\",\n\t\t300: \"#6ee7b7\",\n\t\t400: \"#34d399\",\n\t\t500: \"#10b981\",\n\t\t600: \"#059669\",\n\t\t700: \"#047857\",\n\t\t800: \"#065f46\",\n\t\t900: \"#064e3b\",\n\t\t950: \"#022c22\"\n\t},\n\tteal: {\n\t\t50: \"#f0fdfa\",\n\t\t100: \"#ccfbf1\",\n\t\t200: \"#99f6e4\",\n\t\t300: \"#5eead4\",\n\t\t400: \"#2dd4bf\",\n\t\t500: \"#14b8a6\",\n\t\t600: \"#0d9488\",\n\t\t700: \"#0f766e\",\n\t\t800: \"#115e59\",\n\t\t900: \"#134e4a\",\n\t\t950: \"#042f2e\"\n\t},\n\tcyan: {\n\t\t50: \"#ecfeff\",\n\t\t100: \"#cffafe\",\n\t\t200: \"#a5f3fc\",\n\t\t300: \"#67e8f9\",\n\t\t400: \"#22d3ee\",\n\t\t500: \"#06b6d4\",\n\t\t600: \"#0891b2\",\n\t\t700: \"#0e7490\",\n\t\t800: \"#155e75\",\n\t\t900: \"#164e63\",\n\t\t950: \"#083344\"\n\t},\n\tsky: {\n\t\t50: \"#f0f9ff\",\n\t\t100: \"#e0f2fe\",\n\t\t200: \"#bae6fd\",\n\t\t300: \"#7dd3fc\",\n\t\t400: \"#38bdf8\",\n\t\t500: \"#0ea5e9\",\n\t\t600: \"#0284c7\",\n\t\t700: \"#0369a1\",\n\t\t800: \"#075985\",\n\t\t900: \"#0c4a6e\",\n\t\t950: \"#082f49\"\n\t},\n\tblue: {\n\t\t50: \"#eff6ff\",\n\t\t100: \"#dbeafe\",\n\t\t200: \"#bfdbfe\",\n\t\t300: \"#93c5fd\",\n\t\t400: \"#60a5fa\",\n\t\t500: \"#3b82f6\",\n\t\t600: \"#2563eb\",\n\t\t700: \"#1d4ed8\",\n\t\t800: \"#1e40af\",\n\t\t900: \"#1e3a8a\",\n\t\t950: \"#172554\"\n\t},\n\tindigo: {\n\t\t50: \"#eef2ff\",\n\t\t100: \"#e0e7ff\",\n\t\t200: \"#c7d2fe\",\n\t\t300: \"#a5b4fc\",\n\t\t400: \"#818cf8\",\n\t\t500: \"#6366f1\",\n\t\t600: \"#4f46e5\",\n\t\t700: \"#4338ca\",\n\t\t800: \"#3730a3\",\n\t\t900: \"#312e81\",\n\t\t950: \"#1e1b4b\"\n\t},\n\tviolet: {\n\t\t50: \"#f5f3ff\",\n\t\t100: \"#ede9fe\",\n\t\t200: \"#ddd6fe\",\n\t\t300: \"#c4b5fd\",\n\t\t400: \"#a78bfa\",\n\t\t500: \"#8b5cf6\",\n\t\t600: \"#7c3aed\",\n\t\t700: \"#6d28d9\",\n\t\t800: \"#5b21b6\",\n\t\t900: \"#4c1d95\",\n\t\t950: \"#2e1065\"\n\t},\n\tpurple: {\n\t\t50: \"#faf5ff\",\n\t\t100: \"#f3e8ff\",\n\t\t200: \"#e9d5ff\",\n\t\t300: \"#d8b4fe\",\n\t\t400: \"#c084fc\",\n\t\t500: \"#a855f7\",\n\t\t600: \"#9333ea\",\n\t\t700: \"#7e22ce\",\n\t\t800: \"#6b21a8\",\n\t\t900: \"#581c87\",\n\t\t950: \"#3b0764\"\n\t},\n\tfuchsia: {\n\t\t50: \"#fdf4ff\",\n\t\t100: \"#fae8ff\",\n\t\t200: \"#f5d0fe\",\n\t\t300: \"#f0abfc\",\n\t\t400: \"#e879f9\",\n\t\t500: \"#d946ef\",\n\t\t600: \"#c026d3\",\n\t\t700: \"#a21caf\",\n\t\t800: \"#86198f\",\n\t\t900: \"#701a75\",\n\t\t950: \"#4a044e\"\n\t},\n\tpink: {\n\t\t50: \"#fdf2f8\",\n\t\t100: \"#fce7f3\",\n\t\t200: \"#fbcfe8\",\n\t\t300: \"#f9a8d4\",\n\t\t400: \"#f472b6\",\n\t\t500: \"#ec4899\",\n\t\t600: \"#db2777\",\n\t\t700: \"#be185d\",\n\t\t800: \"#9d174d\",\n\t\t900: \"#831843\",\n\t\t950: \"#500724\"\n\t},\n\trose: {\n\t\t50: \"#fff1f2\",\n\t\t100: \"#ffe4e6\",\n\t\t200: \"#fecdd3\",\n\t\t300: \"#fda4af\",\n\t\t400: \"#fb7185\",\n\t\t500: \"#f43f5e\",\n\t\t600: \"#e11d48\",\n\t\t700: \"#be123c\",\n\t\t800: \"#9f1239\",\n\t\t900: \"#881337\",\n\t\t950: \"#4c0519\"\n\t}\n};\n\nexport const colors = color_values.reduce(\n\t(acc, { color, primary, secondary }) => ({\n\t\t...acc,\n\t\t[color]: {\n\t\t\tprimary: tw_colors[color][primary],\n\t\t\tsecondary: tw_colors[color][secondary]\n\t\t}\n\t}),\n\t{} as Colors\n);\n", "<script context=\"module\" lang=\"ts\">\n\timport { tick } from \"svelte\";\n\timport { pretty_si } from \"./utils\";\n\n\tlet items: HTMLDivElement[] = [];\n\n\tlet called = false;\n\n\tasync function scroll_into_view(\n\t\tel: HTMLDivElement,\n\t\tenable: boolean | null = true\n\t): Promise<void> {\n\t\tif (\n\t\t\twindow.__gradio_mode__ === \"website\" ||\n\t\t\t(window.__gradio_mode__ !== \"app\" && enable !== true)\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\titems.push(el);\n\t\tif (!called) called = true;\n\t\telse return;\n\n\t\tawait tick();\n\n\t\trequestAnimationFrame(() => {\n\t\t\tlet min = [0, 0];\n\n\t\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\t\tconst element = items[i];\n\n\t\t\t\tconst box = element.getBoundingClientRect();\n\t\t\t\tif (i === 0 || box.top + window.scrollY <= min[0]) {\n\t\t\t\t\tmin[0] = box.top + window.scrollY;\n\t\t\t\t\tmin[1] = i;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\twindow.scrollTo({ top: min[0] - 20, behavior: \"smooth\" });\n\n\t\t\tcalled = false;\n\t\t\titems = [];\n\t\t});\n\t}\n</script>\n\n<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\n\timport Loader from \"./Loader.svelte\";\n\timport type { LoadingStatus } from \"./types\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { Clear } from \"@gradio/icons\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let i18n: I18nFormatter;\n\texport let eta: number | null = null;\n\texport let queue_position: number | null;\n\texport let queue_size: number | null;\n\texport let status: \"complete\" | \"pending\" | \"error\" | \"generating\" | null;\n\texport let scroll_to_output = false;\n\texport let timer = true;\n\texport let show_progress: \"full\" | \"minimal\" | \"hidden\" = \"full\";\n\texport let message: string | null = null;\n\texport let progress: LoadingStatus[\"progress\"] | null | undefined = null;\n\texport let variant: \"default\" | \"center\" = \"default\";\n\texport let loading_text = \"Loading...\";\n\texport let absolute = true;\n\texport let translucent = false;\n\texport let border = false;\n\texport let autoscroll: boolean;\n\n\tlet el: HTMLDivElement;\n\n\tlet _timer = false;\n\tlet timer_start = 0;\n\tlet timer_diff = 0;\n\tlet old_eta: number | null = null;\n\tlet eta_from_start: number | null = null;\n\tlet message_visible = false;\n\tlet eta_level: number | null = 0;\n\tlet progress_level: (number | undefined)[] | null = null;\n\tlet last_progress_level: number | undefined = undefined;\n\tlet progress_bar: HTMLElement | null = null;\n\tlet show_eta_bar = true;\n\n\t$: eta_level =\n\t\teta_from_start === null || eta_from_start <= 0 || !timer_diff\n\t\t\t? null\n\t\t\t: Math.min(timer_diff / eta_from_start, 1);\n\t$: if (progress != null) {\n\t\tshow_eta_bar = false;\n\t}\n\n\t$: {\n\t\tif (progress != null) {\n\t\t\tprogress_level = progress.map((p) => {\n\t\t\t\tif (p.index != null && p.length != null) {\n\t\t\t\t\treturn p.index / p.length;\n\t\t\t\t} else if (p.progress != null) {\n\t\t\t\t\treturn p.progress;\n\t\t\t\t}\n\t\t\t\treturn undefined;\n\t\t\t});\n\t\t} else {\n\t\t\tprogress_level = null;\n\t\t}\n\n\t\tif (progress_level) {\n\t\t\tlast_progress_level = progress_level[progress_level.length - 1];\n\t\t\tif (progress_bar) {\n\t\t\t\tif (last_progress_level === 0) {\n\t\t\t\t\tprogress_bar.style.transition = \"0\";\n\t\t\t\t} else {\n\t\t\t\t\tprogress_bar.style.transition = \"150ms\";\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tlast_progress_level = undefined;\n\t\t}\n\t}\n\n\tconst start_timer = (): void => {\n\t\teta = old_eta = formatted_eta = null;\n\t\ttimer_start = performance.now();\n\t\ttimer_diff = 0;\n\t\t_timer = true;\n\t\trun();\n\t};\n\n\tfunction run(): void {\n\t\trequestAnimationFrame(() => {\n\t\t\ttimer_diff = (performance.now() - timer_start) / 1000;\n\t\t\tif (_timer) run();\n\t\t});\n\t}\n\n\tfunction stop_timer(): void {\n\t\ttimer_diff = 0;\n\t\teta = old_eta = formatted_eta = null;\n\n\t\tif (!_timer) return;\n\t\t_timer = false;\n\t}\n\n\tonDestroy(() => {\n\t\tif (_timer) stop_timer();\n\t});\n\n\t$: {\n\t\tif (status === \"pending\") {\n\t\t\tstart_timer();\n\t\t} else {\n\t\t\tstop_timer();\n\t\t}\n\t}\n\n\t$: el &&\n\t\tscroll_to_output &&\n\t\t(status === \"pending\" || status === \"complete\") &&\n\t\tscroll_into_view(el, autoscroll);\n\n\tlet formatted_eta: string | null = null;\n\t$: {\n\t\tif (eta === null) {\n\t\t\teta = old_eta;\n\t\t}\n\t\tif (eta != null && old_eta !== eta) {\n\t\t\teta_from_start = (performance.now() - timer_start) / 1000 + eta;\n\t\t\tformatted_eta = eta_from_start.toFixed(1);\n\t\t\told_eta = eta;\n\t\t}\n\t}\n\tlet show_message_timeout: NodeJS.Timeout | null = null;\n\tfunction close_message(): void {\n\t\tmessage_visible = false;\n\t\tif (show_message_timeout !== null) {\n\t\t\tclearTimeout(show_message_timeout);\n\t\t}\n\t}\n\t$: {\n\t\tclose_message();\n\t\tif (status === \"error\" && message) {\n\t\t\tmessage_visible = true;\n\t\t}\n\t}\n\t$: formatted_timer = timer_diff.toFixed(1);\n</script>\n\n<div\n\tclass=\"wrap {variant} {show_progress}\"\n\tclass:hide={!status || status === \"complete\" || show_progress === \"hidden\"}\n\tclass:translucent={(variant === \"center\" &&\n\t\t(status === \"pending\" || status === \"error\")) ||\n\t\ttranslucent ||\n\t\tshow_progress === \"minimal\"}\n\tclass:generating={status === \"generating\" && show_progress === \"full\"}\n\tclass:border\n\tstyle:position={absolute ? \"absolute\" : \"static\"}\n\tstyle:padding={absolute ? \"0\" : \"var(--size-8) 0\"}\n\tbind:this={el}\n>\n\t{#if status === \"pending\"}\n\t\t{#if variant === \"default\" && show_eta_bar && show_progress === \"full\"}\n\t\t\t<div\n\t\t\t\tclass=\"eta-bar\"\n\t\t\t\tstyle:transform=\"translateX({(eta_level || 0) * 100 - 100}%)\"\n\t\t\t/>\n\t\t{/if}\n\t\t<div\n\t\t\tclass:meta-text-center={variant === \"center\"}\n\t\t\tclass:meta-text={variant === \"default\"}\n\t\t\tclass=\"progress-text\"\n\t\t>\n\t\t\t{#if progress}\n\t\t\t\t{#each progress as p}\n\t\t\t\t\t{#if p.index != null}\n\t\t\t\t\t\t{#if p.length != null}\n\t\t\t\t\t\t\t{pretty_si(p.index || 0)}/{pretty_si(p.length)}\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t{pretty_si(p.index || 0)}\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{p.unit} | {\" \"}\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t{:else if queue_position !== null && queue_size !== undefined && queue_position >= 0}\n\t\t\t\tqueue: {queue_position + 1}/{queue_size} |\n\t\t\t{:else if queue_position === 0}\n\t\t\t\tprocessing |\n\t\t\t{/if}\n\n\t\t\t{#if timer}\n\t\t\t\t{formatted_timer}{eta ? `/${formatted_eta}` : \"\"}s\n\t\t\t{/if}\n\t\t</div>\n\n\t\t{#if last_progress_level != null}\n\t\t\t<div class=\"progress-level\">\n\t\t\t\t<div class=\"progress-level-inner\">\n\t\t\t\t\t{#if progress != null}\n\t\t\t\t\t\t{#each progress as p, i}\n\t\t\t\t\t\t\t{#if p.desc != null || (progress_level && progress_level[i] != null)}\n\t\t\t\t\t\t\t\t{#if i !== 0}\n\t\t\t\t\t\t\t\t\t&nbsp;/\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t{#if p.desc != null}\n\t\t\t\t\t\t\t\t\t{p.desc}\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t{#if p.desc != null && progress_level && progress_level[i] != null}\n\t\t\t\t\t\t\t\t\t-\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t{#if progress_level != null}\n\t\t\t\t\t\t\t\t\t{(100 * (progress_level[i] || 0)).toFixed(1)}%\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t{/if}\n\t\t\t\t</div>\n\n\t\t\t\t<div class=\"progress-bar-wrap\">\n\t\t\t\t\t<div\n\t\t\t\t\t\tbind:this={progress_bar}\n\t\t\t\t\t\tclass=\"progress-bar\"\n\t\t\t\t\t\tstyle:width=\"{last_progress_level * 100}%\"\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t{:else if show_progress === \"full\"}\n\t\t\t<Loader margin={variant === \"default\"} />\n\t\t{/if}\n\n\t\t{#if !timer}\n\t\t\t<p class=\"loading\">{loading_text}</p>\n\t\t\t<slot name=\"additional-loading-text\" />\n\t\t{/if}\n\t{:else if status === \"error\"}\n\t\t<div class=\"clear-status\">\n\t\t\t<IconButton\n\t\t\t\tIcon={Clear}\n\t\t\t\tlabel={i18n(\"common.clear\")}\n\t\t\t\tdisabled={false}\n\t\t\t\ton:click={() => {\n\t\t\t\t\tdispatch(\"clear_status\");\n\t\t\t\t}}\n\t\t\t/>\n\t\t</div>\n\t\t<span class=\"error\">{i18n(\"common.error\")}</span>\n\t\t<slot name=\"error\" />\n\t{/if}\n</div>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: var(--layer-2);\n\t\ttransition: opacity 0.1s ease-in-out;\n\t\tborder-radius: var(--block-radius);\n\t\tbackground: var(--block-background-fill);\n\t\tpadding: 0 var(--size-6);\n\t\tmax-height: var(--size-screen-h);\n\t\toverflow: hidden;\n\t}\n\n\t.wrap.center {\n\t\ttop: 0;\n\t\tright: 0px;\n\t\tleft: 0px;\n\t}\n\n\t.wrap.default {\n\t\ttop: 0px;\n\t\tright: 0px;\n\t\tbottom: 0px;\n\t\tleft: 0px;\n\t}\n\n\t.hide {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n\n\t.generating {\n\t\tanimation:\n\t\t\tpulseStart 1s cubic-bezier(0.4, 0, 0.6, 1),\n\t\t\tpulse 2s cubic-bezier(0.4, 0, 0.6, 1) 1s infinite;\n\t\tborder: 2px solid var(--color-accent);\n\t\tbackground: transparent;\n\t\tz-index: var(--layer-1);\n\t\tpointer-events: none;\n\t}\n\n\t.translucent {\n\t\tbackground: none;\n\t}\n\n\t@keyframes pulseStart {\n\t\t0% {\n\t\t\topacity: 0;\n\t\t}\n\t\t100% {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t@keyframes pulse {\n\t\t0%,\n\t\t100% {\n\t\t\topacity: 1;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.5;\n\t\t}\n\t}\n\n\t.loading {\n\t\tz-index: var(--layer-2);\n\t\tcolor: var(--body-text-color);\n\t}\n\t.eta-bar {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\ttransform-origin: left;\n\t\topacity: 0.8;\n\t\tz-index: var(--layer-1);\n\t\ttransition: 10ms;\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\t.progress-bar-wrap {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tbackground: var(--background-fill-primary);\n\t\twidth: 55.5%;\n\t\theight: var(--size-4);\n\t}\n\t.progress-bar {\n\t\ttransform-origin: left;\n\t\tbackground-color: var(--loader-color);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.progress-level {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tgap: 1;\n\t\tz-index: var(--layer-2);\n\t\twidth: var(--size-full);\n\t}\n\n\t.progress-level-inner {\n\t\tmargin: var(--size-2) auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.meta-text {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tz-index: var(--layer-2);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.meta-text-center {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\ttransform: translateY(var(--size-6));\n\t\tz-index: var(--layer-2);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t\ttext-align: center;\n\t}\n\n\t.error {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: solid 1px var(--error-border-color);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--error-background-fill);\n\t\tpadding-right: var(--size-4);\n\t\tpadding-left: var(--size-4);\n\t\tcolor: var(--error-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\tfont-size: var(--text-lg);\n\t\tline-height: var(--line-lg);\n\t\tfont-family: var(--font);\n\t}\n\n\t.minimal {\n\t\tpointer-events: none;\n\t}\n\n\t.minimal .progress-text {\n\t\tbackground: var(--block-background-fill);\n\t}\n\n\t.border {\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\n\t.clear-status {\n\t\tposition: absolute;\n\t\tdisplay: flex;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tjustify-content: flex-end;\n\t\tgap: var(--spacing-sm);\n\t\tz-index: var(--layer-1);\n\t}\n</style>\n", "/*! @license DOMPurify 3.0.3 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE */\n\nconst {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor\n} = Object;\nlet {\n  freeze,\n  seal,\n  create\n} = Object; // eslint-disable-line import/no-mutable-exports\n\nlet {\n  apply,\n  construct\n} = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function apply(fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!freeze) {\n  freeze = function freeze(x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function seal(x) {\n    return x;\n  };\n}\n\nif (!construct) {\n  construct = function construct(Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\nconst regExpTest = unapply(RegExp.prototype.test);\nconst typeErrorCreate = unconstruct(TypeError);\nfunction unapply(func) {\n  return function (thisArg) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return apply(func, thisArg, args);\n  };\n}\nfunction unconstruct(func) {\n  return function () {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    return construct(func, args);\n  };\n}\n/* Add properties to a lookup table */\n\nfunction addToSet(set, array, transformCaseFunc) {\n  var _transformCaseFunc;\n\n  transformCaseFunc = (_transformCaseFunc = transformCaseFunc) !== null && _transformCaseFunc !== void 0 ? _transformCaseFunc : stringToLowerCase;\n\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n\n  while (l--) {\n    let element = array[l];\n\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n/* Shallow clone an object */\n\nfunction clone(object) {\n  const newObject = create(null);\n\n  for (const [property, value] of entries(object)) {\n    newObject[property] = value;\n  }\n\n  return newObject;\n}\n/* This method automatically checks if the prop is function\n * or getter and behaves accordingly. */\n\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(element) {\n    console.warn('fallback value for', element);\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nconst html$1 = freeze(['a', 'abbr', 'acronym', 'address', 'area', 'article', 'aside', 'audio', 'b', 'bdi', 'bdo', 'big', 'blink', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'center', 'cite', 'code', 'col', 'colgroup', 'content', 'data', 'datalist', 'dd', 'decorator', 'del', 'details', 'dfn', 'dialog', 'dir', 'div', 'dl', 'dt', 'element', 'em', 'fieldset', 'figcaption', 'figure', 'font', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meter', 'nav', 'nobr', 'ol', 'optgroup', 'option', 'output', 'p', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'section', 'select', 'shadow', 'small', 'source', 'spacer', 'span', 'strike', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'track', 'tt', 'u', 'ul', 'var', 'video', 'wbr']); // SVG\n\nconst svg$1 = freeze(['svg', 'a', 'altglyph', 'altglyphdef', 'altglyphitem', 'animatecolor', 'animatemotion', 'animatetransform', 'circle', 'clippath', 'defs', 'desc', 'ellipse', 'filter', 'font', 'g', 'glyph', 'glyphref', 'hkern', 'image', 'line', 'lineargradient', 'marker', 'mask', 'metadata', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialgradient', 'rect', 'stop', 'style', 'switch', 'symbol', 'text', 'textpath', 'title', 'tref', 'tspan', 'view', 'vkern']);\nconst svgFilters = freeze(['feBlend', 'feColorMatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feDropShadow', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence']); // List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\n\nconst svgDisallowed = freeze(['animate', 'color-profile', 'cursor', 'discard', 'font-face', 'font-face-format', 'font-face-name', 'font-face-src', 'font-face-uri', 'foreignobject', 'hatch', 'hatchpath', 'mesh', 'meshgradient', 'meshpatch', 'meshrow', 'missing-glyph', 'script', 'set', 'solidcolor', 'unknown', 'use']);\nconst mathMl$1 = freeze(['math', 'menclose', 'merror', 'mfenced', 'mfrac', 'mglyph', 'mi', 'mlabeledtr', 'mmultiscripts', 'mn', 'mo', 'mover', 'mpadded', 'mphantom', 'mroot', 'mrow', 'ms', 'mspace', 'msqrt', 'mstyle', 'msub', 'msup', 'msubsup', 'mtable', 'mtd', 'mtext', 'mtr', 'munder', 'munderover', 'mprescripts']); // Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\n\nconst mathMlDisallowed = freeze(['maction', 'maligngroup', 'malignmark', 'mlongdiv', 'mscarries', 'mscarry', 'msgroup', 'mstack', 'msline', 'msrow', 'semantics', 'annotation', 'annotation-xml', 'mprescripts', 'none']);\nconst text = freeze(['#text']);\n\nconst html = freeze(['accept', 'action', 'align', 'alt', 'autocapitalize', 'autocomplete', 'autopictureinpicture', 'autoplay', 'background', 'bgcolor', 'border', 'capture', 'cellpadding', 'cellspacing', 'checked', 'cite', 'class', 'clear', 'color', 'cols', 'colspan', 'controls', 'controlslist', 'coords', 'crossorigin', 'datetime', 'decoding', 'default', 'dir', 'disabled', 'disablepictureinpicture', 'disableremoteplayback', 'download', 'draggable', 'enctype', 'enterkeyhint', 'face', 'for', 'headers', 'height', 'hidden', 'high', 'href', 'hreflang', 'id', 'inputmode', 'integrity', 'ismap', 'kind', 'label', 'lang', 'list', 'loading', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'minlength', 'multiple', 'muted', 'name', 'nonce', 'noshade', 'novalidate', 'nowrap', 'open', 'optimum', 'pattern', 'placeholder', 'playsinline', 'poster', 'preload', 'pubdate', 'radiogroup', 'readonly', 'rel', 'required', 'rev', 'reversed', 'role', 'rows', 'rowspan', 'spellcheck', 'scope', 'selected', 'shape', 'size', 'sizes', 'span', 'srclang', 'start', 'src', 'srcset', 'step', 'style', 'summary', 'tabindex', 'title', 'translate', 'type', 'usemap', 'valign', 'value', 'width', 'xmlns', 'slot']);\nconst svg = freeze(['accent-height', 'accumulate', 'additive', 'alignment-baseline', 'ascent', 'attributename', 'attributetype', 'azimuth', 'basefrequency', 'baseline-shift', 'begin', 'bias', 'by', 'class', 'clip', 'clippathunits', 'clip-path', 'clip-rule', 'color', 'color-interpolation', 'color-interpolation-filters', 'color-profile', 'color-rendering', 'cx', 'cy', 'd', 'dx', 'dy', 'diffuseconstant', 'direction', 'display', 'divisor', 'dur', 'edgemode', 'elevation', 'end', 'fill', 'fill-opacity', 'fill-rule', 'filter', 'filterunits', 'flood-color', 'flood-opacity', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch', 'font-style', 'font-variant', 'font-weight', 'fx', 'fy', 'g1', 'g2', 'glyph-name', 'glyphref', 'gradientunits', 'gradienttransform', 'height', 'href', 'id', 'image-rendering', 'in', 'in2', 'k', 'k1', 'k2', 'k3', 'k4', 'kerning', 'keypoints', 'keysplines', 'keytimes', 'lang', 'lengthadjust', 'letter-spacing', 'kernelmatrix', 'kernelunitlength', 'lighting-color', 'local', 'marker-end', 'marker-mid', 'marker-start', 'markerheight', 'markerunits', 'markerwidth', 'maskcontentunits', 'maskunits', 'max', 'mask', 'media', 'method', 'mode', 'min', 'name', 'numoctaves', 'offset', 'operator', 'opacity', 'order', 'orient', 'orientation', 'origin', 'overflow', 'paint-order', 'path', 'pathlength', 'patterncontentunits', 'patterntransform', 'patternunits', 'points', 'preservealpha', 'preserveaspectratio', 'primitiveunits', 'r', 'rx', 'ry', 'radius', 'refx', 'refy', 'repeatcount', 'repeatdur', 'restart', 'result', 'rotate', 'scale', 'seed', 'shape-rendering', 'specularconstant', 'specularexponent', 'spreadmethod', 'startoffset', 'stddeviation', 'stitchtiles', 'stop-color', 'stop-opacity', 'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin', 'stroke-miterlimit', 'stroke-opacity', 'stroke', 'stroke-width', 'style', 'surfacescale', 'systemlanguage', 'tabindex', 'targetx', 'targety', 'transform', 'transform-origin', 'text-anchor', 'text-decoration', 'text-rendering', 'textlength', 'type', 'u1', 'u2', 'unicode', 'values', 'viewbox', 'visibility', 'version', 'vert-adv-y', 'vert-origin-x', 'vert-origin-y', 'width', 'word-spacing', 'wrap', 'writing-mode', 'xchannelselector', 'ychannelselector', 'x', 'x1', 'x2', 'xmlns', 'y', 'y1', 'y2', 'z', 'zoomandpan']);\nconst mathMl = freeze(['accent', 'accentunder', 'align', 'bevelled', 'close', 'columnsalign', 'columnlines', 'columnspan', 'denomalign', 'depth', 'dir', 'display', 'displaystyle', 'encoding', 'fence', 'frame', 'height', 'href', 'id', 'largeop', 'length', 'linethickness', 'lspace', 'lquote', 'mathbackground', 'mathcolor', 'mathsize', 'mathvariant', 'maxsize', 'minsize', 'movablelimits', 'notation', 'numalign', 'open', 'rowalign', 'rowlines', 'rowspacing', 'rowspan', 'rspace', 'rquote', 'scriptlevel', 'scriptminsize', 'scriptsizemultiplier', 'selection', 'separator', 'separators', 'stretchy', 'subscriptshift', 'supscriptshift', 'symmetric', 'voffset', 'width', 'xmlns']);\nconst xml = freeze(['xlink:href', 'xml:id', 'xlink:title', 'xml:space', 'xmlns:xlink']);\n\nconst MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\n\nconst ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nconst TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nconst DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\n\nconst ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\n\nconst IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nconst IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nconst ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nconst DOCTYPE_NAME = seal(/^html$/i);\n\nvar EXPRESSIONS = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  MUSTACHE_EXPR: MUSTACHE_EXPR,\n  ERB_EXPR: ERB_EXPR,\n  TMPLIT_EXPR: TMPLIT_EXPR,\n  DATA_ATTR: DATA_ATTR,\n  ARIA_ATTR: ARIA_ATTR,\n  IS_ALLOWED_URI: IS_ALLOWED_URI,\n  IS_SCRIPT_OR_DATA: IS_SCRIPT_OR_DATA,\n  ATTR_WHITESPACE: ATTR_WHITESPACE,\n  DOCTYPE_NAME: DOCTYPE_NAME\n});\n\nconst getGlobal = () => typeof window === 'undefined' ? null : window;\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {HTMLScriptElement} purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\n\n\nconst _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, purifyHostElement) {\n  if (typeof trustedTypes !== 'object' || typeof trustedTypes.createPolicy !== 'function') {\n    return null;\n  } // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n\n\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      }\n\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn('TrustedTypes policy ' + policyName + ' could not be created.');\n    return null;\n  }\n};\n\nfunction createDOMPurify() {\n  let window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n\n  const DOMPurify = root => createDOMPurify(root);\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n\n\n  DOMPurify.version = '3.0.3';\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n  const currentScript = originalDocument.currentScript;\n  let {\n    document\n  } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes\n  } = window;\n  const ElementPrototype = Element.prototype;\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode'); // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName\n  } = document;\n  const {\n    importNode\n  } = originalDocument;\n  let hooks = {};\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n\n  DOMPurify.isSupported = typeof entries === 'function' && typeof getParentNode === 'function' && implementation && implementation.createHTMLDocument !== undefined;\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE\n  } = EXPRESSIONS;\n  let {\n    IS_ALLOWED_URI: IS_ALLOWED_URI$1\n  } = EXPRESSIONS;\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [...html$1, ...svg$1, ...svgFilters, ...mathMl$1, ...text]);\n  /* Allowed attribute names */\n\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [...html, ...svg, ...mathMl, ...xml]);\n  /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(Object.create(null, {\n    tagNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    attributeNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    allowCustomizedBuiltInElements: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: false\n    }\n  }));\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n\n  let FORBID_TAGS = null;\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n\n  let FORBID_ATTR = null;\n  /* Decide if ARIA attributes are okay */\n\n  let ALLOW_ARIA_ATTR = true;\n  /* Decide if custom data attributes are okay */\n\n  let ALLOW_DATA_ATTR = true;\n  /* Decide if unknown protocols are okay */\n\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n\n  let SAFE_FOR_TEMPLATES = false;\n  /* Decide if document with <html>... should be returned */\n\n  let WHOLE_DOCUMENT = false;\n  /* Track whether config is already set on this instance of DOMPurify. */\n\n  let SET_CONFIG = false;\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n\n  let FORCE_BODY = false;\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n\n  let RETURN_DOM = false;\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n\n  let RETURN_DOM_FRAGMENT = false;\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n\n  let RETURN_TRUSTED_TYPE = false;\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n\n  let SANITIZE_DOM = true;\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n  /* Keep element content when removing element? */\n\n  let KEEP_CONTENT = true;\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n\n  let IN_PLACE = false;\n  /* Allow usage of profiles like html, svg and mathMl */\n\n  let USE_PROFILES = {};\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, ['annotation-xml', 'audio', 'colgroup', 'desc', 'foreignobject', 'head', 'iframe', 'math', 'mi', 'mn', 'mo', 'ms', 'mtext', 'noembed', 'noframes', 'noscript', 'plaintext', 'script', 'style', 'svg', 'template', 'thead', 'title', 'video', 'xmp']);\n  /* Tags that are safe for data: URIs */\n\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, ['audio', 'video', 'img', 'source', 'image', 'track']);\n  /* Attributes safe for values like \"javascript:\" */\n\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, ['alt', 'class', 'for', 'id', 'label', 'name', 'pattern', 'placeholder', 'role', 'summary', 'title', 'value', 'style', 'xmlns']);\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n  /* Allowed XHTML+XML namespaces */\n\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE], stringToString);\n  /* Parsing of strict XHTML documents */\n\n  let PARSER_MEDIA_TYPE;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc;\n  /* Keep a reference to config to pass to hooks */\n\n  let CONFIG = null;\n  /* Ideally, do not touch anything below this line */\n\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function isRegexOrFunction(testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n\n\n  const _parseConfig = function _parseConfig(cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n    /* Shield configuration object from tampering */\n\n\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n    /* Shield configuration object from prototype pollution */\n\n\n    cfg = clone(cfg);\n    PARSER_MEDIA_TYPE = // eslint-disable-next-line unicorn/prefer-includes\n    SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE : PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE; // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n\n    transformCaseFunc = PARSER_MEDIA_TYPE === 'application/xhtml+xml' ? stringToString : stringToLowerCase;\n    /* Set configuration parameters */\n\n    ALLOWED_TAGS = 'ALLOWED_TAGS' in cfg ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = 'ALLOWED_ATTR' in cfg ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = 'ALLOWED_NAMESPACES' in cfg ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = 'ADD_URI_SAFE_ATTR' in cfg ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n    cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n    transformCaseFunc // eslint-disable-line indent\n    ) // eslint-disable-line indent\n    : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = 'ADD_DATA_URI_TAGS' in cfg ? addToSet(clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n    cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n    transformCaseFunc // eslint-disable-line indent\n    ) // eslint-disable-line indent\n    : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = 'FORBID_CONTENTS' in cfg ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = 'FORBID_TAGS' in cfg ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : {};\n    FORBID_ATTR = 'FORBID_ATTR' in cfg ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n\n    IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === 'boolean') {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n    /* Parse profile info */\n\n\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...text]);\n      ALLOWED_ATTR = [];\n\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, html$1);\n        addToSet(ALLOWED_ATTR, html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, svg$1);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, svgFilters);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, mathMl$1);\n        addToSet(ALLOWED_ATTR, mathMl);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n    }\n    /* Merge configuration parameters */\n\n\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n    /* Add #text in case KEEP_CONTENT is set to true */\n\n\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n\n\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n\n\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.');\n      }\n\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.');\n      } // Overwrite existing TrustedTypes policy.\n\n\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY; // Sign local variables required by `sanitize`.\n\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, currentScript);\n      } // If creating the internal policy succeeded sign internal variables.\n\n\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    } // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n\n\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, ['mi', 'mo', 'mn', 'ms', 'mtext']);\n  const HTML_INTEGRATION_POINTS = addToSet({}, ['foreignobject', 'desc', 'title', 'annotation-xml']); // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, ['title', 'style', 'font', 'a', 'script']);\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n\n  const ALL_SVG_TAGS = addToSet({}, svg$1);\n  addToSet(ALL_SVG_TAGS, svgFilters);\n  addToSet(ALL_SVG_TAGS, svgDisallowed);\n  const ALL_MATHML_TAGS = addToSet({}, mathMl$1);\n  addToSet(ALL_MATHML_TAGS, mathMlDisallowed);\n  /**\n   *\n   *\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n\n  const _checkValidNamespace = function _checkValidNamespace(element) {\n    let parent = getParentNode(element); // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template'\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      } // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n\n\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return tagName === 'svg' && (parentTagName === 'annotation-xml' || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n      } // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n\n\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      } // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n\n\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      } // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n\n\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      }\n\n      if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      } // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n\n\n      return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n    } // For XHTML and XML documents that support custom namespaces\n\n\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return true;\n    } // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n\n\n    return false;\n  };\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n\n\n  const _forceRemove = function _forceRemove(node) {\n    arrayPush(DOMPurify.removed, {\n      element: node\n    });\n\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      node.remove();\n    }\n  };\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n\n\n  const _removeAttribute = function _removeAttribute(name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node\n      });\n    }\n\n    node.removeAttribute(name); // We void attribute values for unremovable \"is\"\" attributes\n\n    if (name === 'is' && !ALLOWED_ATTR[name]) {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(node);\n        } catch (_) {}\n      } else {\n        try {\n          node.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n\n\n  const _initDocument = function _initDocument(dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && NAMESPACE === HTML_NAMESPACE) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n    /* Use createHTMLDocument in case DOMParser is not available */\n\n\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n      } catch (_) {// Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n    }\n    /* Work on whole document or just its body */\n\n\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n\n\n  const _createIterator = function _createIterator(root) {\n    return createNodeIterator.call(root.ownerDocument || root, root, // eslint-disable-next-line no-bitwise\n    NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT, null, false);\n  };\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n\n\n  const _isClobbered = function _isClobbered(elm) {\n    return elm instanceof HTMLFormElement && (typeof elm.nodeName !== 'string' || typeof elm.textContent !== 'string' || typeof elm.removeChild !== 'function' || !(elm.attributes instanceof NamedNodeMap) || typeof elm.removeAttribute !== 'function' || typeof elm.setAttribute !== 'function' || typeof elm.namespaceURI !== 'string' || typeof elm.insertBefore !== 'function' || typeof elm.hasChildNodes !== 'function');\n  };\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n\n\n  const _isNode = function _isNode(object) {\n    return typeof Node === 'object' ? object instanceof Node : object && typeof object === 'object' && typeof object.nodeType === 'number' && typeof object.nodeName === 'string';\n  };\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n\n\n  const _executeHook = function _executeHook(entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], hook => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n\n\n  const _sanitizeElements = function _sanitizeElements(currentNode) {\n    let content;\n    /* Execute a hook if present */\n\n    _executeHook('beforeSanitizeElements', currentNode, null);\n    /* Check if element is clobbered or can clobber */\n\n\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n\n      return true;\n    }\n    /* Now let's check the element's type and name */\n\n\n    const tagName = transformCaseFunc(currentNode.nodeName);\n    /* Execute a hook if present */\n\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS\n    });\n    /* Detect mXSS attempts abusing namespace confusion */\n\n\n    if (currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && (!_isNode(currentNode.content) || !_isNode(currentNode.content.firstElementChild)) && regExpTest(/<[/\\w]/g, currentNode.innerHTML) && regExpTest(/<[/\\w]/g, currentNode.textContent)) {\n      _forceRemove(currentNode);\n\n      return true;\n    }\n    /* Remove element if anything forbids its presence */\n\n\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) return false;\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) return false;\n      }\n      /* Keep content except for bad-listed elements */\n\n\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            parentNode.insertBefore(cloneNode(childNodes[i], true), getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n\n      return true;\n    }\n    /* Check whether element has a valid namespace */\n\n\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n\n      return true;\n    }\n    /* Make sure that older browsers don't get noscript mXSS */\n\n\n    if ((tagName === 'noscript' || tagName === 'noembed') && regExpTest(/<\\/no(script|embed)/i, currentNode.innerHTML)) {\n      _forceRemove(currentNode);\n\n      return true;\n    }\n    /* Sanitize element content to be template-safe */\n\n\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = stringReplace(content, MUSTACHE_EXPR, ' ');\n      content = stringReplace(content, ERB_EXPR, ' ');\n      content = stringReplace(content, TMPLIT_EXPR, ' ');\n\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, {\n          element: currentNode.cloneNode()\n        });\n        currentNode.textContent = content;\n      }\n    }\n    /* Execute a hook if present */\n\n\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n\n\n  const _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (SANITIZE_DOM && (lcName === 'id' || lcName === 'name') && (value in document || value in formElement)) {\n      return false;\n    }\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n\n\n    if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR, lcName)) ; else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) ; else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if ( // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n      // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n      _basicCustomElementTest(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) || // Alternative, second condition checks if it's an `is`-attribute, AND\n      // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      lcName === 'is' && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ; else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) ; else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE, ''))) ; else if ((lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') && lcTag !== 'script' && stringIndexOf(value, 'data:') === 0 && DATA_URI_TAGS[lcTag]) ; else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))) ; else if (value) {\n      return false;\n    } else ;\n\n    return true;\n  };\n  /**\n   * _basicCustomElementCheck\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   * @param {string} tagName name of the tag of the node to sanitize\n   */\n\n\n  const _basicCustomElementTest = function _basicCustomElementTest(tagName) {\n    return tagName.indexOf('-') > 0;\n  };\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n\n\n  const _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let l;\n    /* Execute a hook if present */\n\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    const {\n      attributes\n    } = currentNode;\n    /* Check if we have attributes; if not we might have a text node */\n\n    if (!attributes) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR\n    };\n    l = attributes.length;\n    /* Go backwards over all attributes; safely remove bad ones */\n\n    while (l--) {\n      attr = attributes[l];\n      const {\n        name,\n        namespaceURI\n      } = attr;\n      value = name === 'value' ? attr.value : stringTrim(attr.value);\n      lcName = transformCaseFunc(name);\n      /* Execute a hook if present */\n\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n\n      value = hookEvent.attrValue;\n      /* Did the hooks approve of the attribute? */\n\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n      /* Remove attribute */\n\n\n      _removeAttribute(name, currentNode);\n      /* Did the hooks approve of the attribute? */\n\n\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n      /* Work around a security issue in jQuery 3.0 */\n\n\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n\n        continue;\n      }\n      /* Sanitize attribute content to be template-safe */\n\n\n      if (SAFE_FOR_TEMPLATES) {\n        value = stringReplace(value, MUSTACHE_EXPR, ' ');\n        value = stringReplace(value, ERB_EXPR, ' ');\n        value = stringReplace(value, TMPLIT_EXPR, ' ');\n      }\n      /* Is `value` valid for this attribute? */\n\n\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n\n\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode); // Prefix the value and later re-create the attribute with the sanitized value\n\n\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n      /* Handle attributes that require Trusted Types */\n\n\n      if (trustedTypesPolicy && typeof trustedTypes === 'object' && typeof trustedTypes.getAttributeType === 'function') {\n        if (namespaceURI) ; else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML':\n              {\n                value = trustedTypesPolicy.createHTML(value);\n                break;\n              }\n\n            case 'TrustedScriptURL':\n              {\n                value = trustedTypesPolicy.createScriptURL(value);\n                break;\n              }\n          }\n        }\n      }\n      /* Handle invalid data-* attribute set by try-catching it */\n\n\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        arrayPop(DOMPurify.removed);\n      } catch (_) {}\n    }\n    /* Execute a hook if present */\n\n\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n\n\n  const _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n    let shadowNode;\n\n    const shadowIterator = _createIterator(fragment);\n    /* Execute a hook if present */\n\n\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while (shadowNode = shadowIterator.nextNode()) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n      /* Sanitize tags and elements */\n\n\n      if (_sanitizeElements(shadowNode)) {\n        continue;\n      }\n      /* Deep shadow DOM detected */\n\n\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n      /* Check attributes, sanitize if necessary */\n\n\n      _sanitizeAttributes(shadowNode);\n    }\n    /* Execute a hook if present */\n\n\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n\n\n  DOMPurify.sanitize = function (dirty) {\n    let cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let body;\n    let importedNode;\n    let currentNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n\n    IS_EMPTY_INPUT = !dirty;\n\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n    /* Stringify, in case dirty is an object */\n\n\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n    /* Return dirty HTML if DOMPurify cannot run */\n\n\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n    /* Assign config vars */\n\n\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n    /* Clean up removed elements */\n\n\n    DOMPurify.removed = [];\n    /* Check if dirty is correctly typed for IN_PLACE */\n\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate('root node is forbidden and cannot be sanitized in-place');\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT && // eslint-disable-next-line unicorn/prefer-includes\n      dirty.indexOf('<') === -1) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n      }\n      /* Initialize the document to work on */\n\n\n      body = _initDocument(dirty);\n      /* Check we have a DOM node from the data */\n\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n    /* Remove first element node (ours) if FORCE_BODY is set */\n\n\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n    /* Get node iterator */\n\n\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n    /* Now start iterating over the created document */\n\n\n    while (currentNode = nodeIterator.nextNode()) {\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(currentNode)) {\n        continue;\n      }\n      /* Shadow DOM detected, sanitize it */\n\n\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n      /* Check attributes, sanitize if necessary */\n\n\n      _sanitizeAttributes(currentNode);\n    }\n    /* If we sanitized `dirty` in-place, return it. */\n\n\n    if (IN_PLACE) {\n      return dirty;\n    }\n    /* Return sanitized string or DOM */\n\n\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n    /* Serialize doctype if allowed */\n\n    if (WHOLE_DOCUMENT && ALLOWED_TAGS['!doctype'] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n      serializedHTML = '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n    /* Sanitize final string template-safe */\n\n\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, ERB_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n  };\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n\n\n  DOMPurify.setConfig = function (cfg) {\n    _parseConfig(cfg);\n\n    SET_CONFIG = true;\n  };\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n\n\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n\n\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n\n\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */\n\n\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      return arrayPop(hooks[entryPoint]);\n    }\n  };\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n\n\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n\n\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nvar purify = createDOMPurify();\n\nexport { purify as default };\n//# sourceMappingURL=purify.es.js.map\n", "import { setContext, getContext } from \"svelte\";\nimport type { WorkerProxy } from \"../src/worker-proxy\";\n\nconst WORKER_PROXY_CONTEXT_KEY = \"WORKER_PROXY_CONTEXT_KEY\";\n\nexport function setWorkerProxyContext(workerProxy: WorkerProxy): void {\n\tsetContext(WORKER_PROXY_CONTEXT_KEY, workerProxy);\n}\n\nexport function getWorkerProxyContext(): WorkerProxy | undefined {\n\treturn getContext(WORKER_PROXY_CONTEXT_KEY);\n}\n", "// src/inject_fonts.ts\nvar inject_fonts = () => {\n  const source_sans_pro = document.createElement(\"link\");\n  source_sans_pro.href = \"https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap\";\n  source_sans_pro.rel = \"stylesheet\";\n  const ibm_mono = document.createElement(\"link\");\n  ibm_mono.href = \"https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;600;700&display=swap\";\n  ibm_mono.rel = \"stylesheet\";\n  document.head.appendChild(source_sans_pro);\n  document.head.appendChild(ibm_mono);\n};\n\n// src/header/components/box.ts\nvar Box = () => {\n  const box = document.createElement(\"div\");\n  box.style.backgroundImage = \"linear-gradient(to top, #f9fafb, white)\";\n  box.style.border = \"1px solid #e5e7eb\";\n  box.style.borderRadius = \"0.75rem\";\n  box.style.boxShadow = \"0 0 10px rgba(0, 0, 0, 0.1)\";\n  box.style.color = \"#374151\";\n  box.style.display = \"flex\";\n  box.style.flexDirection = \"row\";\n  box.style.alignItems = \"center\";\n  box.style.height = \"40px\";\n  box.style.justifyContent = \"space-between\";\n  box.style.overflow = \"hidden\";\n  box.style.position = \"fixed\";\n  box.style.right = \".75rem\";\n  box.style.top = \".75rem\";\n  box.style.width = \"auto\";\n  box.style.zIndex = \"20\";\n  box.style.paddingLeft = \"1rem\";\n  box.setAttribute(\"id\", \"huggingface-space-header\");\n  window.matchMedia(\"(max-width: 768px)\").addEventListener(\"change\", (e) => {\n    if (e.matches) {\n      box.style.display = \"none\";\n    } else {\n      box.style.display = \"flex\";\n    }\n  });\n  return box;\n};\n\n// src/header/components/collapse/arrow.ts\nvar ArrowCollapse = () => {\n  const arrow = document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n  arrow.setAttribute(\"xmlns\", \"http://www.w3.org/2000/svg\");\n  arrow.setAttribute(\"xmlns:link\", \"http://www.w3.org/1999/xlink\");\n  arrow.setAttribute(\"aria-hidden\", \"true\");\n  arrow.setAttribute(\"focusable\", \"false\");\n  arrow.setAttribute(\"role\", \"img\");\n  arrow.setAttribute(\"width\", \"1em\");\n  arrow.setAttribute(\"height\", \"1em\");\n  arrow.setAttribute(\"preserveAspectRatio\", \"xMidYMid meet\");\n  arrow.setAttribute(\"viewBox\", \"0 0 12 12\");\n  arrow.setAttribute(\"fill\", \"currentColor\");\n  const path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n  path.setAttribute(\n    \"d\",\n    \"M0.375001 10.3828L0.375 1.61719C0.375 1.104 0.816001 0.687501 1.35938 0.687501L10.6406 0.6875C10.9017 0.6875 11.1521 0.785449 11.3367 0.959797C11.5213 1.13415 11.625 1.37062 11.625 1.61719V10.3828C11.625 10.6294 11.5213 10.8659 11.3367 11.0402C11.1521 11.2145 10.9017 11.3125 10.6406 11.3125H1.35938C0.816001 11.3125 0.375001 10.896 0.375001 10.3828ZM1.35938 10.5156H10.6406C10.7183 10.5156 10.7813 10.4561 10.7813 10.3828V4.40625H1.21875V10.3828C1.21875 10.418 1.23356 10.4518 1.25994 10.4767C1.28631 10.5017 1.32208 10.5156 1.35938 10.5156ZM4.61052 6.38251L5.9999 7.69472L7.38927 6.38251C7.44083 6.33007 7.50645 6.29173 7.57913 6.27153C7.6518 6.25134 7.72898 6.25003 7.8024 6.26776C7.87583 6.28549 7.9428 6.3216 7.99628 6.37227C8.04983 6.42295 8.08785 6.48631 8.10645 6.5557C8.12528 6.62497 8.12393 6.69773 8.10263 6.76635C8.0814 6.83497 8.0409 6.8969 7.98555 6.94564L6.29802 8.53936C6.21892 8.61399 6.11169 8.65592 5.9999 8.65592C5.8881 8.65592 5.78087 8.61399 5.70177 8.53936L4.01427 6.94564C3.95874 6.89694 3.91814 6.835 3.89676 6.76633C3.87538 6.69766 3.874 6.62483 3.89277 6.55549C3.91154 6.48615 3.94977 6.42287 4.00343 6.37233C4.05708 6.32179 4.12418 6.28585 4.19765 6.2683C4.27098 6.25054 4.34803 6.25178 4.42068 6.27188C4.49334 6.29198 4.55891 6.3302 4.61052 6.38251Z\"\n  );\n  arrow.appendChild(path);\n  return arrow;\n};\n\n// src/header/components/collapse/index.ts\nvar Collapse = (space, callback) => {\n  const box = document.createElement(\"div\");\n  box.setAttribute(\"id\", \"space-header__collapse\");\n  box.style.display = \"flex\";\n  box.style.flexDirection = \"row\";\n  box.style.alignItems = \"center\";\n  box.style.justifyContent = \"center\";\n  box.style.fontSize = \"16px\";\n  box.style.paddingLeft = \"10px\";\n  box.style.paddingRight = \"10px\";\n  box.style.height = \"40px\";\n  box.style.cursor = \"pointer\";\n  box.style.color = \"#40546e\";\n  box.style.transitionDuration = \"0.1s\";\n  box.style.transitionProperty = \"all\";\n  box.style.transitionTimingFunction = \"ease-in-out\";\n  box.appendChild(ArrowCollapse());\n  box.addEventListener(\"click\", (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    callback();\n  });\n  box.addEventListener(\"mouseenter\", () => {\n    box.style.color = \"#213551\";\n  });\n  box.addEventListener(\"mouseleave\", () => {\n    box.style.color = \"#40546e\";\n  });\n  return box;\n};\n\n// src/header/components/like/count.ts\nvar Count = (count) => {\n  const text = document.createElement(\"p\");\n  text.style.margin = \"0\";\n  text.style.padding = \"0\";\n  text.style.color = \"#9ca3af\";\n  text.style.fontSize = \"14px\";\n  text.style.fontFamily = \"Source Sans Pro, sans-serif\";\n  text.style.padding = \"0px 6px\";\n  text.style.borderLeft = \"1px solid #e5e7eb\";\n  text.style.marginLeft = \"4px\";\n  text.textContent = (count != null ? count : 0).toString();\n  return text;\n};\n\n// src/header/components/like/heart.ts\nvar Heart = () => {\n  const heart = document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n  heart.setAttribute(\"xmlns\", \"http://www.w3.org/2000/svg\");\n  heart.setAttribute(\"xmlns:link\", \"http://www.w3.org/1999/xlink\");\n  heart.setAttribute(\"aria-hidden\", \"true\");\n  heart.setAttribute(\"focusable\", \"false\");\n  heart.setAttribute(\"role\", \"img\");\n  heart.setAttribute(\"width\", \"1em\");\n  heart.setAttribute(\"height\", \"1em\");\n  heart.setAttribute(\"preserveAspectRatio\", \"xMidYMid meet\");\n  heart.setAttribute(\"viewBox\", \"0 0 32 32\");\n  heart.setAttribute(\"fill\", \"#6b7280\");\n  const path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n  path.setAttribute(\n    \"d\",\n    \"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"\n  );\n  heart.appendChild(path);\n  return heart;\n};\n\n// src/header/components/like/index.ts\nvar Like = (space) => {\n  const box = document.createElement(\"a\");\n  box.setAttribute(\"href\", `https://huggingface.co/spaces/${space.id}`);\n  box.setAttribute(\"rel\", \"noopener noreferrer\");\n  box.setAttribute(\"target\", \"_blank\");\n  box.style.border = \"1px solid #e5e7eb\";\n  box.style.borderRadius = \"6px\";\n  box.style.display = \"flex\";\n  box.style.flexDirection = \"row\";\n  box.style.alignItems = \"center\";\n  box.style.margin = \"0 0 0 12px\";\n  box.style.fontSize = \"14px\";\n  box.style.paddingLeft = \"4px\";\n  box.style.textDecoration = \"none\";\n  box.appendChild(Heart());\n  box.appendChild(Count(space.likes));\n  return box;\n};\n\n// src/header/components/content/avatar.ts\nvar Avatar = (username) => {\n  const element = document.createElement(\"img\");\n  element.src = `https://huggingface.co/api/users/${username}/avatar`;\n  element.style.width = \"0.875rem\";\n  element.style.height = \"0.875rem\";\n  element.style.borderRadius = \"50%\";\n  element.style.flex = \"none\";\n  element.style.marginRight = \"0.375rem\";\n  return element;\n};\n\n// src/header/components/content/namespace.ts\nvar Namespace = (id) => {\n  const [_, spaceName] = id.split(\"/\");\n  const element = document.createElement(\"a\");\n  element.setAttribute(\"href\", `https://huggingface.co/spaces/${id}`);\n  element.setAttribute(\"rel\", \"noopener noreferrer\");\n  element.setAttribute(\"target\", \"_blank\");\n  element.style.color = \"#1f2937\";\n  element.style.textDecoration = \"none\";\n  element.style.fontWeight = \"600\";\n  element.style.fontSize = \"15px\";\n  element.style.lineHeight = \"24px\";\n  element.style.flex = \"none\";\n  element.style.fontFamily = \"IBM Plex Mono, sans-serif\";\n  element.addEventListener(\"mouseover\", () => {\n    element.style.color = \"#2563eb\";\n  });\n  element.addEventListener(\"mouseout\", () => {\n    element.style.color = \"#1f2937\";\n  });\n  element.textContent = spaceName;\n  return element;\n};\n\n// src/header/components/content/separation.ts\nvar Separation = () => {\n  const separation = document.createElement(\"div\");\n  separation.style.marginLeft = \".125rem\";\n  separation.style.marginRight = \".125rem\";\n  separation.style.color = \"#d1d5db\";\n  separation.textContent = \"/\";\n  return separation;\n};\n\n// src/header/components/content/username.ts\nvar Username = (username) => {\n  const element = document.createElement(\"a\");\n  element.setAttribute(\"href\", `https://huggingface.co/${username}`);\n  element.setAttribute(\"rel\", \"noopener noreferrer\");\n  element.setAttribute(\"target\", \"_blank\");\n  element.style.color = \"rgb(107, 114, 128)\";\n  element.style.textDecoration = \"none\";\n  element.style.fontWeight = \"400\";\n  element.style.fontSize = \"16px\";\n  element.style.lineHeight = \"24px\";\n  element.style.flex = \"none\";\n  element.style.fontFamily = \"Source Sans Pro, sans-serif\";\n  element.addEventListener(\"mouseover\", () => {\n    element.style.color = \"#2563eb\";\n  });\n  element.addEventListener(\"mouseout\", () => {\n    element.style.color = \"rgb(107, 114, 128)\";\n  });\n  element.textContent = username;\n  return element;\n};\n\n// src/header/components/content/index.ts\nvar Content = (space) => {\n  const content = document.createElement(\"div\");\n  content.style.display = \"flex\";\n  content.style.flexDirection = \"row\";\n  content.style.alignItems = \"center\";\n  content.style.justifyContent = \"center\";\n  content.style.borderRight = \"1px solid #e5e7eb\";\n  content.style.paddingRight = \"12px\";\n  content.style.height = \"40px\";\n  content.appendChild(Avatar(space.author));\n  content.appendChild(Username(space.author));\n  content.appendChild(Separation());\n  content.appendChild(Namespace(space.id));\n  content.appendChild(Like(space));\n  return content;\n};\n\n// src/header/create.ts\nvar create = (space) => {\n  const box = Box();\n  const handleCollapse = () => box.style.display = \"none\";\n  box.appendChild(Content(space));\n  box.appendChild(Collapse(space, handleCollapse));\n  return box;\n};\n\n// src/get_space.ts\nvar get_space = async (space_id) => {\n  try {\n    const response = await fetch(`https://huggingface.co/api/spaces/${space_id}`);\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    return null;\n  }\n};\n\n// src/inject.ts\nvar inject = (element, options) => {\n  if (options == null ? void 0 : options.target) {\n    if (options.target.appendChild) {\n      options.target.appendChild(element);\n      return;\n    }\n    return console.error(\"the target element does not have an appendChild method\");\n  }\n  if (document.body === null) {\n    return console.error(\"document.body is null\");\n  }\n  document.body.appendChild(element);\n};\n\n// src/index.ts\nasync function main(initialSpace, options) {\n  var _a, _b;\n  if (window === void 0) return console.error(\"Please run this script in a browser environment\");\n  const has_huggingface_ancestor = Object.values(\n    (_b = (_a = window.location) == null ? void 0 : _a.ancestorOrigins) != null ? _b : {\n      0: window.document.referrer\n    }\n  ).some((origin) => {\n    var _a2;\n    return ((_a2 = new URL(origin)) == null ? void 0 : _a2.origin) === \"https://huggingface.co\";\n  });\n  if (has_huggingface_ancestor) return;\n  inject_fonts();\n  let space;\n  if (typeof initialSpace === \"string\") {\n    space = await get_space(initialSpace);\n    if (space === null) return console.error(\"Space not found\");\n  } else {\n    space = initialSpace;\n  }\n  const mini_header_element = create(space);\n  inject(mini_header_element, options);\n  return {\n    element: mini_header_element\n  };\n}\nvar init = (space, options) => main(space, options);\nexport {\n  init\n};\n", "<script context=\"module\" lang=\"ts\">\n\timport { writable } from \"svelte/store\";\n\timport { mount_css as default_mount_css, prefix_css } from \"@gradio/core\";\n\n\timport type { Client as ClientType } from \"@gradio/client\";\n\n\timport type { ComponentMeta, Dependency, LayoutNode } from \"@gradio/core\";\n\n\tdeclare let BUILD_MODE: string;\n\tinterface Config {\n\t\tauth_required?: true;\n\t\tauth_message: string;\n\t\tcomponents: ComponentMeta[];\n\t\tcss: string | null;\n\t\tjs: string | null;\n\t\thead: string | null;\n\t\tdependencies: Dependency[];\n\t\tdev_mode: boolean;\n\t\tenable_queue: boolean;\n\t\tlayout: LayoutNode;\n\t\tmode: \"blocks\" | \"interface\";\n\t\troot: string;\n\t\ttheme: string;\n\t\ttitle: string;\n\t\tversion: string;\n\t\tspace_id: string | null;\n\t\tis_colab: boolean;\n\t\tshow_api: boolean;\n\t\tstylesheets?: string[];\n\t\tpath: string;\n\t\tapp_id?: string;\n\t\tfill_height?: boolean;\n\t\tfill_width?: boolean;\n\t\ttheme_hash?: number;\n\t\tusername: string | null;\n\t}\n\n\tlet id = -1;\n\n\tfunction create_intersection_store(): {\n\t\tregister: (n: number, el: HTMLDivElement) => void;\n\t\tsubscribe: (typeof intersecting)[\"subscribe\"];\n\t} {\n\t\tconst intersecting = writable<Record<string, boolean>>({});\n\n\t\tconst els = new Map<HTMLDivElement, number>();\n\n\t\tconst observer = new IntersectionObserver((entries) => {\n\t\t\tentries.forEach((entry) => {\n\t\t\t\tif (entry.isIntersecting) {\n\t\t\t\t\tlet _el: number | undefined = els.get(entry.target as HTMLDivElement);\n\t\t\t\t\tif (_el !== undefined)\n\t\t\t\t\t\tintersecting.update((s) => ({ ...s, [_el as number]: true }));\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\n\t\tfunction register(_id: number, el: HTMLDivElement): void {\n\t\t\tels.set(el, _id);\n\t\t\tobserver.observe(el);\n\t\t}\n\n\t\treturn { register, subscribe: intersecting.subscribe };\n\t}\n\n\tconst intersecting = create_intersection_store();\n</script>\n\n<script lang=\"ts\">\n\timport { onMount, createEventDispatcher, onDestroy } from \"svelte\";\n\timport type { SpaceStatus } from \"@gradio/client\";\n\timport { Embed } from \"@gradio/core\";\n\timport type { ThemeMode } from \"@gradio/core\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\timport { setupi18n } from \"@gradio/core\";\n\timport type { WorkerProxy } from \"@gradio/wasm\";\n\timport { setWorkerProxyContext } from \"@gradio/wasm/svelte\";\n\timport { init } from \"@huggingface/space-header\";\n\n\tsetupi18n();\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let autoscroll: boolean;\n\texport let version: string;\n\texport let initial_height: string;\n\texport let app_mode: boolean;\n\texport let is_embed: boolean;\n\texport let theme_mode: ThemeMode | null = \"system\";\n\texport let control_page_title: boolean;\n\texport let container: boolean;\n\texport let info: boolean;\n\texport let eager: boolean;\n\tlet stream: EventSource;\n\n\t// These utilities are exported to be injectable for the Wasm version.\n\texport let mount_css: typeof default_mount_css = default_mount_css;\n\texport let Client: typeof ClientType;\n\texport let worker_proxy: WorkerProxy | undefined = undefined;\n\tif (worker_proxy) {\n\t\tsetWorkerProxyContext(worker_proxy);\n\n\t\tworker_proxy.addEventListener(\"progress-update\", (event) => {\n\t\t\tloading_text = (event as CustomEvent).detail + \"...\";\n\t\t});\n\t}\n\n\texport let space: string | null;\n\texport let host: string | null;\n\texport let src: string | null;\n\n\tlet _id = id++;\n\n\tlet loader_status: \"pending\" | \"error\" | \"complete\" | \"generating\" =\n\t\t\"pending\";\n\tlet app_id: string | null = null;\n\tlet wrapper: HTMLDivElement;\n\tlet ready = false;\n\tlet render_complete = false;\n\tlet config: Config;\n\tlet loading_text = $_(\"common.loading\") + \"...\";\n\tlet active_theme_mode: ThemeMode;\n\tlet api_url: string;\n\n\t$: if (config?.app_id) {\n\t\tapp_id = config.app_id;\n\t}\n\n\tlet css_text_stylesheet: HTMLStyleElement | null = null;\n\tasync function mount_custom_css(css_string: string | null): Promise<void> {\n\t\tif (css_string) {\n\t\t\tcss_text_stylesheet = prefix_css(\n\t\t\t\tcss_string,\n\t\t\t\tversion,\n\t\t\t\tcss_text_stylesheet || undefined\n\t\t\t);\n\t\t}\n\t\tawait mount_css(\n\t\t\tconfig.root + \"/theme.css?v=\" + config.theme_hash,\n\t\t\tdocument.head\n\t\t);\n\t\tif (!config.stylesheets) return;\n\n\t\tawait Promise.all(\n\t\t\tconfig.stylesheets.map((stylesheet) => {\n\t\t\t\tlet absolute_link =\n\t\t\t\t\tstylesheet.startsWith(\"http:\") || stylesheet.startsWith(\"https:\");\n\t\t\t\tif (absolute_link) {\n\t\t\t\t\treturn mount_css(stylesheet, document.head);\n\t\t\t\t}\n\n\t\t\t\treturn fetch(config.root + \"/\" + stylesheet)\n\t\t\t\t\t.then((response) => response.text())\n\t\t\t\t\t.then((css_string) => {\n\t\t\t\t\t\tprefix_css(css_string, version);\n\t\t\t\t\t});\n\t\t\t})\n\t\t);\n\t}\n\tasync function add_custom_html_head(\n\t\thead_string: string | null\n\t): Promise<void> {\n\t\tif (head_string) {\n\t\t\tconst parser = new DOMParser();\n\t\t\tconst parsed_head_html = Array.from(\n\t\t\t\tparser.parseFromString(head_string, \"text/html\").head.children\n\t\t\t);\n\n\t\t\tif (parsed_head_html) {\n\t\t\t\tfor (let head_element of parsed_head_html) {\n\t\t\t\t\tlet newElement = document.createElement(head_element.tagName);\n\t\t\t\t\tArray.from(head_element.attributes).forEach((attr) => {\n\t\t\t\t\t\tnewElement.setAttribute(attr.name, attr.value);\n\t\t\t\t\t});\n\t\t\t\t\tnewElement.textContent = head_element.textContent;\n\n\t\t\t\t\tif (\n\t\t\t\t\t\tnewElement.tagName == \"META\" &&\n\t\t\t\t\t\tnewElement.getAttribute(\"property\")\n\t\t\t\t\t) {\n\t\t\t\t\t\tconst domMetaList = Array.from(\n\t\t\t\t\t\t\tdocument.head.getElementsByTagName(\"meta\") ?? []\n\t\t\t\t\t\t);\n\t\t\t\t\t\tconst matched = domMetaList.find((el) => {\n\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\tel.getAttribute(\"property\") ==\n\t\t\t\t\t\t\t\t\tnewElement.getAttribute(\"property\") &&\n\t\t\t\t\t\t\t\t!el.isEqualNode(newElement)\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif (matched) {\n\t\t\t\t\t\t\tdocument.head.replaceChild(newElement, matched);\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tdocument.head.appendChild(newElement);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction handle_theme_mode(target: HTMLDivElement): \"light\" | \"dark\" {\n\t\tconst force_light = window.__gradio_mode__ === \"website\";\n\n\t\tlet new_theme_mode: ThemeMode;\n\t\tif (force_light) {\n\t\t\tnew_theme_mode = \"light\";\n\t\t} else {\n\t\t\tconst url = new URL(window.location.toString());\n\t\t\tconst url_color_mode: ThemeMode | null = url.searchParams.get(\n\t\t\t\t\"__theme\"\n\t\t\t) as ThemeMode | null;\n\t\t\tnew_theme_mode = theme_mode || url_color_mode || \"system\";\n\t\t}\n\n\t\tif (new_theme_mode === \"dark\" || new_theme_mode === \"light\") {\n\t\t\tapply_theme(target, new_theme_mode);\n\t\t} else {\n\t\t\tnew_theme_mode = sync_system_theme(target);\n\t\t}\n\t\treturn new_theme_mode;\n\t}\n\n\tfunction sync_system_theme(target: HTMLDivElement): \"light\" | \"dark\" {\n\t\tconst theme = update_scheme();\n\t\twindow\n\t\t\t?.matchMedia(\"(prefers-color-scheme: dark)\")\n\t\t\t?.addEventListener(\"change\", update_scheme);\n\n\t\tfunction update_scheme(): \"light\" | \"dark\" {\n\t\t\tlet _theme: \"light\" | \"dark\" = window?.matchMedia?.(\n\t\t\t\t\"(prefers-color-scheme: dark)\"\n\t\t\t).matches\n\t\t\t\t? \"dark\"\n\t\t\t\t: \"light\";\n\n\t\t\tapply_theme(target, _theme);\n\t\t\treturn _theme;\n\t\t}\n\t\treturn theme;\n\t}\n\n\tfunction apply_theme(target: HTMLDivElement, theme: \"dark\" | \"light\"): void {\n\t\tconst dark_class_element = is_embed ? target.parentElement! : document.body;\n\t\tconst bg_element = is_embed ? target : target.parentElement!;\n\t\tbg_element.style.background = \"var(--body-background-fill)\";\n\t\tif (theme === \"dark\") {\n\t\t\tdark_class_element.classList.add(\"dark\");\n\t\t} else {\n\t\t\tdark_class_element.classList.remove(\"dark\");\n\t\t}\n\t}\n\n\tlet status: SpaceStatus = {\n\t\tmessage: \"\",\n\t\tload_status: \"pending\",\n\t\tstatus: \"sleeping\",\n\t\tdetail: \"SLEEPING\"\n\t};\n\n\tlet app: ClientType;\n\tlet css_ready = false;\n\tfunction handle_status(_status: SpaceStatus): void {\n\t\tstatus = _status;\n\t}\n\t//@ts-ignore\n\tconst gradio_dev_mode = window.__GRADIO_DEV__;\n\n\tonMount(async () => {\n\t\tactive_theme_mode = handle_theme_mode(wrapper);\n\n\t\t//@ts-ignore\n\t\tconst server_port = window.__GRADIO__SERVER_PORT__;\n\n\t\tapi_url =\n\t\t\tBUILD_MODE === \"dev\" || gradio_dev_mode === \"dev\"\n\t\t\t\t? `http://localhost:${\n\t\t\t\t\t\ttypeof server_port === \"number\" ? server_port : 7860\n\t\t\t\t\t}`\n\t\t\t\t: host || space || src || location.origin;\n\n\t\tapp = await Client.connect(api_url, {\n\t\t\tstatus_callback: handle_status,\n\t\t\twith_null_state: true,\n\t\t\tevents: [\"data\", \"log\", \"status\", \"render\"]\n\t\t});\n\n\t\tif (!app.config) {\n\t\t\tthrow new Error(\"Could not resolve app config\");\n\t\t}\n\n\t\tconfig = app.config;\n\t\twindow.__gradio_space__ = config.space_id;\n\n\t\tstatus = {\n\t\t\tmessage: \"\",\n\t\t\tload_status: \"complete\",\n\t\t\tstatus: \"running\",\n\t\t\tdetail: \"RUNNING\"\n\t\t};\n\n\t\tawait mount_custom_css(config.css);\n\t\tawait add_custom_html_head(config.head);\n\t\tcss_ready = true;\n\t\twindow.__is_colab__ = config.is_colab;\n\n\t\tdispatch(\"loaded\");\n\n\t\tif (config.dev_mode) {\n\t\t\tsetTimeout(() => {\n\t\t\t\tconst { host } = new URL(api_url);\n\t\t\t\tlet url = new URL(`http://${host}/dev/reload`);\n\t\t\t\tstream = new EventSource(url);\n\t\t\t\tstream.addEventListener(\"error\", async (e) => {\n\t\t\t\t\tnew_message_fn(\"Error reloading app\", \"error\");\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tconsole.error(JSON.parse(e.data));\n\t\t\t\t});\n\t\t\t\tstream.addEventListener(\"reload\", async (event) => {\n\t\t\t\t\tapp.close();\n\t\t\t\t\tapp = await Client.connect(api_url, {\n\t\t\t\t\t\tstatus_callback: handle_status,\n\t\t\t\t\t\twith_null_state: true,\n\t\t\t\t\t\tevents: [\"data\", \"log\", \"status\", \"render\"]\n\t\t\t\t\t});\n\n\t\t\t\t\tif (!app.config) {\n\t\t\t\t\t\tthrow new Error(\"Could not resolve app config\");\n\t\t\t\t\t}\n\n\t\t\t\t\tconfig = app.config;\n\t\t\t\t\twindow.__gradio_space__ = config.space_id;\n\t\t\t\t\tawait mount_custom_css(config.css);\n\t\t\t\t});\n\t\t\t}, 200);\n\t\t}\n\t});\n\n\t$: loader_status =\n\t\t!ready && status.load_status !== \"error\"\n\t\t\t? \"pending\"\n\t\t\t: !ready && status.load_status === \"error\"\n\t\t\t\t? \"error\"\n\t\t\t\t: status.load_status;\n\n\t$: config && (eager || $intersecting[_id]) && load_demo();\n\n\tlet Blocks: typeof import(\"@gradio/core/blocks\").default;\n\n\tlet Login: typeof import(\"@gradio/core/login\").default;\n\n\tasync function get_blocks(): Promise<void> {\n\t\tBlocks = (await import(\"@gradio/core/blocks\")).default;\n\t}\n\tasync function get_login(): Promise<void> {\n\t\tLogin = (await import(\"@gradio/core/login\")).default;\n\t}\n\n\tfunction load_demo(): void {\n\t\tif (config.auth_required) get_login();\n\t\telse get_blocks();\n\t}\n\n\ttype error_types =\n\t\t| \"NO_APP_FILE\"\n\t\t| \"CONFIG_ERROR\"\n\t\t| \"BUILD_ERROR\"\n\t\t| \"RUNTIME_ERROR\"\n\t\t| \"PAUSED\";\n\n\t// todo @hannahblair: translate these messages\n\tconst discussion_message = {\n\t\treadable_error: {\n\t\t\tNO_APP_FILE: $_(\"errors.no_app_file\"),\n\t\t\tCONFIG_ERROR: $_(\"errors.config_error\"),\n\t\t\tBUILD_ERROR: $_(\"errors.build_error\"),\n\t\t\tRUNTIME_ERROR: $_(\"errors.runtime_error\"),\n\t\t\tPAUSED: $_(\"errors.space_paused\")\n\t\t} as const,\n\t\ttitle(error: error_types): string {\n\t\t\treturn encodeURIComponent($_(\"errors.space_not_working\"));\n\t\t},\n\t\tdescription(error: error_types, site: string): string {\n\t\t\treturn encodeURIComponent(\n\t\t\t\t`Hello,\\n\\nFirstly, thanks for creating this space!\\n\\nI noticed that the space isn't working correctly because there is ${\n\t\t\t\t\tthis.readable_error[error] || \"an error\"\n\t\t\t\t}.\\n\\nIt would be great if you could take a look at this because this space is being embedded on ${site}.\\n\\nThanks!`\n\t\t\t);\n\t\t}\n\t};\n\n\tlet new_message_fn: (message: string, type: string) => void;\n\n\tonMount(async () => {\n\t\tintersecting.register(_id, wrapper);\n\t});\n\n\t$: if (render_complete) {\n\t\twrapper.dispatchEvent(\n\t\t\tnew CustomEvent(\"render\", {\n\t\t\t\tbubbles: true,\n\t\t\t\tcancelable: false,\n\t\t\t\tcomposed: true\n\t\t\t})\n\t\t);\n\t}\n\n\t$: app?.config && mount_space_header(app?.config?.space_id, is_embed);\n\tlet spaceheader: HTMLElement | undefined;\n\n\tasync function mount_space_header(\n\t\tspace_id: string | null | undefined,\n\t\tis_embed: boolean\n\t): Promise<void> {\n\t\tif (space_id && !is_embed && window.self === window.top) {\n\t\t\tif (spaceheader) {\n\t\t\t\tspaceheader.remove();\n\t\t\t\tspaceheader = undefined;\n\t\t\t}\n\t\t\tconst header = await init(space_id);\n\t\t\tif (header) spaceheader = header.element;\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tspaceheader?.remove();\n\t});\n</script>\n\n<Embed\n\tdisplay={container && is_embed}\n\t{is_embed}\n\tinfo={!!space && info}\n\t{version}\n\t{initial_height}\n\t{space}\n\tloaded={loader_status === \"complete\"}\n\tfill_width={config?.fill_width || false}\n\tbind:wrapper\n>\n\t{#if (loader_status === \"pending\" || loader_status === \"error\") && !(config && config?.auth_required)}\n\t\t<StatusTracker\n\t\t\tabsolute={!is_embed}\n\t\t\tstatus={loader_status}\n\t\t\ttimer={false}\n\t\t\tqueue_position={null}\n\t\t\tqueue_size={null}\n\t\t\ttranslucent={true}\n\t\t\t{loading_text}\n\t\t\ti18n={$_}\n\t\t\t{autoscroll}\n\t\t>\n\t\t\t<div class=\"load-text\" slot=\"additional-loading-text\">\n\t\t\t\t{#if gradio_dev_mode === \"dev\"}\n\t\t\t\t\t<p>\n\t\t\t\t\t\tIf your custom component never loads, consult the troubleshooting <a\n\t\t\t\t\t\t\tstyle=\"color: blue;\"\n\t\t\t\t\t\t\thref=\"https://www.gradio.app/guides/frequently-asked-questions#the-development-server-didnt-work-for-me\"\n\t\t\t\t\t\t\t>guide</a\n\t\t\t\t\t\t>.\n\t\t\t\t\t</p>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t\t<!-- todo: translate message text -->\n\t\t\t<div class=\"error\" slot=\"error\">\n\t\t\t\t<p><strong>{status?.message || \"\"}</strong></p>\n\t\t\t\t{#if (status.status === \"space_error\" || status.status === \"paused\") && status.discussions_enabled}\n\t\t\t\t\t<p>\n\t\t\t\t\t\tPlease <a\n\t\t\t\t\t\t\thref=\"https://huggingface.co/spaces/{space}/discussions/new?title={discussion_message.title(\n\t\t\t\t\t\t\t\tstatus?.detail\n\t\t\t\t\t\t\t)}&description={discussion_message.description(\n\t\t\t\t\t\t\t\tstatus?.detail,\n\t\t\t\t\t\t\t\tlocation.origin\n\t\t\t\t\t\t\t)}\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tcontact the author of the space</a\n\t\t\t\t\t\t> to let them know.\n\t\t\t\t\t</p>\n\t\t\t\t{:else}\n\t\t\t\t\t<p>{$_(\"errors.contact_page_author\")}</p>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</StatusTracker>\n\t{/if}\n\t{#if config?.auth_required && Login}\n\t\t<Login\n\t\t\tauth_message={config.auth_message}\n\t\t\troot={config.root}\n\t\t\tspace_id={space}\n\t\t\t{app_mode}\n\t\t/>\n\t{:else if config && Blocks && css_ready}\n\t\t<Blocks\n\t\t\t{app}\n\t\t\t{...config}\n\t\t\tfill_height={!is_embed && config.fill_height}\n\t\t\ttheme_mode={active_theme_mode}\n\t\t\t{control_page_title}\n\t\t\ttarget={wrapper}\n\t\t\t{autoscroll}\n\t\t\tbind:ready\n\t\t\tbind:render_complete\n\t\t\tbind:add_new_message={new_message_fn}\n\t\t\tshow_footer={!is_embed}\n\t\t\t{app_mode}\n\t\t\t{version}\n\t\t/>\n\t{/if}\n</Embed>\n\n<style>\n\t.error {\n\t\tposition: relative;\n\t\tpadding: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t\ttext-align: center;\n\t}\n\n\t.error > * {\n\t\tmargin-top: var(--size-4);\n\t}\n\n\ta {\n\t\tcolor: var(--link-text-color);\n\t}\n\n\ta:hover {\n\t\tcolor: var(--link-text-color-hover);\n\t\ttext-decoration: underline;\n\t}\n\n\ta:visited {\n\t\tcolor: var(--link-text-color-visited);\n\t}\n\n\ta:active {\n\t\tcolor: var(--link-text-color-active);\n\t}\n</style>\n"], "file": "assets/Index-DB1XLvMK.js"}
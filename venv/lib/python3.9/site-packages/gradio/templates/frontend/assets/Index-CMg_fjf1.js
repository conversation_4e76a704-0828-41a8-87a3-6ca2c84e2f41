const __vite__fileDeps=["./Canvas3D-DWif4Rvt.js","./index-BQPjLIsY.js","./index-BOW2xVAS.css","./file-url-SIRImsEF.js","./Index-DB1XLvMK.js","./Index-BEyjvDG_.css","./Canvas3DGS-CrpHBNp3.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as ae}from"./index-BQPjLIsY.js";import{I as Ie,S as Se}from"./Index-DB1XLvMK.js";import{B as pe}from"./BlockLabel-BlSr62f_.js";import{D as Te}from"./Download-DVtk-Jv3.js";import{F as re}from"./File-BQ_9P3Ye.js";import{U as Pe}from"./Undo-CpmTQw3B.js";import{U as Ve}from"./Upload-1-QDTAlg.js";import{M as Re}from"./ModifyUpload-CEEIIKhx.js";import{B as Ee}from"./Button-BIUaXfcG.js";import{E as Ge}from"./Empty-BgF7sXBn.js";import{U as We}from"./UploadText-CCg0GCB-.js";import{default as on}from"./Example-BQyGztrG.js";import"./svelte/svelte.js";/* empty css                                                   */import"./DownloadLink-CHpWw1Ex.js";import"./file-url-SIRImsEF.js";import"./Upload-Cp8Go_XF.js";var $e=Object.prototype.hasOwnProperty;function ve(l,e,n){for(n of l.keys())if(J(n,e))return n}function J(l,e){var n,t,s;if(l===e)return!0;if(l&&e&&(n=l.constructor)===e.constructor){if(n===Date)return l.getTime()===e.getTime();if(n===RegExp)return l.toString()===e.toString();if(n===Array){if((t=l.length)===e.length)for(;t--&&J(l[t],e[t]););return t===-1}if(n===Set){if(l.size!==e.size)return!1;for(t of l)if(s=t,s&&typeof s=="object"&&(s=ve(e,s),!s)||!e.has(s))return!1;return!0}if(n===Map){if(l.size!==e.size)return!1;for(t of l)if(s=t[0],s&&typeof s=="object"&&(s=ve(e,s),!s)||!J(t[1],e.get(s)))return!1;return!0}if(n===ArrayBuffer)l=new Uint8Array(l),e=new Uint8Array(e);else if(n===DataView){if((t=l.byteLength)===e.byteLength)for(;t--&&l.getInt8(t)===e.getInt8(t););return t===-1}if(ArrayBuffer.isView(l)){if((t=l.byteLength)===e.byteLength)for(;t--&&l[t]===e[t];);return t===-1}if(!n||typeof l=="object"){t=0;for(n in l)if($e.call(l,n)&&++t&&!$e.call(e,n)||!(n in e)||!J(l[n],e[n]))return!1;return Object.keys(e).length===t}}return l!==l&&e!==e}const{SvelteComponent:Fe,add_flush_callback:Me,append:le,attr:O,bind:se,binding_callbacks:K,check_outros:Q,construct_svelte_component:ie,create_component:T,destroy_component:P,detach:X,element:he,empty:be,flush:A,group_outros:Y,init:ye,insert:Z,mount_component:V,noop:He,safe_not_equal:Je,space:ge,transition_in:k,transition_out:I}=window.__gradio__svelte__internal;function ze(l){let e,n,t,s,a,o,_,i,u,r,f=!l[9]&&De(l);a=new Ie({props:{Icon:Te,label:l[5]("common.download")}});const c=[Qe,Ke],h=[];function D(p,b){return p[9]?0:1}return i=D(l),u=h[i]=c[i](l),{c(){e=he("div"),n=he("div"),f&&f.c(),t=ge(),s=he("a"),T(a.$$.fragment),_=ge(),u.c(),O(s,"href",l[13]),O(s,"target",window.__is_colab__?"_blank":null),O(s,"download",o=window.__is_colab__?null:l[0].orig_name||l[0].path),O(n,"class","buttons svelte-1yz3qf4"),O(e,"class","model3D svelte-1yz3qf4")},m(p,b){Z(p,e,b),le(e,n),f&&f.m(n,null),le(n,t),le(n,s),V(a,s,null),le(e,_),h[i].m(e,null),r=!0},p(p,b){p[9]?f&&(Y(),I(f,1,1,()=>{f=null}),Q()):f?(f.p(p,b),b&512&&k(f,1)):(f=De(p),f.c(),k(f,1),f.m(n,t));const M={};b&32&&(M.label=p[5]("common.download")),a.$set(M),(!r||b&8192)&&O(s,"href",p[13]),(!r||b&1&&o!==(o=window.__is_colab__?null:p[0].orig_name||p[0].path))&&O(s,"download",o);let $=i;i=D(p),i===$?h[i].p(p,b):(Y(),I(h[$],1,1,()=>{h[$]=null}),Q(),u=h[i],u?u.p(p,b):(u=h[i]=c[i](p),u.c()),k(u,1),u.m(e,null))},i(p){r||(k(f),k(a.$$.fragment,p),k(u),r=!0)},o(p){I(f),I(a.$$.fragment,p),I(u),r=!1},d(p){p&&X(e),f&&f.d(),P(a),h[i].d()}}}function De(l){let e,n;return e=new Ie({props:{Icon:Pe,label:"Undo"}}),e.$on("click",l[16]),{c(){T(e.$$.fragment)},m(t,s){V(e,t,s),n=!0},p:He,i(t){n||(k(e.$$.fragment,t),n=!0)},o(t){I(e.$$.fragment,t),n=!1},d(t){P(e,t)}}}function Ke(l){let e,n,t,s;function a(i){l[19](i)}var o=l[12];function _(i,u){let r={value:i[0],display_mode:i[1],clear_color:i[2],camera_position:i[8],zoom_speed:i[6],pan_speed:i[7]};return i[13]!==void 0&&(r.resolved_url=i[13]),{props:r}}return o&&(e=ie(o,_(l)),l[18](e),K.push(()=>se(e,"resolved_url",a))),{c(){e&&T(e.$$.fragment),t=be()},m(i,u){e&&V(e,i,u),Z(i,t,u),s=!0},p(i,u){if(u&4096&&o!==(o=i[12])){if(e){Y();const r=e;I(r.$$.fragment,1,0,()=>{P(r,1)}),Q()}o?(e=ie(o,_(i)),i[18](e),K.push(()=>se(e,"resolved_url",a)),T(e.$$.fragment),k(e.$$.fragment,1),V(e,t.parentNode,t)):e=null}else if(o){const r={};u&1&&(r.value=i[0]),u&2&&(r.display_mode=i[1]),u&4&&(r.clear_color=i[2]),u&256&&(r.camera_position=i[8]),u&64&&(r.zoom_speed=i[6]),u&128&&(r.pan_speed=i[7]),!n&&u&8192&&(n=!0,r.resolved_url=i[13],Me(()=>n=!1)),e.$set(r)}},i(i){s||(e&&k(e.$$.fragment,i),s=!0)},o(i){e&&I(e.$$.fragment,i),s=!1},d(i){i&&X(t),l[18](null),e&&P(e,i)}}}function Qe(l){let e,n,t,s;function a(i){l[17](i)}var o=l[11];function _(i,u){let r={value:i[0],zoom_speed:i[6],pan_speed:i[7]};return i[13]!==void 0&&(r.resolved_url=i[13]),{props:r}}return o&&(e=ie(o,_(l)),K.push(()=>se(e,"resolved_url",a))),{c(){e&&T(e.$$.fragment),t=be()},m(i,u){e&&V(e,i,u),Z(i,t,u),s=!0},p(i,u){if(u&2048&&o!==(o=i[11])){if(e){Y();const r=e;I(r.$$.fragment,1,0,()=>{P(r,1)}),Q()}o?(e=ie(o,_(i)),K.push(()=>se(e,"resolved_url",a)),T(e.$$.fragment),k(e.$$.fragment,1),V(e,t.parentNode,t)):e=null}else if(o){const r={};u&1&&(r.value=i[0]),u&64&&(r.zoom_speed=i[6]),u&128&&(r.pan_speed=i[7]),!n&&u&8192&&(n=!0,r.resolved_url=i[13],Me(()=>n=!1)),e.$set(r)}},i(i){s||(e&&k(e.$$.fragment,i),s=!0)},o(i){e&&I(e.$$.fragment,i),s=!1},d(i){i&&X(t),e&&P(e,i)}}}function Xe(l){let e,n,t,s;e=new pe({props:{show_label:l[4],Icon:re,label:l[3]||l[5]("3D_model.3d_model")}});let a=l[0]&&ze(l);return{c(){T(e.$$.fragment),n=ge(),a&&a.c(),t=be()},m(o,_){V(e,o,_),Z(o,n,_),a&&a.m(o,_),Z(o,t,_),s=!0},p(o,[_]){const i={};_&16&&(i.show_label=o[4]),_&40&&(i.label=o[3]||o[5]("3D_model.3d_model")),e.$set(i),o[0]?a?(a.p(o,_),_&1&&k(a,1)):(a=ze(o),a.c(),k(a,1),a.m(t.parentNode,t)):a&&(Y(),I(a,1,1,()=>{a=null}),Q())},i(o){s||(k(e.$$.fragment,o),k(a),s=!0)},o(o){I(e.$$.fragment,o),I(a),s=!1},d(o){o&&(X(n),X(t)),P(e,o),a&&a.d(o)}}}async function Ye(){return(await ae(()=>import("./Canvas3D-DWif4Rvt.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).default}async function Ze(){return(await ae(()=>import("./Canvas3DGS-CrpHBNp3.js"),__vite__mapDeps([6,3,4,1,2,5]),import.meta.url)).default}function xe(l,e,n){let{value:t}=e,{display_mode:s="solid"}=e,{clear_color:a=[0,0,0,0]}=e,{label:o=""}=e,{show_label:_}=e,{i18n:i}=e,{zoom_speed:u=1}=e,{pan_speed:r=1}=e,{camera_position:f=[null,null,null]}=e,c={camera_position:f,zoom_speed:u,pan_speed:r},h=!1,D,p,b;function M(){b?.reset_camera_position(f,u,r)}let $;const F=()=>M();function L(g){$=g,n(13,$)}function q(g){K[g?"unshift":"push"](()=>{b=g,n(10,b)})}function y(g){$=g,n(13,$)}return l.$$set=g=>{"value"in g&&n(0,t=g.value),"display_mode"in g&&n(1,s=g.display_mode),"clear_color"in g&&n(2,a=g.clear_color),"label"in g&&n(3,o=g.label),"show_label"in g&&n(4,_=g.show_label),"i18n"in g&&n(5,i=g.i18n),"zoom_speed"in g&&n(6,u=g.zoom_speed),"pan_speed"in g&&n(7,r=g.pan_speed),"camera_position"in g&&n(8,f=g.camera_position)},l.$$.update=()=>{l.$$.dirty&513&&t&&(n(9,h=t.path.endsWith(".splat")||t.path.endsWith(".ply")),h?Ze().then(g=>{n(11,D=g)}):Ye().then(g=>{n(12,p=g)})),l.$$.dirty&34240&&(!J(c.camera_position,f)||c.zoom_speed!==u||c.pan_speed!==r)&&(b?.reset_camera_position(f,u,r),n(15,c={camera_position:f,zoom_speed:u,pan_speed:r}))},[t,s,a,o,_,i,u,r,f,h,b,D,p,$,M,c,F,L,q,y]}class et extends Fe{constructor(e){super(),ye(this,e,xe,Xe,Je,{value:0,display_mode:1,clear_color:2,label:3,show_label:4,i18n:5,zoom_speed:6,pan_speed:7,camera_position:8})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),A()}get display_mode(){return this.$$.ctx[1]}set display_mode(e){this.$$set({display_mode:e}),A()}get clear_color(){return this.$$.ctx[2]}set clear_color(e){this.$$set({clear_color:e}),A()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),A()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),A()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),A()}get zoom_speed(){return this.$$.ctx[6]}set zoom_speed(e){this.$$set({zoom_speed:e}),A()}get pan_speed(){return this.$$.ctx[7]}set pan_speed(e){this.$$set({pan_speed:e}),A()}get camera_position(){return this.$$.ctx[8]}set camera_position(e){this.$$set({camera_position:e}),A()}}const tt=et,{SvelteComponent:nt,add_flush_callback:lt,append:st,attr:it,bind:ot,binding_callbacks:Ue,bubble:at,check_outros:_e,construct_svelte_component:oe,create_component:R,create_slot:rt,destroy_component:G,detach:x,element:_t,empty:we,flush:C,get_all_dirty_from_scope:ut,get_slot_changes:ft,group_outros:ue,init:ct,insert:ee,mount_component:W,safe_not_equal:mt,space:je,transition_in:S,transition_out:E,update_slot_base:dt}=window.__gradio__svelte__internal,{createEventDispatcher:ht,tick:Ce}=window.__gradio__svelte__internal;function gt(l){let e,n,t,s,a,o;n=new Re({props:{undoable:!l[13],i18n:l[6],absolute:!0}}),n.$on("clear",l[19]),n.$on("undo",l[20]);const _=[wt,bt],i=[];function u(r,f){return r[13]?0:1}return s=u(l),a=i[s]=_[s](l),{c(){e=_t("div"),R(n.$$.fragment),t=je(),a.c(),it(e,"class","input-model svelte-jub4pj")},m(r,f){ee(r,e,f),W(n,e,null),st(e,t),i[s].m(e,null),o=!0},p(r,f){const c={};f&8192&&(c.undoable=!r[13]),f&64&&(c.i18n=r[6]),n.$set(c);let h=s;s=u(r),s===h?i[s].p(r,f):(ue(),E(i[h],1,1,()=>{i[h]=null}),_e(),a=i[s],a?a.p(r,f):(a=i[s]=_[s](r),a.c()),S(a,1),a.m(e,null))},i(r){o||(S(n.$$.fragment,r),S(a),o=!0)},o(r){E(n.$$.fragment,r),E(a),o=!1},d(r){r&&x(e),G(n),i[s].d()}}}function pt(l){let e,n,t;function s(o){l[22](o)}let a={upload:l[11],stream_handler:l[12],root:l[5],max_file_size:l[9],filetype:[".stl",".obj",".gltf",".glb","model/obj",".splat",".ply"],$$slots:{default:[kt]},$$scope:{ctx:l}};return l[14]!==void 0&&(a.dragging=l[14]),e=new Ve({props:a}),Ue.push(()=>ot(e,"dragging",s)),e.$on("load",l[18]),e.$on("error",l[23]),{c(){R(e.$$.fragment)},m(o,_){W(e,o,_),t=!0},p(o,_){const i={};_&2048&&(i.upload=o[11]),_&4096&&(i.stream_handler=o[12]),_&32&&(i.root=o[5]),_&512&&(i.max_file_size=o[9]),_&33554432&&(i.$$scope={dirty:_,ctx:o}),!n&&_&16384&&(n=!0,i.dragging=o[14],lt(()=>n=!1)),e.$set(i)},i(o){t||(S(e.$$.fragment,o),t=!0)},o(o){E(e.$$.fragment,o),t=!1},d(o){G(e,o)}}}function bt(l){let e,n,t;var s=l[16];function a(o,_){return{props:{value:o[0],display_mode:o[1],clear_color:o[2],camera_position:o[10],zoom_speed:o[7],pan_speed:o[8]}}}return s&&(e=oe(s,a(l)),l[24](e)),{c(){e&&R(e.$$.fragment),n=we()},m(o,_){e&&W(e,o,_),ee(o,n,_),t=!0},p(o,_){if(_&65536&&s!==(s=o[16])){if(e){ue();const i=e;E(i.$$.fragment,1,0,()=>{G(i,1)}),_e()}s?(e=oe(s,a(o)),o[24](e),R(e.$$.fragment),S(e.$$.fragment,1),W(e,n.parentNode,n)):e=null}else if(s){const i={};_&1&&(i.value=o[0]),_&2&&(i.display_mode=o[1]),_&4&&(i.clear_color=o[2]),_&1024&&(i.camera_position=o[10]),_&128&&(i.zoom_speed=o[7]),_&256&&(i.pan_speed=o[8]),e.$set(i)}},i(o){t||(e&&S(e.$$.fragment,o),t=!0)},o(o){e&&E(e.$$.fragment,o),t=!1},d(o){o&&x(n),l[24](null),e&&G(e,o)}}}function wt(l){let e,n,t;var s=l[15];function a(o,_){return{props:{value:o[0],zoom_speed:o[7],pan_speed:o[8]}}}return s&&(e=oe(s,a(l))),{c(){e&&R(e.$$.fragment),n=we()},m(o,_){e&&W(e,o,_),ee(o,n,_),t=!0},p(o,_){if(_&32768&&s!==(s=o[15])){if(e){ue();const i=e;E(i.$$.fragment,1,0,()=>{G(i,1)}),_e()}s?(e=oe(s,a(o)),R(e.$$.fragment),S(e.$$.fragment,1),W(e,n.parentNode,n)):e=null}else if(s){const i={};_&1&&(i.value=o[0]),_&128&&(i.zoom_speed=o[7]),_&256&&(i.pan_speed=o[8]),e.$set(i)}},i(o){t||(e&&S(e.$$.fragment,o),t=!0)},o(o){e&&E(e.$$.fragment,o),t=!1},d(o){o&&x(n),e&&G(e,o)}}}function kt(l){let e;const n=l[21].default,t=rt(n,l,l[25],null);return{c(){t&&t.c()},m(s,a){t&&t.m(s,a),e=!0},p(s,a){t&&t.p&&(!e||a&33554432)&&dt(t,n,s,s[25],e?ft(n,s[25],a,null):ut(s[25]),null)},i(s){e||(S(t,s),e=!0)},o(s){E(t,s),e=!1},d(s){t&&t.d(s)}}}function $t(l){let e,n,t,s,a,o;e=new pe({props:{show_label:l[4],Icon:re,label:l[3]||"3D Model"}});const _=[pt,gt],i=[];function u(r,f){return r[0]===null?0:1}return t=u(l),s=i[t]=_[t](l),{c(){R(e.$$.fragment),n=je(),s.c(),a=we()},m(r,f){W(e,r,f),ee(r,n,f),i[t].m(r,f),ee(r,a,f),o=!0},p(r,[f]){const c={};f&16&&(c.show_label=r[4]),f&8&&(c.label=r[3]||"3D Model"),e.$set(c);let h=t;t=u(r),t===h?i[t].p(r,f):(ue(),E(i[h],1,1,()=>{i[h]=null}),_e(),s=i[t],s?s.p(r,f):(s=i[t]=_[t](r),s.c()),S(s,1),s.m(a.parentNode,a))},i(r){o||(S(e.$$.fragment,r),S(s),o=!0)},o(r){E(e.$$.fragment,r),E(s),o=!1},d(r){r&&(x(n),x(a)),G(e,r),i[t].d(r)}}}async function vt(){return(await ae(()=>import("./Canvas3D-DWif4Rvt.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).default}async function zt(){return(await ae(()=>import("./Canvas3DGS-CrpHBNp3.js"),__vite__mapDeps([6,3,4,1,2,5]),import.meta.url)).default}function Dt(l,e,n){let{$$slots:t={},$$scope:s}=e,{value:a}=e,{display_mode:o="solid"}=e,{clear_color:_=[0,0,0,0]}=e,{label:i=""}=e,{show_label:u}=e,{root:r}=e,{i18n:f}=e,{zoom_speed:c=1}=e,{pan_speed:h=1}=e,{max_file_size:D=null}=e,{camera_position:p=[null,null,null]}=e,{upload:b}=e,{stream_handler:M}=e;async function $({detail:d}){n(0,a=d),await Ce(),N("change",a),N("load",a)}async function F(){n(0,a=null),await Ce(),N("clear"),N("change")}let L=!1,q,y,g;async function fe(){g?.reset_camera_position(p,c,h)}const N=ht();let H=!1;function ce(d){H=d,n(14,H)}function me(d){at.call(this,l,d)}function de(d){Ue[d?"unshift":"push"](()=>{g=d,n(17,g)})}return l.$$set=d=>{"value"in d&&n(0,a=d.value),"display_mode"in d&&n(1,o=d.display_mode),"clear_color"in d&&n(2,_=d.clear_color),"label"in d&&n(3,i=d.label),"show_label"in d&&n(4,u=d.show_label),"root"in d&&n(5,r=d.root),"i18n"in d&&n(6,f=d.i18n),"zoom_speed"in d&&n(7,c=d.zoom_speed),"pan_speed"in d&&n(8,h=d.pan_speed),"max_file_size"in d&&n(9,D=d.max_file_size),"camera_position"in d&&n(10,p=d.camera_position),"upload"in d&&n(11,b=d.upload),"stream_handler"in d&&n(12,M=d.stream_handler),"$$scope"in d&&n(25,s=d.$$scope)},l.$$.update=()=>{l.$$.dirty&8193&&a&&(n(13,L=a.path.endsWith(".splat")||a.path.endsWith(".ply")),L?zt().then(d=>{n(15,q=d)}):vt().then(d=>{n(16,y=d)})),l.$$.dirty&16384&&N("drag",H)},[a,o,_,i,u,r,f,c,h,D,p,b,M,L,H,q,y,g,$,F,fe,t,ce,me,de,s]}class Ct extends nt{constructor(e){super(),ct(this,e,Dt,$t,mt,{value:0,display_mode:1,clear_color:2,label:3,show_label:4,root:5,i18n:6,zoom_speed:7,pan_speed:8,max_file_size:9,camera_position:10,upload:11,stream_handler:12})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),C()}get display_mode(){return this.$$.ctx[1]}set display_mode(e){this.$$set({display_mode:e}),C()}get clear_color(){return this.$$.ctx[2]}set clear_color(e){this.$$set({clear_color:e}),C()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),C()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),C()}get root(){return this.$$.ctx[5]}set root(e){this.$$set({root:e}),C()}get i18n(){return this.$$.ctx[6]}set i18n(e){this.$$set({i18n:e}),C()}get zoom_speed(){return this.$$.ctx[7]}set zoom_speed(e){this.$$set({zoom_speed:e}),C()}get pan_speed(){return this.$$.ctx[8]}set pan_speed(e){this.$$set({pan_speed:e}),C()}get max_file_size(){return this.$$.ctx[9]}set max_file_size(e){this.$$set({max_file_size:e}),C()}get camera_position(){return this.$$.ctx[10]}set camera_position(e){this.$$set({camera_position:e}),C()}get upload(){return this.$$.ctx[11]}set upload(e){this.$$set({upload:e}),C()}get stream_handler(){return this.$$.ctx[12]}set stream_handler(e){this.$$set({stream_handler:e}),C()}}const It=Ct,{SvelteComponent:St,assign:Be,check_outros:Ae,create_component:U,destroy_component:j,detach:te,empty:Le,flush:w,get_spread_object:qe,get_spread_update:Ne,group_outros:Oe,init:Et,insert:ne,mount_component:B,safe_not_equal:Mt,space:ke,transition_in:v,transition_out:z}=window.__gradio__svelte__internal;function Ut(l){let e,n;return e=new Ee({props:{visible:l[4],variant:l[0]===null?"dashed":"solid",border_mode:l[18]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],container:l[10],scale:l[11],min_width:l[12],height:l[14],$$slots:{default:[At]},$$scope:{ctx:l}}}),{c(){U(e.$$.fragment)},m(t,s){B(e,t,s),n=!0},p(t,s){const a={};s&16&&(a.visible=t[4]),s&1&&(a.variant=t[0]===null?"dashed":"solid"),s&262144&&(a.border_mode=t[18]?"focus":"base"),s&4&&(a.elem_id=t[2]),s&8&&(a.elem_classes=t[3]),s&1024&&(a.container=t[10]),s&2048&&(a.scale=t[11]),s&4096&&(a.min_width=t[12]),s&16384&&(a.height=t[14]),s&268805091&&(a.$$scope={dirty:s,ctx:t}),e.$set(a)},i(t){n||(v(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){j(e,t)}}}function jt(l){let e,n;return e=new Ee({props:{visible:l[4],variant:l[0]===null?"dashed":"solid",border_mode:l[18]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],container:l[10],scale:l[11],min_width:l[12],height:l[14],$$slots:{default:[Ot]},$$scope:{ctx:l}}}),{c(){U(e.$$.fragment)},m(t,s){B(e,t,s),n=!0},p(t,s){const a={};s&16&&(a.visible=t[4]),s&1&&(a.variant=t[0]===null?"dashed":"solid"),s&262144&&(a.border_mode=t[18]?"focus":"base"),s&4&&(a.elem_id=t[2]),s&8&&(a.elem_classes=t[3]),s&1024&&(a.container=t[10]),s&2048&&(a.scale=t[11]),s&4096&&(a.min_width=t[12]),s&16384&&(a.height=t[14]),s&268542915&&(a.$$scope={dirty:s,ctx:t}),e.$set(a)},i(t){n||(v(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){j(e,t)}}}function Bt(l){let e,n;return e=new We({props:{i18n:l[13].i18n,type:"file"}}),{c(){U(e.$$.fragment)},m(t,s){B(e,t,s),n=!0},p(t,s){const a={};s&8192&&(a.i18n=t[13].i18n),e.$set(a)},i(t){n||(v(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){j(e,t)}}}function At(l){let e,n,t,s;const a=[{autoscroll:l[13].autoscroll},{i18n:l[13].i18n},l[1]];let o={};for(let _=0;_<a.length;_+=1)o=Be(o,a[_]);return e=new Se({props:o}),e.$on("clear_status",l[21]),t=new It({props:{label:l[8],show_label:l[9],root:l[5],display_mode:l[6],clear_color:l[7],value:l[0],camera_position:l[16],zoom_speed:l[15],i18n:l[13].i18n,max_file_size:l[13].max_file_size,upload:l[13].client.upload,stream_handler:l[13].client.stream,$$slots:{default:[Bt]},$$scope:{ctx:l}}}),t.$on("change",l[22]),t.$on("drag",l[23]),t.$on("change",l[24]),t.$on("clear",l[25]),t.$on("load",l[26]),t.$on("error",l[27]),{c(){U(e.$$.fragment),n=ke(),U(t.$$.fragment)},m(_,i){B(e,_,i),ne(_,n,i),B(t,_,i),s=!0},p(_,i){const u=i&8194?Ne(a,[i&8192&&{autoscroll:_[13].autoscroll},i&8192&&{i18n:_[13].i18n},i&2&&qe(_[1])]):{};e.$set(u);const r={};i&256&&(r.label=_[8]),i&512&&(r.show_label=_[9]),i&32&&(r.root=_[5]),i&64&&(r.display_mode=_[6]),i&128&&(r.clear_color=_[7]),i&1&&(r.value=_[0]),i&65536&&(r.camera_position=_[16]),i&32768&&(r.zoom_speed=_[15]),i&8192&&(r.i18n=_[13].i18n),i&8192&&(r.max_file_size=_[13].max_file_size),i&8192&&(r.upload=_[13].client.upload),i&8192&&(r.stream_handler=_[13].client.stream),i&268443648&&(r.$$scope={dirty:i,ctx:_}),t.$set(r)},i(_){s||(v(e.$$.fragment,_),v(t.$$.fragment,_),s=!0)},o(_){z(e.$$.fragment,_),z(t.$$.fragment,_),s=!1},d(_){_&&te(n),j(e,_),j(t,_)}}}function Lt(l){let e,n,t,s;return e=new pe({props:{show_label:l[9],Icon:re,label:l[8]||"3D Model"}}),t=new Ge({props:{unpadded_box:!0,size:"large",$$slots:{default:[Nt]},$$scope:{ctx:l}}}),{c(){U(e.$$.fragment),n=ke(),U(t.$$.fragment)},m(a,o){B(e,a,o),ne(a,n,o),B(t,a,o),s=!0},p(a,o){const _={};o&512&&(_.show_label=a[9]),o&256&&(_.label=a[8]||"3D Model"),e.$set(_);const i={};o&268435456&&(i.$$scope={dirty:o,ctx:a}),t.$set(i)},i(a){s||(v(e.$$.fragment,a),v(t.$$.fragment,a),s=!0)},o(a){z(e.$$.fragment,a),z(t.$$.fragment,a),s=!1},d(a){a&&te(n),j(e,a),j(t,a)}}}function qt(l){let e,n;return e=new tt({props:{value:l[0],i18n:l[13].i18n,display_mode:l[6],clear_color:l[7],label:l[8],show_label:l[9],camera_position:l[16],zoom_speed:l[15]}}),{c(){U(e.$$.fragment)},m(t,s){B(e,t,s),n=!0},p(t,s){const a={};s&1&&(a.value=t[0]),s&8192&&(a.i18n=t[13].i18n),s&64&&(a.display_mode=t[6]),s&128&&(a.clear_color=t[7]),s&256&&(a.label=t[8]),s&512&&(a.show_label=t[9]),s&65536&&(a.camera_position=t[16]),s&32768&&(a.zoom_speed=t[15]),e.$set(a)},i(t){n||(v(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){j(e,t)}}}function Nt(l){let e,n;return e=new re({}),{c(){U(e.$$.fragment)},m(t,s){B(e,t,s),n=!0},i(t){n||(v(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){j(e,t)}}}function Ot(l){let e,n,t,s,a,o;const _=[{autoscroll:l[13].autoscroll},{i18n:l[13].i18n},l[1]];let i={};for(let c=0;c<_.length;c+=1)i=Be(i,_[c]);e=new Se({props:i}),e.$on("clear_status",l[20]);const u=[qt,Lt],r=[];function f(c,h){return c[0]&&c[19]?0:1}return t=f(l),s=r[t]=u[t](l),{c(){U(e.$$.fragment),n=ke(),s.c(),a=Le()},m(c,h){B(e,c,h),ne(c,n,h),r[t].m(c,h),ne(c,a,h),o=!0},p(c,h){const D=h&8194?Ne(_,[h&8192&&{autoscroll:c[13].autoscroll},h&8192&&{i18n:c[13].i18n},h&2&&qe(c[1])]):{};e.$set(D);let p=t;t=f(c),t===p?r[t].p(c,h):(Oe(),z(r[p],1,1,()=>{r[p]=null}),Ae(),s=r[t],s?s.p(c,h):(s=r[t]=u[t](c),s.c()),v(s,1),s.m(a.parentNode,a))},i(c){o||(v(e.$$.fragment,c),v(s),o=!0)},o(c){z(e.$$.fragment,c),z(s),o=!1},d(c){c&&(te(n),te(a)),j(e,c),r[t].d(c)}}}function Tt(l){let e,n,t,s;const a=[jt,Ut],o=[];function _(i,u){return i[17]?1:0}return e=_(l),n=o[e]=a[e](l),{c(){n.c(),t=Le()},m(i,u){o[e].m(i,u),ne(i,t,u),s=!0},p(i,[u]){let r=e;e=_(i),e===r?o[e].p(i,u):(Oe(),z(o[r],1,1,()=>{o[r]=null}),Ae(),n=o[e],n?n.p(i,u):(n=o[e]=a[e](i),n.c()),v(n,1),n.m(t.parentNode,t))},i(i){s||(v(n),s=!0)},o(i){z(n),s=!1},d(i){i&&te(t),o[e].d(i)}}}function Pt(l,e,n){let{elem_id:t=""}=e,{elem_classes:s=[]}=e,{visible:a=!0}=e,{value:o=null}=e,{root:_}=e,{display_mode:i="solid"}=e,{clear_color:u}=e,{loading_status:r}=e,{label:f}=e,{show_label:c}=e,{container:h=!0}=e,{scale:D=null}=e,{min_width:p=void 0}=e,{gradio:b}=e,{height:M=void 0}=e,{zoom_speed:$=1}=e,{camera_position:F=[null,null,null]}=e,{interactive:L}=e,q=!1;const y=typeof window<"u",g=()=>b.dispatch("clear_status",r),fe=()=>b.dispatch("clear_status",r),N=({detail:m})=>n(0,o=m),H=({detail:m})=>n(18,q=m),ce=({detail:m})=>b.dispatch("change",m),me=()=>{n(0,o=null),b.dispatch("clear")},de=({detail:m})=>{n(0,o=m),b.dispatch("upload")},d=({detail:m})=>{n(1,r=r||{}),n(1,r.status="error",r),b.dispatch("error",m)};return l.$$set=m=>{"elem_id"in m&&n(2,t=m.elem_id),"elem_classes"in m&&n(3,s=m.elem_classes),"visible"in m&&n(4,a=m.visible),"value"in m&&n(0,o=m.value),"root"in m&&n(5,_=m.root),"display_mode"in m&&n(6,i=m.display_mode),"clear_color"in m&&n(7,u=m.clear_color),"loading_status"in m&&n(1,r=m.loading_status),"label"in m&&n(8,f=m.label),"show_label"in m&&n(9,c=m.show_label),"container"in m&&n(10,h=m.container),"scale"in m&&n(11,D=m.scale),"min_width"in m&&n(12,p=m.min_width),"gradio"in m&&n(13,b=m.gradio),"height"in m&&n(14,M=m.height),"zoom_speed"in m&&n(15,$=m.zoom_speed),"camera_position"in m&&n(16,F=m.camera_position),"interactive"in m&&n(17,L=m.interactive)},[o,r,t,s,a,_,i,u,f,c,h,D,p,b,M,$,F,L,q,y,g,fe,N,H,ce,me,de,d]}class nn extends St{constructor(e){super(),Et(this,e,Pt,Tt,Mt,{elem_id:2,elem_classes:3,visible:4,value:0,root:5,display_mode:6,clear_color:7,loading_status:1,label:8,show_label:9,container:10,scale:11,min_width:12,gradio:13,height:14,zoom_speed:15,camera_position:16,interactive:17})}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),w()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),w()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),w()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),w()}get root(){return this.$$.ctx[5]}set root(e){this.$$set({root:e}),w()}get display_mode(){return this.$$.ctx[6]}set display_mode(e){this.$$set({display_mode:e}),w()}get clear_color(){return this.$$.ctx[7]}set clear_color(e){this.$$set({clear_color:e}),w()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),w()}get label(){return this.$$.ctx[8]}set label(e){this.$$set({label:e}),w()}get show_label(){return this.$$.ctx[9]}set show_label(e){this.$$set({show_label:e}),w()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),w()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),w()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),w()}get gradio(){return this.$$.ctx[13]}set gradio(e){this.$$set({gradio:e}),w()}get height(){return this.$$.ctx[14]}set height(e){this.$$set({height:e}),w()}get zoom_speed(){return this.$$.ctx[15]}set zoom_speed(e){this.$$set({zoom_speed:e}),w()}get camera_position(){return this.$$.ctx[16]}set camera_position(e){this.$$set({camera_position:e}),w()}get interactive(){return this.$$.ctx[17]}set interactive(e){this.$$set({interactive:e}),w()}}export{on as BaseExample,tt as BaseModel3D,It as BaseModel3DUpload,nn as default};
//# sourceMappingURL=Index-CMg_fjf1.js.map

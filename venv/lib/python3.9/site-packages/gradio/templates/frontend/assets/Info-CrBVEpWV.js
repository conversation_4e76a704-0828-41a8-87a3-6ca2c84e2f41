import"./Index-DB1XLvMK.js";const{SvelteComponent:r,attr:a,create_slot:_,detach:u,element:f,get_all_dirty_from_scope:c,get_slot_changes:d,init:p,insert:m,safe_not_equal:$,transition_in:v,transition_out:g,update_slot_base:h}=window.__gradio__svelte__internal;function w(l){let n,s;const o=l[1].default,e=_(o,l,l[0],null);return{c(){n=f("div"),e&&e.c(),a(n,"class","svelte-e8n7p6")},m(t,i){m(t,n,i),e&&e.m(n,null),s=!0},p(t,[i]){e&&e.p&&(!s||i&1)&&h(e,o,t,t[0],s?d(o,t[0],i,null):c(t[0]),null)},i(t){s||(v(e,t),s=!0)},o(t){g(e,t),s=!1},d(t){t&&u(n),e&&e.d(t)}}}function I(l,n,s){let{$$slots:o={},$$scope:e}=n;return l.$$set=t=>{"$$scope"in t&&s(0,e=t.$$scope)},[e,o]}class q extends r{constructor(n){super(),p(this,n,I,w,$,{})}}export{q as I};
//# sourceMappingURL=Info-CrBVEpWV.js.map

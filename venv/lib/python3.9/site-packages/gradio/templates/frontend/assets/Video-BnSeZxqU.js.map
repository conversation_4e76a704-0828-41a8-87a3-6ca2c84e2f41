{"version": 3, "file": "Video-BnSeZxqU.js", "sources": ["../../../../node_modules/.pnpm/@ffmpeg+util@0.12.1_patch_hash=x7vlbpaf44zdrkz5rus4uvldii/node_modules/@ffmpeg/util/dist/esm/errors.js", "../../../../node_modules/.pnpm/@ffmpeg+util@0.12.1_patch_hash=x7vlbpaf44zdrkz5rus4uvldii/node_modules/@ffmpeg/util/dist/esm/const.js", "../../../../node_modules/.pnpm/@ffmpeg+util@0.12.1_patch_hash=x7vlbpaf44zdrkz5rus4uvldii/node_modules/@ffmpeg/util/dist/esm/index.js", "../../../../node_modules/.pnpm/@ffmpeg+ffmpeg@0.12.7/node_modules/@ffmpeg/ffmpeg/dist/esm/const.js", "../../../../node_modules/.pnpm/@ffmpeg+ffmpeg@0.12.7/node_modules/@ffmpeg/ffmpeg/dist/esm/utils.js", "../../../../node_modules/.pnpm/@ffmpeg+ffmpeg@0.12.7/node_modules/@ffmpeg/ffmpeg/dist/esm/errors.js", "../../../../node_modules/.pnpm/@ffmpeg+ffmpeg@0.12.7/node_modules/@ffmpeg/ffmpeg/dist/esm/classes.js", "../../../../node_modules/.pnpm/mrmime@2.0.0/node_modules/mrmime/index.mjs", "../../../../js/video/shared/utils.ts", "../../../../js/video/shared/Video.svelte"], "sourcesContent": ["export const ERROR_RESPONSE_BODY_READER = new Error(\"failed to get response body reader\");\nexport const ERROR_INCOMPLETED_DOWNLOAD = new Error(\"failed to complete download\");\n", "export const HeaderContentLength = \"Content-Length\";\n", "import { ERROR_RESPONSE_BODY_READER, ERROR_INCOMPLETED_DOWNLOAD, } from \"./errors.js\";\nimport { HeaderContentLength } from \"./const.js\";\nconst readFromBlobOrFile = (blob) => new Promise((resolve, reject) => {\n    const fileReader = new FileReader();\n    fileReader.onload = () => {\n        const { result } = fileReader;\n        if (result instanceof ArrayBuffer) {\n            resolve(new Uint8Array(result));\n        }\n        else {\n            resolve(new Uint8Array());\n        }\n    };\n    fileReader.onerror = (event) => {\n        reject(Error(`File could not be read! Code=${event?.target?.error?.code || -1}`));\n    };\n    fileReader.readAsArrayBuffer(blob);\n});\n/**\n * An util function to fetch data from url string, base64, URL, File or Blob format.\n *\n * Examples:\n * ```ts\n * // URL\n * await fetchFile(\"http://localhost:3000/video.mp4\");\n * // base64\n * await fetchFile(\"data:<type>;base64,wL2dvYWwgbW9yZ...\");\n * // URL\n * await fetchFile(new URL(\"video.mp4\", import.meta.url));\n * // File\n * fileInput.addEventListener('change', (e) => {\n *   await fetchFile(e.target.files[0]);\n * });\n * // Blob\n * const blob = new Blob(...);\n * await fetchFile(blob);\n * ```\n */\nexport const fetchFile = async (file) => {\n    let data;\n    if (typeof file === \"string\") {\n        /* From base64 format */\n        if (/data:_data\\/([a-zA-Z]*);base64,([^\"]*)/.test(file)) {\n            data = atob(file.split(\",\")[1])\n                .split(\"\")\n                .map((c) => c.charCodeAt(0));\n            /* From remote server/URL */\n        }\n        else {\n            data = await (await fetch(file)).arrayBuffer();\n        }\n    }\n    else if (file instanceof URL) {\n        data = await (await fetch(file)).arrayBuffer();\n    }\n    else if (file instanceof File || file instanceof Blob) {\n        data = await readFromBlobOrFile(file);\n    }\n    else {\n        return new Uint8Array();\n    }\n    return new Uint8Array(data);\n};\n/**\n * importScript dynamically import a script, useful when you\n * want to use different versions of ffmpeg.wasm based on environment.\n *\n * Example:\n *\n * ```ts\n * await importScript(\"http://localhost:3000/ffmpeg.js\");\n * ```\n */\nexport const importScript = async (url) => new Promise((resolve) => {\n    const script = document.createElement(\"script\");\n    const eventHandler = () => {\n        script.removeEventListener(\"load\", eventHandler);\n        resolve();\n    };\n    script.src = url;\n    script.type = \"text/javascript\";\n    script.addEventListener(\"load\", eventHandler);\n    document.getElementsByTagName(\"head\")[0].appendChild(script);\n});\n/**\n * Download content of a URL with progress.\n *\n * Progress only works when Content-Length is provided by the server.\n *\n */\nexport const downloadWithProgress = async (url, cb) => {\n    const resp = await fetch(url);\n    let buf;\n    try {\n        // Set total to -1 to indicate that there is not Content-Type Header.\n        const total = parseInt(resp.headers.get(HeaderContentLength) || \"-1\");\n        const reader = resp.body?.getReader();\n        if (!reader)\n            throw ERROR_RESPONSE_BODY_READER;\n        const chunks = [];\n        let received = 0;\n        for (;;) {\n            const { done, value } = await reader.read();\n            const delta = value ? value.length : 0;\n            if (done) {\n                if (total != -1 && total !== received)\n                    throw ERROR_INCOMPLETED_DOWNLOAD;\n                cb && cb({ url, total, received, delta, done });\n                break;\n            }\n            chunks.push(value);\n            received += delta;\n            cb && cb({ url, total, received, delta, done });\n        }\n        const data = new Uint8Array(received);\n        let position = 0;\n        for (const chunk of chunks) {\n            data.set(chunk, position);\n            position += chunk.length;\n        }\n        buf = data.buffer;\n    }\n    catch (e) {\n        console.log(`failed to send download progress event: `, e);\n        // Fetch arrayBuffer directly when it is not possible to get progress.\n        buf = await resp.arrayBuffer();\n        cb &&\n            cb({\n                url,\n                total: buf.byteLength,\n                received: buf.byteLength,\n                delta: 0,\n                done: true,\n            });\n    }\n    return buf;\n};\n/**\n * toBlobURL fetches data from an URL and return a blob URL.\n *\n * Example:\n *\n * ```ts\n * await toBlobURL(\"http://localhost:3000/ffmpeg.js\", \"text/javascript\");\n * ```\n */\nexport const toBlobURL = async (url, mimeType, progress = false, cb) => {\n    const buf = progress\n        ? await downloadWithProgress(url, cb)\n        : await (await fetch(url)).arrayBuffer();\n    const blob = new Blob([buf], { type: mimeType });\n    return URL.createObjectURL(blob);\n};\n", "export const MIME_TYPE_JAVASCRIPT = \"text/javascript\";\nexport const MIME_TYPE_WASM = \"application/wasm\";\nexport const CORE_VERSION = \"0.12.1\";\nexport const CORE_URL = `https://unpkg.com/@ffmpeg/core@${CORE_VERSION}/dist/umd/ffmpeg-core.js`;\nexport var FFMessageType;\n(function (FFMessageType) {\n    FFMessageType[\"LOAD\"] = \"LOAD\";\n    FFMessageType[\"EXEC\"] = \"EXEC\";\n    FFMessageType[\"WRITE_FILE\"] = \"WRITE_FILE\";\n    FFMessageType[\"READ_FILE\"] = \"READ_FILE\";\n    FFMessageType[\"DELETE_FILE\"] = \"DELETE_FILE\";\n    FFMessageType[\"RENAME\"] = \"RENAME\";\n    FFMessageType[\"CREATE_DIR\"] = \"CREATE_DIR\";\n    FFMessageType[\"LIST_DIR\"] = \"LIST_DIR\";\n    FFMessageType[\"DELETE_DIR\"] = \"DELETE_DIR\";\n    FFMessageType[\"ERROR\"] = \"ERROR\";\n    FFMessageType[\"DOWNLOAD\"] = \"DOWNLOAD\";\n    FFMessageType[\"PROGRESS\"] = \"PROGRESS\";\n    FFMessageType[\"LOG\"] = \"LOG\";\n    FFMessageType[\"MOUNT\"] = \"MOUNT\";\n    FFMessageType[\"UNMOUNT\"] = \"UNMOUNT\";\n})(FFMessageType || (FFMessageType = {}));\n", "/**\n * Generate an unique message ID.\n */\nexport const getMessageID = (() => {\n    let messageID = 0;\n    return () => messageID++;\n})();\n", "export const ERROR_UNKNOWN_MESSAGE_TYPE = new Error(\"unknown message type\");\nexport const ERROR_NOT_LOADED = new Error(\"ffmpeg is not loaded, call `await ffmpeg.load()` first\");\nexport const ERROR_TERMINATED = new Error(\"called FFmpeg.terminate()\");\nexport const ERROR_IMPORT_FAILURE = new Error(\"failed to import ffmpeg-core.js\");\n", "import { FFMessageType } from \"./const.js\";\nimport { getMessageID } from \"./utils.js\";\nimport { ERROR_TERMINATED, ERROR_NOT_LOADED } from \"./errors.js\";\n/**\n * Provides APIs to interact with ffmpeg web worker.\n *\n * @example\n * ```ts\n * const ffmpeg = new FFmpeg();\n * ```\n */\nexport class FFmpeg {\n    #worker = null;\n    /**\n     * #resolves and #rejects tracks Promise resolves and rejects to\n     * be called when we receive message from web worker.\n     */\n    #resolves = {};\n    #rejects = {};\n    #logEventCallbacks = [];\n    #progressEventCallbacks = [];\n    loaded = false;\n    /**\n     * register worker message event handlers.\n     */\n    #registerHandlers = () => {\n        if (this.#worker) {\n            this.#worker.onmessage = ({ data: { id, type, data }, }) => {\n                switch (type) {\n                    case FFMessageType.LOAD:\n                        this.loaded = true;\n                        this.#resolves[id](data);\n                        break;\n                    case FFMessageType.MOUNT:\n                    case FFMessageType.UNMOUNT:\n                    case FFMessageType.EXEC:\n                    case FFMessageType.WRITE_FILE:\n                    case FFMessageType.READ_FILE:\n                    case FFMessageType.DELETE_FILE:\n                    case FFMessageType.RENAME:\n                    case FFMessageType.CREATE_DIR:\n                    case FFMessageType.LIST_DIR:\n                    case FFMessageType.DELETE_DIR:\n                        this.#resolves[id](data);\n                        break;\n                    case FFMessageType.LOG:\n                        this.#logEventCallbacks.forEach((f) => f(data));\n                        break;\n                    case FFMessageType.PROGRESS:\n                        this.#progressEventCallbacks.forEach((f) => f(data));\n                        break;\n                    case FFMessageType.ERROR:\n                        this.#rejects[id](data);\n                        break;\n                }\n                delete this.#resolves[id];\n                delete this.#rejects[id];\n            };\n        }\n    };\n    /**\n     * Generic function to send messages to web worker.\n     */\n    #send = ({ type, data }, trans = [], signal) => {\n        if (!this.#worker) {\n            return Promise.reject(ERROR_NOT_LOADED);\n        }\n        return new Promise((resolve, reject) => {\n            const id = getMessageID();\n            this.#worker && this.#worker.postMessage({ id, type, data }, trans);\n            this.#resolves[id] = resolve;\n            this.#rejects[id] = reject;\n            signal?.addEventListener(\"abort\", () => {\n                reject(new DOMException(`Message # ${id} was aborted`, \"AbortError\"));\n            }, { once: true });\n        });\n    };\n    on(event, callback) {\n        if (event === \"log\") {\n            this.#logEventCallbacks.push(callback);\n        }\n        else if (event === \"progress\") {\n            this.#progressEventCallbacks.push(callback);\n        }\n    }\n    off(event, callback) {\n        if (event === \"log\") {\n            this.#logEventCallbacks = this.#logEventCallbacks.filter((f) => f !== callback);\n        }\n        else if (event === \"progress\") {\n            this.#progressEventCallbacks = this.#progressEventCallbacks.filter((f) => f !== callback);\n        }\n    }\n    /**\n     * Loads ffmpeg-core inside web worker. It is required to call this method first\n     * as it initializes WebAssembly and other essential variables.\n     *\n     * @category FFmpeg\n     * @returns `true` if ffmpeg core is loaded for the first time.\n     */\n    load = (config = {}, { signal } = {}) => {\n        if (!this.#worker) {\n            this.#worker = new Worker(new URL(\"./worker.js\", import.meta.url), {\n                type: \"module\",\n            });\n            this.#registerHandlers();\n        }\n        return this.#send({\n            type: FFMessageType.LOAD,\n            data: config,\n        }, undefined, signal);\n    };\n    /**\n     * Execute ffmpeg command.\n     *\n     * @remarks\n     * To avoid common I/O issues, [\"-nostdin\", \"-y\"] are prepended to the args\n     * by default.\n     *\n     * @example\n     * ```ts\n     * const ffmpeg = new FFmpeg();\n     * await ffmpeg.load();\n     * await ffmpeg.writeFile(\"video.avi\", ...);\n     * // ffmpeg -i video.avi video.mp4\n     * await ffmpeg.exec([\"-i\", \"video.avi\", \"video.mp4\"]);\n     * const data = ffmpeg.readFile(\"video.mp4\");\n     * ```\n     *\n     * @returns `0` if no error, `!= 0` if timeout (1) or error.\n     * @category FFmpeg\n     */\n    exec = (\n    /** ffmpeg command line args */\n    args, \n    /**\n     * milliseconds to wait before stopping the command execution.\n     *\n     * @defaultValue -1\n     */\n    timeout = -1, { signal } = {}) => this.#send({\n        type: FFMessageType.EXEC,\n        data: { args, timeout },\n    }, undefined, signal);\n    /**\n     * Terminate all ongoing API calls and terminate web worker.\n     * `FFmpeg.load()` must be called again before calling any other APIs.\n     *\n     * @category FFmpeg\n     */\n    terminate = () => {\n        const ids = Object.keys(this.#rejects);\n        // rejects all incomplete Promises.\n        for (const id of ids) {\n            this.#rejects[id](ERROR_TERMINATED);\n            delete this.#rejects[id];\n            delete this.#resolves[id];\n        }\n        if (this.#worker) {\n            this.#worker.terminate();\n            this.#worker = null;\n            this.loaded = false;\n        }\n    };\n    /**\n     * Write data to ffmpeg.wasm.\n     *\n     * @example\n     * ```ts\n     * const ffmpeg = new FFmpeg();\n     * await ffmpeg.load();\n     * await ffmpeg.writeFile(\"video.avi\", await fetchFile(\"../video.avi\"));\n     * await ffmpeg.writeFile(\"text.txt\", \"hello world\");\n     * ```\n     *\n     * @category File System\n     */\n    writeFile = (path, data, { signal } = {}) => {\n        const trans = [];\n        if (data instanceof Uint8Array) {\n            trans.push(data.buffer);\n        }\n        return this.#send({\n            type: FFMessageType.WRITE_FILE,\n            data: { path, data },\n        }, trans, signal);\n    };\n    mount = (fsType, options, mountPoint) => {\n        const trans = [];\n        return this.#send({\n            type: FFMessageType.MOUNT,\n            data: { fsType, options, mountPoint },\n        }, trans);\n    };\n    unmount = (mountPoint) => {\n        const trans = [];\n        return this.#send({\n            type: FFMessageType.UNMOUNT,\n            data: { mountPoint },\n        }, trans);\n    };\n    /**\n     * Read data from ffmpeg.wasm.\n     *\n     * @example\n     * ```ts\n     * const ffmpeg = new FFmpeg();\n     * await ffmpeg.load();\n     * const data = await ffmpeg.readFile(\"video.mp4\");\n     * ```\n     *\n     * @category File System\n     */\n    readFile = (path, \n    /**\n     * File content encoding, supports two encodings:\n     * - utf8: read file as text file, return data in string type.\n     * - binary: read file as binary file, return data in Uint8Array type.\n     *\n     * @defaultValue binary\n     */\n    encoding = \"binary\", { signal } = {}) => this.#send({\n        type: FFMessageType.READ_FILE,\n        data: { path, encoding },\n    }, undefined, signal);\n    /**\n     * Delete a file.\n     *\n     * @category File System\n     */\n    deleteFile = (path, { signal } = {}) => this.#send({\n        type: FFMessageType.DELETE_FILE,\n        data: { path },\n    }, undefined, signal);\n    /**\n     * Rename a file or directory.\n     *\n     * @category File System\n     */\n    rename = (oldPath, newPath, { signal } = {}) => this.#send({\n        type: FFMessageType.RENAME,\n        data: { oldPath, newPath },\n    }, undefined, signal);\n    /**\n     * Create a directory.\n     *\n     * @category File System\n     */\n    createDir = (path, { signal } = {}) => this.#send({\n        type: FFMessageType.CREATE_DIR,\n        data: { path },\n    }, undefined, signal);\n    /**\n     * List directory contents.\n     *\n     * @category File System\n     */\n    listDir = (path, { signal } = {}) => this.#send({\n        type: FFMessageType.LIST_DIR,\n        data: { path },\n    }, undefined, signal);\n    /**\n     * Delete an empty directory.\n     *\n     * @category File System\n     */\n    deleteDir = (path, { signal } = {}) => this.#send({\n        type: FFMessageType.DELETE_DIR,\n        data: { path },\n    }, undefined, signal);\n}\n", "const mimes = {\n  \"3g2\": \"video/3gpp2\",\n  \"3gp\": \"video/3gpp\",\n  \"3gpp\": \"video/3gpp\",\n  \"3mf\": \"model/3mf\",\n  \"aac\": \"audio/aac\",\n  \"ac\": \"application/pkix-attr-cert\",\n  \"adp\": \"audio/adpcm\",\n  \"adts\": \"audio/aac\",\n  \"ai\": \"application/postscript\",\n  \"aml\": \"application/automationml-aml+xml\",\n  \"amlx\": \"application/automationml-amlx+zip\",\n  \"amr\": \"audio/amr\",\n  \"apng\": \"image/apng\",\n  \"appcache\": \"text/cache-manifest\",\n  \"appinstaller\": \"application/appinstaller\",\n  \"appx\": \"application/appx\",\n  \"appxbundle\": \"application/appxbundle\",\n  \"asc\": \"application/pgp-keys\",\n  \"atom\": \"application/atom+xml\",\n  \"atomcat\": \"application/atomcat+xml\",\n  \"atomdeleted\": \"application/atomdeleted+xml\",\n  \"atomsvc\": \"application/atomsvc+xml\",\n  \"au\": \"audio/basic\",\n  \"avci\": \"image/avci\",\n  \"avcs\": \"image/avcs\",\n  \"avif\": \"image/avif\",\n  \"aw\": \"application/applixware\",\n  \"bdoc\": \"application/bdoc\",\n  \"bin\": \"application/octet-stream\",\n  \"bmp\": \"image/bmp\",\n  \"bpk\": \"application/octet-stream\",\n  \"btf\": \"image/prs.btif\",\n  \"btif\": \"image/prs.btif\",\n  \"buffer\": \"application/octet-stream\",\n  \"ccxml\": \"application/ccxml+xml\",\n  \"cdfx\": \"application/cdfx+xml\",\n  \"cdmia\": \"application/cdmi-capability\",\n  \"cdmic\": \"application/cdmi-container\",\n  \"cdmid\": \"application/cdmi-domain\",\n  \"cdmio\": \"application/cdmi-object\",\n  \"cdmiq\": \"application/cdmi-queue\",\n  \"cer\": \"application/pkix-cert\",\n  \"cgm\": \"image/cgm\",\n  \"cjs\": \"application/node\",\n  \"class\": \"application/java-vm\",\n  \"coffee\": \"text/coffeescript\",\n  \"conf\": \"text/plain\",\n  \"cpl\": \"application/cpl+xml\",\n  \"cpt\": \"application/mac-compactpro\",\n  \"crl\": \"application/pkix-crl\",\n  \"css\": \"text/css\",\n  \"csv\": \"text/csv\",\n  \"cu\": \"application/cu-seeme\",\n  \"cwl\": \"application/cwl\",\n  \"cww\": \"application/prs.cww\",\n  \"davmount\": \"application/davmount+xml\",\n  \"dbk\": \"application/docbook+xml\",\n  \"deb\": \"application/octet-stream\",\n  \"def\": \"text/plain\",\n  \"deploy\": \"application/octet-stream\",\n  \"dib\": \"image/bmp\",\n  \"disposition-notification\": \"message/disposition-notification\",\n  \"dist\": \"application/octet-stream\",\n  \"distz\": \"application/octet-stream\",\n  \"dll\": \"application/octet-stream\",\n  \"dmg\": \"application/octet-stream\",\n  \"dms\": \"application/octet-stream\",\n  \"doc\": \"application/msword\",\n  \"dot\": \"application/msword\",\n  \"dpx\": \"image/dpx\",\n  \"drle\": \"image/dicom-rle\",\n  \"dsc\": \"text/prs.lines.tag\",\n  \"dssc\": \"application/dssc+der\",\n  \"dtd\": \"application/xml-dtd\",\n  \"dump\": \"application/octet-stream\",\n  \"dwd\": \"application/atsc-dwd+xml\",\n  \"ear\": \"application/java-archive\",\n  \"ecma\": \"application/ecmascript\",\n  \"elc\": \"application/octet-stream\",\n  \"emf\": \"image/emf\",\n  \"eml\": \"message/rfc822\",\n  \"emma\": \"application/emma+xml\",\n  \"emotionml\": \"application/emotionml+xml\",\n  \"eps\": \"application/postscript\",\n  \"epub\": \"application/epub+zip\",\n  \"exe\": \"application/octet-stream\",\n  \"exi\": \"application/exi\",\n  \"exp\": \"application/express\",\n  \"exr\": \"image/aces\",\n  \"ez\": \"application/andrew-inset\",\n  \"fdf\": \"application/fdf\",\n  \"fdt\": \"application/fdt+xml\",\n  \"fits\": \"image/fits\",\n  \"g3\": \"image/g3fax\",\n  \"gbr\": \"application/rpki-ghostbusters\",\n  \"geojson\": \"application/geo+json\",\n  \"gif\": \"image/gif\",\n  \"glb\": \"model/gltf-binary\",\n  \"gltf\": \"model/gltf+json\",\n  \"gml\": \"application/gml+xml\",\n  \"gpx\": \"application/gpx+xml\",\n  \"gram\": \"application/srgs\",\n  \"grxml\": \"application/srgs+xml\",\n  \"gxf\": \"application/gxf\",\n  \"gz\": \"application/gzip\",\n  \"h261\": \"video/h261\",\n  \"h263\": \"video/h263\",\n  \"h264\": \"video/h264\",\n  \"heic\": \"image/heic\",\n  \"heics\": \"image/heic-sequence\",\n  \"heif\": \"image/heif\",\n  \"heifs\": \"image/heif-sequence\",\n  \"hej2\": \"image/hej2k\",\n  \"held\": \"application/atsc-held+xml\",\n  \"hjson\": \"application/hjson\",\n  \"hlp\": \"application/winhlp\",\n  \"hqx\": \"application/mac-binhex40\",\n  \"hsj2\": \"image/hsj2\",\n  \"htm\": \"text/html\",\n  \"html\": \"text/html\",\n  \"ics\": \"text/calendar\",\n  \"ief\": \"image/ief\",\n  \"ifb\": \"text/calendar\",\n  \"iges\": \"model/iges\",\n  \"igs\": \"model/iges\",\n  \"img\": \"application/octet-stream\",\n  \"in\": \"text/plain\",\n  \"ini\": \"text/plain\",\n  \"ink\": \"application/inkml+xml\",\n  \"inkml\": \"application/inkml+xml\",\n  \"ipfix\": \"application/ipfix\",\n  \"iso\": \"application/octet-stream\",\n  \"its\": \"application/its+xml\",\n  \"jade\": \"text/jade\",\n  \"jar\": \"application/java-archive\",\n  \"jhc\": \"image/jphc\",\n  \"jls\": \"image/jls\",\n  \"jp2\": \"image/jp2\",\n  \"jpe\": \"image/jpeg\",\n  \"jpeg\": \"image/jpeg\",\n  \"jpf\": \"image/jpx\",\n  \"jpg\": \"image/jpeg\",\n  \"jpg2\": \"image/jp2\",\n  \"jpgm\": \"image/jpm\",\n  \"jpgv\": \"video/jpeg\",\n  \"jph\": \"image/jph\",\n  \"jpm\": \"image/jpm\",\n  \"jpx\": \"image/jpx\",\n  \"js\": \"text/javascript\",\n  \"json\": \"application/json\",\n  \"json5\": \"application/json5\",\n  \"jsonld\": \"application/ld+json\",\n  \"jsonml\": \"application/jsonml+json\",\n  \"jsx\": \"text/jsx\",\n  \"jt\": \"model/jt\",\n  \"jxr\": \"image/jxr\",\n  \"jxra\": \"image/jxra\",\n  \"jxrs\": \"image/jxrs\",\n  \"jxs\": \"image/jxs\",\n  \"jxsc\": \"image/jxsc\",\n  \"jxsi\": \"image/jxsi\",\n  \"jxss\": \"image/jxss\",\n  \"kar\": \"audio/midi\",\n  \"ktx\": \"image/ktx\",\n  \"ktx2\": \"image/ktx2\",\n  \"less\": \"text/less\",\n  \"lgr\": \"application/lgr+xml\",\n  \"list\": \"text/plain\",\n  \"litcoffee\": \"text/coffeescript\",\n  \"log\": \"text/plain\",\n  \"lostxml\": \"application/lost+xml\",\n  \"lrf\": \"application/octet-stream\",\n  \"m1v\": \"video/mpeg\",\n  \"m21\": \"application/mp21\",\n  \"m2a\": \"audio/mpeg\",\n  \"m2v\": \"video/mpeg\",\n  \"m3a\": \"audio/mpeg\",\n  \"m4a\": \"audio/mp4\",\n  \"m4p\": \"application/mp4\",\n  \"m4s\": \"video/iso.segment\",\n  \"ma\": \"application/mathematica\",\n  \"mads\": \"application/mads+xml\",\n  \"maei\": \"application/mmt-aei+xml\",\n  \"man\": \"text/troff\",\n  \"manifest\": \"text/cache-manifest\",\n  \"map\": \"application/json\",\n  \"mar\": \"application/octet-stream\",\n  \"markdown\": \"text/markdown\",\n  \"mathml\": \"application/mathml+xml\",\n  \"mb\": \"application/mathematica\",\n  \"mbox\": \"application/mbox\",\n  \"md\": \"text/markdown\",\n  \"mdx\": \"text/mdx\",\n  \"me\": \"text/troff\",\n  \"mesh\": \"model/mesh\",\n  \"meta4\": \"application/metalink4+xml\",\n  \"metalink\": \"application/metalink+xml\",\n  \"mets\": \"application/mets+xml\",\n  \"mft\": \"application/rpki-manifest\",\n  \"mid\": \"audio/midi\",\n  \"midi\": \"audio/midi\",\n  \"mime\": \"message/rfc822\",\n  \"mj2\": \"video/mj2\",\n  \"mjp2\": \"video/mj2\",\n  \"mjs\": \"text/javascript\",\n  \"mml\": \"text/mathml\",\n  \"mods\": \"application/mods+xml\",\n  \"mov\": \"video/quicktime\",\n  \"mp2\": \"audio/mpeg\",\n  \"mp21\": \"application/mp21\",\n  \"mp2a\": \"audio/mpeg\",\n  \"mp3\": \"audio/mpeg\",\n  \"mp4\": \"video/mp4\",\n  \"mp4a\": \"audio/mp4\",\n  \"mp4s\": \"application/mp4\",\n  \"mp4v\": \"video/mp4\",\n  \"mpd\": \"application/dash+xml\",\n  \"mpe\": \"video/mpeg\",\n  \"mpeg\": \"video/mpeg\",\n  \"mpf\": \"application/media-policy-dataset+xml\",\n  \"mpg\": \"video/mpeg\",\n  \"mpg4\": \"video/mp4\",\n  \"mpga\": \"audio/mpeg\",\n  \"mpp\": \"application/dash-patch+xml\",\n  \"mrc\": \"application/marc\",\n  \"mrcx\": \"application/marcxml+xml\",\n  \"ms\": \"text/troff\",\n  \"mscml\": \"application/mediaservercontrol+xml\",\n  \"msh\": \"model/mesh\",\n  \"msi\": \"application/octet-stream\",\n  \"msix\": \"application/msix\",\n  \"msixbundle\": \"application/msixbundle\",\n  \"msm\": \"application/octet-stream\",\n  \"msp\": \"application/octet-stream\",\n  \"mtl\": \"model/mtl\",\n  \"musd\": \"application/mmt-usd+xml\",\n  \"mxf\": \"application/mxf\",\n  \"mxmf\": \"audio/mobile-xmf\",\n  \"mxml\": \"application/xv+xml\",\n  \"n3\": \"text/n3\",\n  \"nb\": \"application/mathematica\",\n  \"nq\": \"application/n-quads\",\n  \"nt\": \"application/n-triples\",\n  \"obj\": \"model/obj\",\n  \"oda\": \"application/oda\",\n  \"oga\": \"audio/ogg\",\n  \"ogg\": \"audio/ogg\",\n  \"ogv\": \"video/ogg\",\n  \"ogx\": \"application/ogg\",\n  \"omdoc\": \"application/omdoc+xml\",\n  \"onepkg\": \"application/onenote\",\n  \"onetmp\": \"application/onenote\",\n  \"onetoc\": \"application/onenote\",\n  \"onetoc2\": \"application/onenote\",\n  \"opf\": \"application/oebps-package+xml\",\n  \"opus\": \"audio/ogg\",\n  \"otf\": \"font/otf\",\n  \"owl\": \"application/rdf+xml\",\n  \"oxps\": \"application/oxps\",\n  \"p10\": \"application/pkcs10\",\n  \"p7c\": \"application/pkcs7-mime\",\n  \"p7m\": \"application/pkcs7-mime\",\n  \"p7s\": \"application/pkcs7-signature\",\n  \"p8\": \"application/pkcs8\",\n  \"pdf\": \"application/pdf\",\n  \"pfr\": \"application/font-tdpfr\",\n  \"pgp\": \"application/pgp-encrypted\",\n  \"pkg\": \"application/octet-stream\",\n  \"pki\": \"application/pkixcmp\",\n  \"pkipath\": \"application/pkix-pkipath\",\n  \"pls\": \"application/pls+xml\",\n  \"png\": \"image/png\",\n  \"prc\": \"model/prc\",\n  \"prf\": \"application/pics-rules\",\n  \"provx\": \"application/provenance+xml\",\n  \"ps\": \"application/postscript\",\n  \"pskcxml\": \"application/pskc+xml\",\n  \"pti\": \"image/prs.pti\",\n  \"qt\": \"video/quicktime\",\n  \"raml\": \"application/raml+yaml\",\n  \"rapd\": \"application/route-apd+xml\",\n  \"rdf\": \"application/rdf+xml\",\n  \"relo\": \"application/p2p-overlay+xml\",\n  \"rif\": \"application/reginfo+xml\",\n  \"rl\": \"application/resource-lists+xml\",\n  \"rld\": \"application/resource-lists-diff+xml\",\n  \"rmi\": \"audio/midi\",\n  \"rnc\": \"application/relax-ng-compact-syntax\",\n  \"rng\": \"application/xml\",\n  \"roa\": \"application/rpki-roa\",\n  \"roff\": \"text/troff\",\n  \"rq\": \"application/sparql-query\",\n  \"rs\": \"application/rls-services+xml\",\n  \"rsat\": \"application/atsc-rsat+xml\",\n  \"rsd\": \"application/rsd+xml\",\n  \"rsheet\": \"application/urc-ressheet+xml\",\n  \"rss\": \"application/rss+xml\",\n  \"rtf\": \"text/rtf\",\n  \"rtx\": \"text/richtext\",\n  \"rusd\": \"application/route-usd+xml\",\n  \"s3m\": \"audio/s3m\",\n  \"sbml\": \"application/sbml+xml\",\n  \"scq\": \"application/scvp-cv-request\",\n  \"scs\": \"application/scvp-cv-response\",\n  \"sdp\": \"application/sdp\",\n  \"senmlx\": \"application/senml+xml\",\n  \"sensmlx\": \"application/sensml+xml\",\n  \"ser\": \"application/java-serialized-object\",\n  \"setpay\": \"application/set-payment-initiation\",\n  \"setreg\": \"application/set-registration-initiation\",\n  \"sgi\": \"image/sgi\",\n  \"sgm\": \"text/sgml\",\n  \"sgml\": \"text/sgml\",\n  \"shex\": \"text/shex\",\n  \"shf\": \"application/shf+xml\",\n  \"shtml\": \"text/html\",\n  \"sieve\": \"application/sieve\",\n  \"sig\": \"application/pgp-signature\",\n  \"sil\": \"audio/silk\",\n  \"silo\": \"model/mesh\",\n  \"siv\": \"application/sieve\",\n  \"slim\": \"text/slim\",\n  \"slm\": \"text/slim\",\n  \"sls\": \"application/route-s-tsid+xml\",\n  \"smi\": \"application/smil+xml\",\n  \"smil\": \"application/smil+xml\",\n  \"snd\": \"audio/basic\",\n  \"so\": \"application/octet-stream\",\n  \"spdx\": \"text/spdx\",\n  \"spp\": \"application/scvp-vp-response\",\n  \"spq\": \"application/scvp-vp-request\",\n  \"spx\": \"audio/ogg\",\n  \"sql\": \"application/sql\",\n  \"sru\": \"application/sru+xml\",\n  \"srx\": \"application/sparql-results+xml\",\n  \"ssdl\": \"application/ssdl+xml\",\n  \"ssml\": \"application/ssml+xml\",\n  \"stk\": \"application/hyperstudio\",\n  \"stl\": \"model/stl\",\n  \"stpx\": \"model/step+xml\",\n  \"stpxz\": \"model/step-xml+zip\",\n  \"stpz\": \"model/step+zip\",\n  \"styl\": \"text/stylus\",\n  \"stylus\": \"text/stylus\",\n  \"svg\": \"image/svg+xml\",\n  \"svgz\": \"image/svg+xml\",\n  \"swidtag\": \"application/swid+xml\",\n  \"t\": \"text/troff\",\n  \"t38\": \"image/t38\",\n  \"td\": \"application/urc-targetdesc+xml\",\n  \"tei\": \"application/tei+xml\",\n  \"teicorpus\": \"application/tei+xml\",\n  \"text\": \"text/plain\",\n  \"tfi\": \"application/thraud+xml\",\n  \"tfx\": \"image/tiff-fx\",\n  \"tif\": \"image/tiff\",\n  \"tiff\": \"image/tiff\",\n  \"toml\": \"application/toml\",\n  \"tr\": \"text/troff\",\n  \"trig\": \"application/trig\",\n  \"ts\": \"video/mp2t\",\n  \"tsd\": \"application/timestamped-data\",\n  \"tsv\": \"text/tab-separated-values\",\n  \"ttc\": \"font/collection\",\n  \"ttf\": \"font/ttf\",\n  \"ttl\": \"text/turtle\",\n  \"ttml\": \"application/ttml+xml\",\n  \"txt\": \"text/plain\",\n  \"u3d\": \"model/u3d\",\n  \"u8dsn\": \"message/global-delivery-status\",\n  \"u8hdr\": \"message/global-headers\",\n  \"u8mdn\": \"message/global-disposition-notification\",\n  \"u8msg\": \"message/global\",\n  \"ubj\": \"application/ubjson\",\n  \"uri\": \"text/uri-list\",\n  \"uris\": \"text/uri-list\",\n  \"urls\": \"text/uri-list\",\n  \"vcard\": \"text/vcard\",\n  \"vrml\": \"model/vrml\",\n  \"vtt\": \"text/vtt\",\n  \"vxml\": \"application/voicexml+xml\",\n  \"war\": \"application/java-archive\",\n  \"wasm\": \"application/wasm\",\n  \"wav\": \"audio/wav\",\n  \"weba\": \"audio/webm\",\n  \"webm\": \"video/webm\",\n  \"webmanifest\": \"application/manifest+json\",\n  \"webp\": \"image/webp\",\n  \"wgsl\": \"text/wgsl\",\n  \"wgt\": \"application/widget\",\n  \"wif\": \"application/watcherinfo+xml\",\n  \"wmf\": \"image/wmf\",\n  \"woff\": \"font/woff\",\n  \"woff2\": \"font/woff2\",\n  \"wrl\": \"model/vrml\",\n  \"wsdl\": \"application/wsdl+xml\",\n  \"wspolicy\": \"application/wspolicy+xml\",\n  \"x3d\": \"model/x3d+xml\",\n  \"x3db\": \"model/x3d+fastinfoset\",\n  \"x3dbz\": \"model/x3d+binary\",\n  \"x3dv\": \"model/x3d-vrml\",\n  \"x3dvz\": \"model/x3d+vrml\",\n  \"x3dz\": \"model/x3d+xml\",\n  \"xaml\": \"application/xaml+xml\",\n  \"xav\": \"application/xcap-att+xml\",\n  \"xca\": \"application/xcap-caps+xml\",\n  \"xcs\": \"application/calendar+xml\",\n  \"xdf\": \"application/xcap-diff+xml\",\n  \"xdssc\": \"application/dssc+xml\",\n  \"xel\": \"application/xcap-el+xml\",\n  \"xenc\": \"application/xenc+xml\",\n  \"xer\": \"application/patch-ops-error+xml\",\n  \"xfdf\": \"application/xfdf\",\n  \"xht\": \"application/xhtml+xml\",\n  \"xhtml\": \"application/xhtml+xml\",\n  \"xhvml\": \"application/xv+xml\",\n  \"xlf\": \"application/xliff+xml\",\n  \"xm\": \"audio/xm\",\n  \"xml\": \"text/xml\",\n  \"xns\": \"application/xcap-ns+xml\",\n  \"xop\": \"application/xop+xml\",\n  \"xpl\": \"application/xproc+xml\",\n  \"xsd\": \"application/xml\",\n  \"xsf\": \"application/prs.xsf+xml\",\n  \"xsl\": \"application/xml\",\n  \"xslt\": \"application/xml\",\n  \"xspf\": \"application/xspf+xml\",\n  \"xvm\": \"application/xv+xml\",\n  \"xvml\": \"application/xv+xml\",\n  \"yaml\": \"text/yaml\",\n  \"yang\": \"application/yang\",\n  \"yin\": \"application/yin+xml\",\n  \"yml\": \"text/yaml\",\n  \"zip\": \"application/zip\"\n};\n\nfunction lookup(extn) {\n\tlet tmp = ('' + extn).trim().toLowerCase();\n\tlet idx = tmp.lastIndexOf('.');\n\treturn mimes[!~idx ? tmp : tmp.substring(++idx)];\n}\n\nexport { mimes, lookup };\n", "import { toB<PERSON>bURL } from \"@ffmpeg/util\";\nimport { FFmpeg } from \"@ffmpeg/ffmpeg\";\nimport { lookup } from \"mrmime\";\n\nexport const prettyBytes = (bytes: number): string => {\n\tlet units = [\"B\", \"KB\", \"MB\", \"GB\", \"PB\"];\n\tlet i = 0;\n\twhile (bytes > 1024) {\n\t\tbytes /= 1024;\n\t\ti++;\n\t}\n\tlet unit = units[i];\n\treturn bytes.toFixed(1) + \" \" + unit;\n};\n\nexport const playable = (): boolean => {\n\t// TODO: Fix this\n\t// let video_element = document.createElement(\"video\");\n\t// let mime_type = mime.lookup(filename);\n\t// return video_element.canPlayType(mime_type) != \"\";\n\treturn true; // FIX BEFORE COMMIT - mime import causing issues\n};\n\nexport function loaded(\n\tnode: HTMLVideoElement,\n\t{ autoplay }: { autoplay: boolean }\n): any {\n\tasync function handle_playback(): Promise<void> {\n\t\tif (!autoplay) return;\n\t\tawait node.play();\n\t}\n\n\tnode.addEventListener(\"loadeddata\", handle_playback);\n\n\treturn {\n\t\tdestroy(): void {\n\t\t\tnode.removeEventListener(\"loadeddata\", handle_playback);\n\t\t}\n\t};\n}\n\nexport default async function loadFfmpeg(): Promise<FFmpeg> {\n\tconst ffmpeg = new FFmpeg();\n\tconst baseURL = \"https://unpkg.com/@ffmpeg/core@0.12.4/dist/esm\";\n\n\tawait ffmpeg.load({\n\t\tcoreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, \"text/javascript\"),\n\t\twasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, \"application/wasm\")\n\t});\n\n\treturn ffmpeg;\n}\n\nexport function blob_to_data_url(blob: Blob): Promise<string> {\n\treturn new Promise((fulfill, reject) => {\n\t\tlet reader = new FileReader();\n\t\treader.onerror = reject;\n\t\treader.onload = () => fulfill(reader.result as string);\n\t\treader.readAsDataURL(blob);\n\t});\n}\n\nexport async function trimVideo(\n\tffmpeg: FFmpeg,\n\tstartTime: number,\n\tendTime: number,\n\tvideoElement: HTMLVideoElement\n): Promise<any> {\n\tconst videoUrl = videoElement.src;\n\tconst mimeType = lookup(videoElement.src) || \"video/mp4\";\n\tconst blobUrl = await toBlobURL(videoUrl, mimeType);\n\tconst response = await fetch(blobUrl);\n\tconst vidBlob = await response.blob();\n\tconst type = getVideoExtensionFromMimeType(mimeType) || \"mp4\";\n\tconst inputName = `input.${type}`;\n\tconst outputName = `output.${type}`;\n\n\ttry {\n\t\tif (startTime === 0 && endTime === 0) {\n\t\t\treturn vidBlob;\n\t\t}\n\n\t\tawait ffmpeg.writeFile(\n\t\t\tinputName,\n\t\t\tnew Uint8Array(await vidBlob.arrayBuffer())\n\t\t);\n\n\t\tlet command = [\n\t\t\t\"-i\",\n\t\t\tinputName,\n\t\t\t...(startTime !== 0 ? [\"-ss\", startTime.toString()] : []),\n\t\t\t...(endTime !== 0 ? [\"-to\", endTime.toString()] : []),\n\t\t\t\"-c:a\",\n\t\t\t\"copy\",\n\t\t\toutputName\n\t\t];\n\n\t\tawait ffmpeg.exec(command);\n\t\tconst outputData = await ffmpeg.readFile(outputName);\n\t\tconst outputBlob = new Blob([outputData], {\n\t\t\ttype: `video/${type}`\n\t\t});\n\n\t\treturn outputBlob;\n\t} catch (error) {\n\t\tconsole.error(\"Error initializing FFmpeg:\", error);\n\t\treturn vidBlob;\n\t}\n}\n\nconst getVideoExtensionFromMimeType = (mimeType: string): string | null => {\n\tconst videoMimeToExtensionMap: { [key: string]: string } = {\n\t\t\"video/mp4\": \"mp4\",\n\t\t\"video/webm\": \"webm\",\n\t\t\"video/ogg\": \"ogv\",\n\t\t\"video/quicktime\": \"mov\",\n\t\t\"video/x-msvideo\": \"avi\",\n\t\t\"video/x-matroska\": \"mkv\",\n\t\t\"video/mpeg\": \"mpeg\",\n\t\t\"video/3gpp\": \"3gp\",\n\t\t\"video/3gpp2\": \"3g2\",\n\t\t\"video/h261\": \"h261\",\n\t\t\"video/h263\": \"h263\",\n\t\t\"video/h264\": \"h264\",\n\t\t\"video/jpeg\": \"jpgv\",\n\t\t\"video/jpm\": \"jpm\",\n\t\t\"video/mj2\": \"mj2\",\n\t\t\"video/mpv\": \"mpv\",\n\t\t\"video/vnd.ms-playready.media.pyv\": \"pyv\",\n\t\t\"video/vnd.uvvu.mp4\": \"uvu\",\n\t\t\"video/vnd.vivo\": \"viv\",\n\t\t\"video/x-f4v\": \"f4v\",\n\t\t\"video/x-fli\": \"fli\",\n\t\t\"video/x-flv\": \"flv\",\n\t\t\"video/x-m4v\": \"m4v\",\n\t\t\"video/x-ms-asf\": \"asf\",\n\t\t\"video/x-ms-wm\": \"wm\",\n\t\t\"video/x-ms-wmv\": \"wmv\",\n\t\t\"video/x-ms-wmx\": \"wmx\",\n\t\t\"video/x-ms-wvx\": \"wvx\",\n\t\t\"video/x-sgi-movie\": \"movie\",\n\t\t\"video/x-smv\": \"smv\"\n\t};\n\n\treturn videoMimeToExtensionMap[mimeType] || null;\n};\n", "<script lang=\"ts\">\n\timport type { HTMLVideoAttributes } from \"svelte/elements\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport { loaded } from \"./utils\";\n\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\n\texport let src: HTMLVideoAttributes[\"src\"] = undefined;\n\n\texport let muted: HTMLVideoAttributes[\"muted\"] = undefined;\n\texport let playsinline: HTMLVideoAttributes[\"playsinline\"] = undefined;\n\texport let preload: HTMLVideoAttributes[\"preload\"] = undefined;\n\texport let autoplay: HTMLVideoAttributes[\"autoplay\"] = undefined;\n\texport let controls: HTMLVideoAttributes[\"controls\"] = undefined;\n\n\texport let currentTime: number | undefined = undefined;\n\texport let duration: number | undefined = undefined;\n\texport let paused: boolean | undefined = undefined;\n\n\texport let node: HTMLVideoElement | undefined = undefined;\n\texport let loop: boolean;\n\n\texport let processingVideo = false;\n\n\tlet resolved_src: typeof src;\n\n\t// The `src` prop can be updated before the Promise from `resolve_wasm_src` is resolved.\n\t// In such a case, the resolved value for the old `src` has to be discarded,\n\t// This variable `latest_src` is used to pick up only the value resolved for the latest `src` prop.\n\tlet latest_src: typeof src;\n\t$: {\n\t\t// In normal (non-Wasm) Gradio, the `<img>` element should be rendered with the passed `src` props immediately\n\t\t// without waiting for `resolve_wasm_src()` to resolve.\n\t\t// If it waits, a blank element is displayed until the async task finishes\n\t\t// and it leads to undesirable flickering.\n\t\t// So set `src` to `resolved_src` here.\n\t\tresolved_src = src;\n\n\t\tlatest_src = src;\n\t\tconst resolving_src = src;\n\t\tresolve_wasm_src(resolving_src).then((s) => {\n\t\t\tif (latest_src === resolving_src) {\n\t\t\t\tresolved_src = s;\n\t\t\t}\n\t\t});\n\t}\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<!--\nThe spread operator with `$$props` or `$$restProps` can't be used here\nto pass props from the parent component to the <video> element\nbecause of its unexpected behavior: https://github.com/sveltejs/svelte/issues/7404\nFor example, if we add {...$$props} or {...$$restProps}, the boolean props aside it like `controls` will be compiled as string \"true\" or \"false\" on the actual DOM.\nThen, even when `controls` is false, the compiled DOM would be `<video controls=\"false\">` which is equivalent to `<video controls>` since the string \"false\" is even truthy.\n-->\n<div class:hidden={!processingVideo} class=\"overlay\">\n\t<span class=\"load-wrap\">\n\t\t<span class=\"loader\" />\n\t</span>\n</div>\n<video\n\tsrc={resolved_src}\n\t{muted}\n\t{playsinline}\n\t{preload}\n\t{autoplay}\n\t{controls}\n\t{loop}\n\ton:loadeddata={dispatch.bind(null, \"loadeddata\")}\n\ton:click={dispatch.bind(null, \"click\")}\n\ton:play={dispatch.bind(null, \"play\")}\n\ton:pause={dispatch.bind(null, \"pause\")}\n\ton:ended={dispatch.bind(null, \"ended\")}\n\ton:mouseover={dispatch.bind(null, \"mouseover\")}\n\ton:mouseout={dispatch.bind(null, \"mouseout\")}\n\ton:focus={dispatch.bind(null, \"focus\")}\n\ton:blur={dispatch.bind(null, \"blur\")}\n\ton:load\n\tbind:currentTime\n\tbind:duration\n\tbind:paused\n\tbind:this={node}\n\tuse:loaded={{ autoplay: autoplay ?? false }}\n\tdata-testid={$$props[\"data-testid\"]}\n\tcrossorigin=\"anonymous\"\n>\n\t<slot />\n</video>\n\n<style>\n\t.overlay {\n\t\tposition: absolute;\n\t\tbackground-color: rgba(0, 0, 0, 0.4);\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.hidden {\n\t\tdisplay: none;\n\t}\n\n\t.load-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\theight: 100%;\n\t}\n\n\t.loader {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tbackground-color: var(--border-color-accent-subdued);\n\t\tanimation: shadowPulse 2s linear infinite;\n\t\tbox-shadow:\n\t\t\t-24px 0 var(--border-color-accent-subdued),\n\t\t\t24px 0 var(--border-color-accent-subdued);\n\t\tmargin: var(--spacing-md);\n\t\tborder-radius: 50%;\n\t\twidth: 10px;\n\t\theight: 10px;\n\t\tscale: 0.5;\n\t}\n\n\t@keyframes shadowPulse {\n\t\t33% {\n\t\t\tbox-shadow:\n\t\t\t\t-24px 0 var(--border-color-accent-subdued),\n\t\t\t\t24px 0 #fff;\n\t\t\tbackground: #fff;\n\t\t}\n\t\t66% {\n\t\t\tbox-shadow:\n\t\t\t\t-24px 0 #fff,\n\t\t\t\t24px 0 #fff;\n\t\t\tbackground: var(--border-color-accent-subdued);\n\t\t}\n\t\t100% {\n\t\t\tbox-shadow:\n\t\t\t\t-24px 0 #fff,\n\t\t\t\t24px 0 var(--border-color-accent-subdued);\n\t\t\tbackground: #fff;\n\t\t}\n\t}\n</style>\n"], "names": ["ERROR_RESPONSE_BODY_READER", "ERROR_INCOMPLETED_DOWNLOAD", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "downloadWithProgress", "url", "cb", "resp", "buf", "total", "reader", "chunks", "received", "done", "value", "delta", "data", "position", "chunk", "e", "toBlobURL", "mimeType", "progress", "blob", "FFMessageType", "getMessageID", "messageID", "ERROR_NOT_LOADED", "ERROR_TERMINATED", "FFmpeg", "#worker", "#resolves", "#rejects", "#logEventCallbacks", "#progressEventCallbacks", "#registerHandlers", "id", "type", "f", "#send", "trans", "signal", "resolve", "reject", "event", "callback", "config", "args", "timeout", "ids", "path", "fsType", "options", "mountPoint", "encoding", "old<PERSON><PERSON>", "newPath", "mimes", "lookup", "extn", "tmp", "idx", "prettyBytes", "bytes", "units", "i", "unit", "playable", "loaded", "node", "autoplay", "handle_playback", "loadFfmpeg", "ffmpeg", "baseURL", "trimVideo", "startTime", "endTime", "videoElement", "videoUrl", "blobUrl", "vidBlob", "getVideoExtensionFromMimeType", "inputName", "outputName", "command", "outputData", "error", "createEventDispatcher", "ctx", "attr", "video", "video_src_value", "video_data_testid_value", "insert", "target", "div", "anchor", "listen", "current", "dirty", "src", "$$props", "muted", "playsinline", "preload", "controls", "currentTime", "duration", "paused", "loop", "processingVideo", "resolved_src", "latest_src", "dispatch", "$$value", "$$invalidate", "resolving_src", "resolve_wasm_src", "s"], "mappings": "2CAAO,MAAMA,EAA6B,IAAI,MAAM,oCAAoC,EAC3EC,EAA6B,IAAI,MAAM,6BAA6B,ECDpEC,EAAsB,iBC0FtBC,EAAuB,MAAOC,EAAKC,IAAO,CACnD,MAAMC,EAAO,MAAM,MAAMF,CAAG,EAC5B,IAAIG,EACJ,GAAI,CAEA,MAAMC,EAAQ,SAASF,EAAK,QAAQ,IAAIJ,CAAmB,GAAK,IAAI,EAC9DO,EAASH,EAAK,MAAM,UAAS,EACnC,GAAI,CAACG,EACD,MAAMT,EACV,MAAMU,EAAS,CAAA,EACf,IAAIC,EAAW,EACf,OAAS,CACL,KAAM,CAAE,KAAAC,EAAM,MAAAC,CAAK,EAAK,MAAMJ,EAAO,KAAI,EACnCK,EAAQD,EAAQA,EAAM,OAAS,EACrC,GAAID,EAAM,CACN,GAAIJ,GAAS,IAAMA,IAAUG,EACzB,MAAMV,EACVI,GAAMA,EAAG,CAAE,IAAAD,EAAK,MAAAI,EAAO,SAAAG,EAAU,MAAAG,EAAO,KAAAF,CAAI,CAAE,EAC9C,KACH,CACDF,EAAO,KAAKG,CAAK,EACjBF,GAAYG,EACZT,GAAMA,EAAG,CAAE,IAAAD,EAAK,MAAAI,EAAO,SAAAG,EAAU,MAAAG,EAAO,KAAAF,CAAI,CAAE,CACjD,CACD,MAAMG,EAAO,IAAI,WAAWJ,CAAQ,EACpC,IAAIK,EAAW,EACf,UAAWC,KAASP,EAChBK,EAAK,IAAIE,EAAOD,CAAQ,EACxBA,GAAYC,EAAM,OAEtBV,EAAMQ,EAAK,MACd,OACMG,EAAG,CACN,QAAQ,IAAI,2CAA4CA,CAAC,EAEzDX,EAAM,MAAMD,EAAK,aASpB,CACD,OAAOC,CACX,EAUaY,EAAY,MAAOf,EAAKgB,EAAUC,EAAW,GAAOhB,IAAO,CACpE,MAAME,EAAMc,EACN,MAAMlB,EAAqBC,EAAKC,CAAE,EAClC,MAAO,MAAM,MAAMD,CAAG,GAAG,YAAW,EACpCkB,EAAO,IAAI,KAAK,CAACf,CAAG,EAAG,CAAE,KAAMa,CAAQ,CAAE,EAC/C,OAAO,IAAI,gBAAgBE,CAAI,CACnC,ECpJO,IAAIC,GACV,SAAUA,EAAe,CACtBA,EAAc,KAAU,OACxBA,EAAc,KAAU,OACxBA,EAAc,WAAgB,aAC9BA,EAAc,UAAe,YAC7BA,EAAc,YAAiB,cAC/BA,EAAc,OAAY,SAC1BA,EAAc,WAAgB,aAC9BA,EAAc,SAAc,WAC5BA,EAAc,WAAgB,aAC9BA,EAAc,MAAW,QACzBA,EAAc,SAAc,WAC5BA,EAAc,SAAc,WAC5BA,EAAc,IAAS,MACvBA,EAAc,MAAW,QACzBA,EAAc,QAAa,SAC/B,GAAGA,IAAkBA,EAAgB,CAAA,EAAG,EClBjC,MAAMC,GAAgB,IAAM,CAC/B,IAAIC,EAAY,EAChB,MAAO,IAAMA,GACjB,GAAI,ECLSC,EAAmB,IAAI,MAAM,wDAAwD,EACrFC,EAAmB,IAAI,MAAM,2BAA2B,ECS9D,MAAMC,CAAO,CAChBC,GAAU,KAKVC,GAAY,CAAA,EACZC,GAAW,CAAA,EACXC,GAAqB,CAAA,EACrBC,GAA0B,CAAA,EAC1B,OAAS,GAITC,GAAoB,IAAM,CAClB,KAAKL,KACL,KAAKA,GAAQ,UAAY,CAAC,CAAE,KAAM,CAAE,GAAAM,EAAI,KAAAC,EAAM,KAAArB,CAAI,KAAU,CACxD,OAAQqB,EAAI,CACR,KAAKb,EAAc,KACf,KAAK,OAAS,GACd,KAAKO,GAAUK,CAAE,EAAEpB,CAAI,EACvB,MACJ,KAAKQ,EAAc,MACnB,KAAKA,EAAc,QACnB,KAAKA,EAAc,KACnB,KAAKA,EAAc,WACnB,KAAKA,EAAc,UACnB,KAAKA,EAAc,YACnB,KAAKA,EAAc,OACnB,KAAKA,EAAc,WACnB,KAAKA,EAAc,SACnB,KAAKA,EAAc,WACf,KAAKO,GAAUK,CAAE,EAAEpB,CAAI,EACvB,MACJ,KAAKQ,EAAc,IACf,KAAKS,GAAmB,QAASK,GAAMA,EAAEtB,CAAI,CAAC,EAC9C,MACJ,KAAKQ,EAAc,SACf,KAAKU,GAAwB,QAASI,GAAMA,EAAEtB,CAAI,CAAC,EACnD,MACJ,KAAKQ,EAAc,MACf,KAAKQ,GAASI,CAAE,EAAEpB,CAAI,EACtB,KACP,CACD,OAAO,KAAKe,GAAUK,CAAE,EACxB,OAAO,KAAKJ,GAASI,CAAE,CACvC,EAEA,EAIIG,GAAQ,CAAC,CAAE,KAAAF,EAAM,KAAArB,CAAM,EAAEwB,EAAQ,CAAE,EAAEC,IAC5B,KAAKX,GAGH,IAAI,QAAQ,CAACY,EAASC,IAAW,CACpC,MAAMP,EAAKX,IACX,KAAKK,IAAW,KAAKA,GAAQ,YAAY,CAAE,GAAAM,EAAI,KAAAC,EAAM,KAAArB,GAAQwB,CAAK,EAClE,KAAKT,GAAUK,CAAE,EAAIM,EACrB,KAAKV,GAASI,CAAE,EAAIO,EACpBF,GAAQ,iBAAiB,QAAS,IAAM,CACpCE,EAAO,IAAI,aAAa,aAAaP,CAAE,eAAgB,YAAY,CAAC,CACpF,EAAe,CAAE,KAAM,EAAI,CAAE,CAC7B,CAAS,EAVU,QAAQ,OAAOT,CAAgB,EAY9C,GAAGiB,EAAOC,EAAU,CACZD,IAAU,MACV,KAAKX,GAAmB,KAAKY,CAAQ,EAEhCD,IAAU,YACf,KAAKV,GAAwB,KAAKW,CAAQ,CAEjD,CACD,IAAID,EAAOC,EAAU,CACbD,IAAU,MACV,KAAKX,GAAqB,KAAKA,GAAmB,OAAQK,GAAMA,IAAMO,CAAQ,EAEzED,IAAU,aACf,KAAKV,GAA0B,KAAKA,GAAwB,OAAQI,GAAMA,IAAMO,CAAQ,EAE/F,CAQD,KAAO,CAACC,EAAS,CAAA,EAAI,CAAE,OAAAL,CAAM,EAAK,CAAA,KACzB,KAAKX,KACN,KAAKA,GAAU,IAAI,OAAO,IAAA,IAAA,GAAA,IAAA,IAAA,qBAAA,YAAA,GAAA,EAAA,KAAA,YAAA,GAAA,EAAyC,CAC/D,KAAM,QACtB,CAAa,EACD,KAAKK,GAAiB,GAEnB,KAAKI,GAAM,CACd,KAAMf,EAAc,KACpB,KAAMsB,CAClB,EAAW,OAAWL,CAAM,GAsBxB,KAAO,CAEPM,EAMAC,EAAU,GAAI,CAAE,OAAAP,CAAQ,EAAG,CAAE,IAAK,KAAKF,GAAM,CACzC,KAAMf,EAAc,KACpB,KAAM,CAAE,KAAAuB,EAAM,QAAAC,CAAS,CAC/B,EAAO,OAAWP,CAAM,EAOpB,UAAY,IAAM,CACd,MAAMQ,EAAM,OAAO,KAAK,KAAKjB,EAAQ,EAErC,UAAWI,KAAMa,EACb,KAAKjB,GAASI,CAAE,EAAER,CAAgB,EAClC,OAAO,KAAKI,GAASI,CAAE,EACvB,OAAO,KAAKL,GAAUK,CAAE,EAExB,KAAKN,KACL,KAAKA,GAAQ,YACb,KAAKA,GAAU,KACf,KAAK,OAAS,GAE1B,EAcI,UAAY,CAACoB,EAAMlC,EAAM,CAAE,OAAAyB,CAAM,EAAK,CAAA,IAAO,CACzC,MAAMD,EAAQ,CAAA,EACd,OAAIxB,aAAgB,YAChBwB,EAAM,KAAKxB,EAAK,MAAM,EAEnB,KAAKuB,GAAM,CACd,KAAMf,EAAc,WACpB,KAAM,CAAE,KAAA0B,EAAM,KAAAlC,CAAM,CAChC,EAAWwB,EAAOC,CAAM,CACxB,EACI,MAAQ,CAACU,EAAQC,EAASC,IAAe,CACrC,MAAMb,EAAQ,CAAA,EACd,OAAO,KAAKD,GAAM,CACd,KAAMf,EAAc,MACpB,KAAM,CAAE,OAAA2B,EAAQ,QAAAC,EAAS,WAAAC,CAAY,CACxC,EAAEb,CAAK,CAChB,EACI,QAAWa,GAAe,CACtB,MAAMb,EAAQ,CAAA,EACd,OAAO,KAAKD,GAAM,CACd,KAAMf,EAAc,QACpB,KAAM,CAAE,WAAA6B,CAAY,CACvB,EAAEb,CAAK,CAChB,EAaI,SAAW,CAACU,EAQZI,EAAW,SAAU,CAAE,OAAAb,CAAQ,EAAG,CAAE,IAAK,KAAKF,GAAM,CAChD,KAAMf,EAAc,UACpB,KAAM,CAAE,KAAA0B,EAAM,SAAAI,CAAU,CAChC,EAAO,OAAWb,CAAM,EAMpB,WAAa,CAACS,EAAM,CAAE,OAAAT,CAAQ,EAAG,CAAE,IAAK,KAAKF,GAAM,CAC/C,KAAMf,EAAc,YACpB,KAAM,CAAE,KAAA0B,CAAM,CACtB,EAAO,OAAWT,CAAM,EAMpB,OAAS,CAACc,EAASC,EAAS,CAAE,OAAAf,CAAM,EAAK,CAAE,IAAK,KAAKF,GAAM,CACvD,KAAMf,EAAc,OACpB,KAAM,CAAE,QAAA+B,EAAS,QAAAC,CAAS,CAClC,EAAO,OAAWf,CAAM,EAMpB,UAAY,CAACS,EAAM,CAAE,OAAAT,CAAQ,EAAG,CAAE,IAAK,KAAKF,GAAM,CAC9C,KAAMf,EAAc,WACpB,KAAM,CAAE,KAAA0B,CAAM,CACtB,EAAO,OAAWT,CAAM,EAMpB,QAAU,CAACS,EAAM,CAAE,OAAAT,CAAQ,EAAG,CAAE,IAAK,KAAKF,GAAM,CAC5C,KAAMf,EAAc,SACpB,KAAM,CAAE,KAAA0B,CAAM,CACtB,EAAO,OAAWT,CAAM,EAMpB,UAAY,CAACS,EAAM,CAAE,OAAAT,CAAQ,EAAG,CAAE,IAAK,KAAKF,GAAM,CAC9C,KAAMf,EAAc,WACpB,KAAM,CAAE,KAAA0B,CAAM,CACtB,EAAO,OAAWT,CAAM,CACxB,CC9QA,MAAMgB,EAAQ,CACZ,MAAO,cACP,MAAO,aACP,OAAQ,aACR,MAAO,YACP,IAAO,YACP,GAAM,6BACN,IAAO,cACP,KAAQ,YACR,GAAM,yBACN,IAAO,mCACP,KAAQ,oCACR,IAAO,YACP,KAAQ,aACR,SAAY,sBACZ,aAAgB,2BAChB,KAAQ,mBACR,WAAc,yBACd,IAAO,uBACP,KAAQ,uBACR,QAAW,0BACX,YAAe,8BACf,QAAW,0BACX,GAAM,cACN,KAAQ,aACR,KAAQ,aACR,KAAQ,aACR,GAAM,yBACN,KAAQ,mBACR,IAAO,2BACP,IAAO,YACP,IAAO,2BACP,IAAO,iBACP,KAAQ,iBACR,OAAU,2BACV,MAAS,wBACT,KAAQ,uBACR,MAAS,8BACT,MAAS,6BACT,MAAS,0BACT,MAAS,0BACT,MAAS,yBACT,IAAO,wBACP,IAAO,YACP,IAAO,mBACP,MAAS,sBACT,OAAU,oBACV,KAAQ,aACR,IAAO,sBACP,IAAO,6BACP,IAAO,uBACP,IAAO,WACP,IAAO,WACP,GAAM,uBACN,IAAO,kBACP,IAAO,sBACP,SAAY,2BACZ,IAAO,0BACP,IAAO,2BACP,IAAO,aACP,OAAU,2BACV,IAAO,YACP,2BAA4B,mCAC5B,KAAQ,2BACR,MAAS,2BACT,IAAO,2BACP,IAAO,2BACP,IAAO,2BACP,IAAO,qBACP,IAAO,qBACP,IAAO,YACP,KAAQ,kBACR,IAAO,qBACP,KAAQ,uBACR,IAAO,sBACP,KAAQ,2BACR,IAAO,2BACP,IAAO,2BACP,KAAQ,yBACR,IAAO,2BACP,IAAO,YACP,IAAO,iBACP,KAAQ,uBACR,UAAa,4BACb,IAAO,yBACP,KAAQ,uBACR,IAAO,2BACP,IAAO,kBACP,IAAO,sBACP,IAAO,aACP,GAAM,2BACN,IAAO,kBACP,IAAO,sBACP,KAAQ,aACR,GAAM,cACN,IAAO,gCACP,QAAW,uBACX,IAAO,YACP,IAAO,oBACP,KAAQ,kBACR,IAAO,sBACP,IAAO,sBACP,KAAQ,mBACR,MAAS,uBACT,IAAO,kBACP,GAAM,mBACN,KAAQ,aACR,KAAQ,aACR,KAAQ,aACR,KAAQ,aACR,MAAS,sBACT,KAAQ,aACR,MAAS,sBACT,KAAQ,cACR,KAAQ,4BACR,MAAS,oBACT,IAAO,qBACP,IAAO,2BACP,KAAQ,aACR,IAAO,YACP,KAAQ,YACR,IAAO,gBACP,IAAO,YACP,IAAO,gBACP,KAAQ,aACR,IAAO,aACP,IAAO,2BACP,GAAM,aACN,IAAO,aACP,IAAO,wBACP,MAAS,wBACT,MAAS,oBACT,IAAO,2BACP,IAAO,sBACP,KAAQ,YACR,IAAO,2BACP,IAAO,aACP,IAAO,YACP,IAAO,YACP,IAAO,aACP,KAAQ,aACR,IAAO,YACP,IAAO,aACP,KAAQ,YACR,KAAQ,YACR,KAAQ,aACR,IAAO,YACP,IAAO,YACP,IAAO,YACP,GAAM,kBACN,KAAQ,mBACR,MAAS,oBACT,OAAU,sBACV,OAAU,0BACV,IAAO,WACP,GAAM,WACN,IAAO,YACP,KAAQ,aACR,KAAQ,aACR,IAAO,YACP,KAAQ,aACR,KAAQ,aACR,KAAQ,aACR,IAAO,aACP,IAAO,YACP,KAAQ,aACR,KAAQ,YACR,IAAO,sBACP,KAAQ,aACR,UAAa,oBACb,IAAO,aACP,QAAW,uBACX,IAAO,2BACP,IAAO,aACP,IAAO,mBACP,IAAO,aACP,IAAO,aACP,IAAO,aACP,IAAO,YACP,IAAO,kBACP,IAAO,oBACP,GAAM,0BACN,KAAQ,uBACR,KAAQ,0BACR,IAAO,aACP,SAAY,sBACZ,IAAO,mBACP,IAAO,2BACP,SAAY,gBACZ,OAAU,yBACV,GAAM,0BACN,KAAQ,mBACR,GAAM,gBACN,IAAO,WACP,GAAM,aACN,KAAQ,aACR,MAAS,4BACT,SAAY,2BACZ,KAAQ,uBACR,IAAO,4BACP,IAAO,aACP,KAAQ,aACR,KAAQ,iBACR,IAAO,YACP,KAAQ,YACR,IAAO,kBACP,IAAO,cACP,KAAQ,uBACR,IAAO,kBACP,IAAO,aACP,KAAQ,mBACR,KAAQ,aACR,IAAO,aACP,IAAO,YACP,KAAQ,YACR,KAAQ,kBACR,KAAQ,YACR,IAAO,uBACP,IAAO,aACP,KAAQ,aACR,IAAO,uCACP,IAAO,aACP,KAAQ,YACR,KAAQ,aACR,IAAO,6BACP,IAAO,mBACP,KAAQ,0BACR,GAAM,aACN,MAAS,qCACT,IAAO,aACP,IAAO,2BACP,KAAQ,mBACR,WAAc,yBACd,IAAO,2BACP,IAAO,2BACP,IAAO,YACP,KAAQ,0BACR,IAAO,kBACP,KAAQ,mBACR,KAAQ,qBACR,GAAM,UACN,GAAM,0BACN,GAAM,sBACN,GAAM,wBACN,IAAO,YACP,IAAO,kBACP,IAAO,YACP,IAAO,YACP,IAAO,YACP,IAAO,kBACP,MAAS,wBACT,OAAU,sBACV,OAAU,sBACV,OAAU,sBACV,QAAW,sBACX,IAAO,gCACP,KAAQ,YACR,IAAO,WACP,IAAO,sBACP,KAAQ,mBACR,IAAO,qBACP,IAAO,yBACP,IAAO,yBACP,IAAO,8BACP,GAAM,oBACN,IAAO,kBACP,IAAO,yBACP,IAAO,4BACP,IAAO,2BACP,IAAO,sBACP,QAAW,2BACX,IAAO,sBACP,IAAO,YACP,IAAO,YACP,IAAO,yBACP,MAAS,6BACT,GAAM,yBACN,QAAW,uBACX,IAAO,gBACP,GAAM,kBACN,KAAQ,wBACR,KAAQ,4BACR,IAAO,sBACP,KAAQ,8BACR,IAAO,0BACP,GAAM,iCACN,IAAO,sCACP,IAAO,aACP,IAAO,sCACP,IAAO,kBACP,IAAO,uBACP,KAAQ,aACR,GAAM,2BACN,GAAM,+BACN,KAAQ,4BACR,IAAO,sBACP,OAAU,+BACV,IAAO,sBACP,IAAO,WACP,IAAO,gBACP,KAAQ,4BACR,IAAO,YACP,KAAQ,uBACR,IAAO,8BACP,IAAO,+BACP,IAAO,kBACP,OAAU,wBACV,QAAW,yBACX,IAAO,qCACP,OAAU,qCACV,OAAU,0CACV,IAAO,YACP,IAAO,YACP,KAAQ,YACR,KAAQ,YACR,IAAO,sBACP,MAAS,YACT,MAAS,oBACT,IAAO,4BACP,IAAO,aACP,KAAQ,aACR,IAAO,oBACP,KAAQ,YACR,IAAO,YACP,IAAO,+BACP,IAAO,uBACP,KAAQ,uBACR,IAAO,cACP,GAAM,2BACN,KAAQ,YACR,IAAO,+BACP,IAAO,8BACP,IAAO,YACP,IAAO,kBACP,IAAO,sBACP,IAAO,iCACP,KAAQ,uBACR,KAAQ,uBACR,IAAO,0BACP,IAAO,YACP,KAAQ,iBACR,MAAS,qBACT,KAAQ,iBACR,KAAQ,cACR,OAAU,cACV,IAAO,gBACP,KAAQ,gBACR,QAAW,uBACX,EAAK,aACL,IAAO,YACP,GAAM,iCACN,IAAO,sBACP,UAAa,sBACb,KAAQ,aACR,IAAO,yBACP,IAAO,gBACP,IAAO,aACP,KAAQ,aACR,KAAQ,mBACR,GAAM,aACN,KAAQ,mBACR,GAAM,aACN,IAAO,+BACP,IAAO,4BACP,IAAO,kBACP,IAAO,WACP,IAAO,cACP,KAAQ,uBACR,IAAO,aACP,IAAO,YACP,MAAS,iCACT,MAAS,yBACT,MAAS,0CACT,MAAS,iBACT,IAAO,qBACP,IAAO,gBACP,KAAQ,gBACR,KAAQ,gBACR,MAAS,aACT,KAAQ,aACR,IAAO,WACP,KAAQ,2BACR,IAAO,2BACP,KAAQ,mBACR,IAAO,YACP,KAAQ,aACR,KAAQ,aACR,YAAe,4BACf,KAAQ,aACR,KAAQ,YACR,IAAO,qBACP,IAAO,8BACP,IAAO,YACP,KAAQ,YACR,MAAS,aACT,IAAO,aACP,KAAQ,uBACR,SAAY,2BACZ,IAAO,gBACP,KAAQ,wBACR,MAAS,mBACT,KAAQ,iBACR,MAAS,iBACT,KAAQ,gBACR,KAAQ,uBACR,IAAO,2BACP,IAAO,4BACP,IAAO,2BACP,IAAO,4BACP,MAAS,uBACT,IAAO,0BACP,KAAQ,uBACR,IAAO,kCACP,KAAQ,mBACR,IAAO,wBACP,MAAS,wBACT,MAAS,qBACT,IAAO,wBACP,GAAM,WACN,IAAO,WACP,IAAO,0BACP,IAAO,sBACP,IAAO,wBACP,IAAO,kBACP,IAAO,0BACP,IAAO,kBACP,KAAQ,kBACR,KAAQ,uBACR,IAAO,qBACP,KAAQ,qBACR,KAAQ,YACR,KAAQ,mBACR,IAAO,sBACP,IAAO,YACP,IAAO,iBACT,EAEA,SAASC,EAAOC,EAAM,CACrB,IAAIC,GAAO,GAAKD,GAAM,KAAI,EAAG,cACzBE,EAAMD,EAAI,YAAY,GAAG,EAC7B,OAAOH,EAAO,CAACI,EAAYD,EAAI,UAAU,EAAEC,CAAG,EAAzBD,CAA0B,CAChD,CCrba,MAAAE,GAAeC,GAA0B,CACrD,IAAIC,EAAQ,CAAC,IAAK,KAAM,KAAM,KAAM,IAAI,EACpCC,EAAI,EACR,KAAOF,EAAQ,MACLA,GAAA,KACTE,IAEG,IAAAC,EAAOF,EAAMC,CAAC,EAClB,OAAOF,EAAM,QAAQ,CAAC,EAAI,IAAMG,CACjC,EAEaC,GAAW,IAKhB,GAGD,SAASC,EACfC,EACA,CAAE,SAAAC,GACI,CACN,eAAeC,GAAiC,CAC1CD,GACL,MAAMD,EAAK,MACZ,CAEK,OAAAA,EAAA,iBAAiB,aAAcE,CAAe,EAE5C,CACN,SAAgB,CACVF,EAAA,oBAAoB,aAAcE,CAAe,CACvD,CAAA,CAEF,CAEA,eAA8BC,IAA8B,CACrD,MAAAC,EAAS,IAAI5C,EACb6C,EAAU,iDAEhB,aAAMD,EAAO,KAAK,CACjB,QAAS,MAAMrD,EAAU,GAAGsD,CAAO,kBAAmB,iBAAiB,EACvE,QAAS,MAAMtD,EAAU,GAAGsD,CAAO,oBAAqB,kBAAkB,CAAA,CAC1E,EAEMD,CACR,CAWA,eAAsBE,GACrBF,EACAG,EACAC,EACAC,EACe,CACf,MAAMC,EAAWD,EAAa,IACxBzD,EAAWqC,EAAOoB,EAAa,GAAG,GAAK,YACvCE,EAAU,MAAM5D,EAAU2D,EAAU1D,CAAQ,EAE5C4D,EAAU,MADC,MAAM,MAAMD,CAAO,GACL,OACzB3C,EAAO6C,EAA8B7D,CAAQ,GAAK,MAClD8D,EAAY,SAAS9C,CAAI,GACzB+C,EAAa,UAAU/C,CAAI,GAE7B,GAAA,CACC,GAAAuC,IAAc,GAAKC,IAAY,EAC3B,OAAAI,EAGR,MAAMR,EAAO,UACZU,EACA,IAAI,WAAW,MAAMF,EAAQ,aAAa,CAAA,EAG3C,IAAII,EAAU,CACb,KACAF,EACA,GAAIP,IAAc,EAAI,CAAC,MAAOA,EAAU,SAAU,CAAA,EAAI,CAAC,EACvD,GAAIC,IAAY,EAAI,CAAC,MAAOA,EAAQ,SAAU,CAAA,EAAI,CAAC,EACnD,OACA,OACAO,CAAA,EAGK,MAAAX,EAAO,KAAKY,CAAO,EACzB,MAAMC,EAAa,MAAMb,EAAO,SAASW,CAAU,EAK5C,OAJY,IAAI,KAAK,CAACE,CAAU,EAAG,CACzC,KAAM,SAASjD,CAAI,EAAA,CACnB,QAGOkD,EAAO,CACP,eAAA,MAAM,6BAA8BA,CAAK,EAC1CN,CACR,CACD,CAEA,MAAMC,EAAiC7D,IACqB,CAC1D,YAAa,MACb,aAAc,OACd,YAAa,MACb,kBAAmB,MACnB,kBAAmB,MACnB,mBAAoB,MACpB,aAAc,OACd,aAAc,MACd,cAAe,MACf,aAAc,OACd,aAAc,OACd,aAAc,OACd,aAAc,OACd,YAAa,MACb,YAAa,MACb,YAAa,MACb,mCAAoC,MACpC,qBAAsB,MACtB,iBAAkB,MAClB,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,iBAAkB,MAClB,gBAAiB,KACjB,iBAAkB,MAClB,iBAAkB,MAClB,iBAAkB,MAClB,oBAAqB,QACrB,cAAe,KAAA,GAGeA,CAAQ,GAAK,mbC9InC,CAAA,sBAAAmE,EAAA,SAAqC,kZAuD3BC,EAAe,EAAA,CAAA,YAM7BA,EAAY,EAAA,CAAA,GAAAC,EAAAC,EAAA,MAAAC,CAAA,kGAsBJF,EAAAC,EAAA,cAAAE,EAAAJ,MAAQ,aAAa,CAAA,8EA5BnCK,EAIKC,EAAAC,EAAAC,CAAA,WACLH,EA2BOC,EAAAJ,EAAAM,CAAA,sCAnBSC,EAAAP,EAAA,aAAAF,EAAS,EAAA,EAAA,KAAK,KAAM,YAAY,CAAA,EACrCS,EAAAP,EAAA,QAAAF,EAAS,EAAA,EAAA,KAAK,KAAM,OAAO,CAAA,EAC5BS,EAAAP,EAAA,OAAAF,EAAS,EAAA,EAAA,KAAK,KAAM,MAAM,CAAA,EACzBS,EAAAP,EAAA,QAAAF,EAAS,EAAA,EAAA,KAAK,KAAM,OAAO,CAAA,EAC3BS,EAAAP,EAAA,QAAAF,EAAS,EAAA,EAAA,KAAK,KAAM,OAAO,CAAA,EACvBS,EAAAP,EAAA,YAAAF,EAAS,EAAA,EAAA,KAAK,KAAM,WAAW,CAAA,EAChCS,EAAAP,EAAA,WAAAF,EAAS,EAAA,EAAA,KAAK,KAAM,UAAU,CAAA,EACjCS,EAAAP,EAAA,QAAAF,EAAS,EAAA,EAAA,KAAK,KAAM,OAAO,CAAA,EAC5BS,EAAAP,EAAA,OAAAF,EAAS,EAAA,EAAA,KAAK,KAAM,MAAM,CAAA,4HAMrB,SAAUA,EAAQ,CAAA,GAAI,EAAK,CAAA,CAAA,+CA3BtBA,EAAe,EAAA,CAAA,qGAM7BA,EAAY,EAAA,CAAA,uMAsBJ,CAAAU,GAAAC,EAAA,MAAAP,KAAAA,EAAAJ,MAAQ,aAAa,gKADpB,SAAUA,EAAQ,CAAA,GAAI,EAAK,CAAA,2JA7E9B,CAAA,IAAAY,EAAkC,MAAS,EAAAC,EAE3C,CAAA,MAAAC,EAAsC,MAAS,EAAAD,EAC/C,CAAA,YAAAE,EAAkD,MAAS,EAAAF,EAC3D,CAAA,QAAAG,EAA0C,MAAS,EAAAH,EACnD,CAAA,SAAAhC,EAA4C,MAAS,EAAAgC,EACrD,CAAA,SAAAI,EAA4C,MAAS,EAAAJ,EAErD,CAAA,YAAAK,EAAkC,MAAS,EAAAL,EAC3C,CAAA,SAAAM,EAA+B,MAAS,EAAAN,EACxC,CAAA,OAAAO,EAA8B,MAAS,EAAAP,EAEvC,CAAA,KAAAjC,EAAqC,MAAS,EAAAiC,GAC9C,KAAAQ,CAAa,EAAAR,EAEb,CAAA,gBAAAS,EAAkB,EAAK,EAAAT,EAE9BU,EAKAC,EAkBE,MAAAC,EAAW1B,4LAoCNnB,EAAI8C,2gBArDd,CAMAC,EAAA,GAAAJ,EAAeX,CAAG,EAElBe,EAAA,GAAAH,EAAaZ,CAAG,EACV,MAAAgB,EAAgBhB,EACtBiB,EAAiBD,CAAa,EAAE,KAAME,GAAC,CAClCN,IAAeI,GAClBD,EAAA,GAAAJ,EAAeO,CAAC", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}
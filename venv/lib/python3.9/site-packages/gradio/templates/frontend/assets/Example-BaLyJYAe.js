const{SvelteComponent:o,attr:g,detach:d,element:_,flush:i,init:h,insert:v,noop:r,safe_not_equal:b,set_style:f,toggle_class:n}=window.__gradio__svelte__internal;function y(s){let e;return{c(){e=_("div"),f(e,"background-color",s[0]?s[0]:"black"),g(e,"class","svelte-h6ogpl"),n(e,"table",s[1]==="table"),n(e,"gallery",s[1]==="gallery"),n(e,"selected",s[2])},m(t,l){v(t,e,l)},p(t,[l]){l&1&&f(e,"background-color",t[0]?t[0]:"black"),l&2&&n(e,"table",t[1]==="table"),l&2&&n(e,"gallery",t[1]==="gallery"),l&4&&n(e,"selected",t[2])},i:r,o:r,d(t){t&&d(e)}}}function m(s,e,t){let{value:l}=e,{type:c}=e,{selected:u=!1}=e;return s.$$set=a=>{"value"in a&&t(0,l=a.value),"type"in a&&t(1,c=a.type),"selected"in a&&t(2,u=a.selected)},[l,c,u]}class k extends o{constructor(e){super(),h(this,e,m,y,b,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),i()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),i()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),i()}}export{k as default};
//# sourceMappingURL=Example-BaLyJYAe.js.map

{"version": 3, "file": "dockerfile-C8FRPiPq.js", "sources": ["../../../../node_modules/.pnpm/@codemirror+legacy-modes@6.3.1/node_modules/@codemirror/legacy-modes/mode/simple-mode.js", "../../../../node_modules/.pnpm/@codemirror+legacy-modes@6.3.1/node_modules/@codemirror/legacy-modes/mode/dockerfile.js"], "sourcesContent": ["export function simpleMode(states) {\n  ensureState(states, \"start\");\n  var states_ = {}, meta = states.languageData || {}, hasIndentation = false;\n  for (var state in states) if (state != meta && states.hasOwnProperty(state)) {\n    var list = states_[state] = [], orig = states[state];\n    for (var i = 0; i < orig.length; i++) {\n      var data = orig[i];\n      list.push(new Rule(data, states));\n      if (data.indent || data.dedent) hasIndentation = true;\n    }\n  }\n  return {\n    name: meta.name,\n    startState: function() {\n      return {state: \"start\", pending: null, indent: hasIndentation ? [] : null};\n    },\n    copyState: function(state) {\n      var s = {state: state.state, pending: state.pending, indent: state.indent && state.indent.slice(0)};\n      if (state.stack)\n        s.stack = state.stack.slice(0);\n      return s;\n    },\n    token: tokenFunction(states_),\n    indent: indentFunction(states_, meta),\n    languageData: meta\n  }\n};\n\nfunction ensureState(states, name) {\n  if (!states.hasOwnProperty(name))\n    throw new Error(\"Undefined state \" + name + \" in simple mode\");\n}\n\nfunction toRegex(val, caret) {\n  if (!val) return /(?:)/;\n  var flags = \"\";\n  if (val instanceof RegExp) {\n    if (val.ignoreCase) flags = \"i\";\n    val = val.source;\n  } else {\n    val = String(val);\n  }\n  return new RegExp((caret === false ? \"\" : \"^\") + \"(?:\" + val + \")\", flags);\n}\n\nfunction asToken(val) {\n  if (!val) return null;\n  if (val.apply) return val\n  if (typeof val == \"string\") return val.replace(/\\./g, \" \");\n  var result = [];\n  for (var i = 0; i < val.length; i++)\n    result.push(val[i] && val[i].replace(/\\./g, \" \"));\n  return result;\n}\n\nfunction Rule(data, states) {\n  if (data.next || data.push) ensureState(states, data.next || data.push);\n  this.regex = toRegex(data.regex);\n  this.token = asToken(data.token);\n  this.data = data;\n}\n\nfunction tokenFunction(states) {\n  return function(stream, state) {\n    if (state.pending) {\n      var pend = state.pending.shift();\n      if (state.pending.length == 0) state.pending = null;\n      stream.pos += pend.text.length;\n      return pend.token;\n    }\n\n    var curState = states[state.state];\n    for (var i = 0; i < curState.length; i++) {\n      var rule = curState[i];\n      var matches = (!rule.data.sol || stream.sol()) && stream.match(rule.regex);\n      if (matches) {\n        if (rule.data.next) {\n          state.state = rule.data.next;\n        } else if (rule.data.push) {\n          (state.stack || (state.stack = [])).push(state.state);\n          state.state = rule.data.push;\n        } else if (rule.data.pop && state.stack && state.stack.length) {\n          state.state = state.stack.pop();\n        }\n\n        if (rule.data.indent)\n          state.indent.push(stream.indentation() + stream.indentUnit);\n        if (rule.data.dedent)\n          state.indent.pop();\n        var token = rule.token\n        if (token && token.apply) token = token(matches)\n        if (matches.length > 2 && rule.token && typeof rule.token != \"string\") {\n          state.pending = [];\n          for (var j = 2; j < matches.length; j++)\n            if (matches[j])\n              state.pending.push({text: matches[j], token: rule.token[j - 1]});\n          stream.backUp(matches[0].length - (matches[1] ? matches[1].length : 0));\n          return token[0];\n        } else if (token && token.join) {\n          return token[0];\n        } else {\n          return token;\n        }\n      }\n    }\n    stream.next();\n    return null;\n  };\n}\n\nfunction indentFunction(states, meta) {\n  return function(state, textAfter) {\n    if (state.indent == null || meta.dontIndentStates && meta.doneIndentState.indexOf(state.state) > -1)\n      return null\n\n    var pos = state.indent.length - 1, rules = states[state.state];\n    scan: for (;;) {\n      for (var i = 0; i < rules.length; i++) {\n        var rule = rules[i];\n        if (rule.data.dedent && rule.data.dedentIfLineStart !== false) {\n          var m = rule.regex.exec(textAfter);\n          if (m && m[0]) {\n            pos--;\n            if (rule.next || rule.push) rules = states[rule.next || rule.push];\n            textAfter = textAfter.slice(m[0].length);\n            continue scan;\n          }\n        }\n      }\n      break;\n    }\n    return pos < 0 ? 0 : state.indent[pos];\n  };\n}\n", "import {simpleMode} from \"./simple-mode.js\"\n\nvar from = \"from\";\nvar fromRegex = new RegExp(\"^(\\\\s*)\\\\b(\" + from + \")\\\\b\", \"i\");\n\nvar shells = [\"run\", \"cmd\", \"entrypoint\", \"shell\"];\nvar shellsAsArrayRegex = new RegExp(\"^(\\\\s*)(\" + shells.join('|') + \")(\\\\s+\\\\[)\", \"i\");\n\nvar expose = \"expose\";\nvar exposeRegex = new RegExp(\"^(\\\\s*)(\" + expose + \")(\\\\s+)\", \"i\");\n\nvar others = [\n  \"arg\", \"from\", \"maintainer\", \"label\", \"env\",\n  \"add\", \"copy\", \"volume\", \"user\",\n  \"workdir\", \"onbuild\", \"stopsignal\", \"healthcheck\", \"shell\"\n];\n\n// Collect all Dockerfile directives\nvar instructions = [from, expose].concat(shells).concat(others),\n    instructionRegex = \"(\" + instructions.join('|') + \")\",\n    instructionOnlyLine = new RegExp(\"^(\\\\s*)\" + instructionRegex + \"(\\\\s*)(#.*)?$\", \"i\"),\n    instructionWithArguments = new RegExp(\"^(\\\\s*)\" + instructionRegex + \"(\\\\s+)\", \"i\");\n\nexport const dockerFile = simpleMode({\n  start: [\n    // Block comment: This is a line starting with a comment\n    {\n      regex: /^\\s*#.*$/,\n      sol: true,\n      token: \"comment\"\n    },\n    {\n      regex: fromRegex,\n      token: [null, \"keyword\"],\n      sol: true,\n      next: \"from\"\n    },\n    // Highlight an instruction without any arguments (for convenience)\n    {\n      regex: instructionOnlyLine,\n      token: [null, \"keyword\", null, \"error\"],\n      sol: true\n    },\n    {\n      regex: shellsAsArrayRegex,\n      token: [null, \"keyword\", null],\n      sol: true,\n      next: \"array\"\n    },\n    {\n      regex: exposeRegex,\n      token: [null, \"keyword\", null],\n      sol: true,\n      next: \"expose\"\n    },\n    // Highlight an instruction followed by arguments\n    {\n      regex: instructionWithArguments,\n      token: [null, \"keyword\", null],\n      sol: true,\n      next: \"arguments\"\n    },\n    {\n      regex: /./,\n      token: null\n    }\n  ],\n  from: [\n    {\n      regex: /\\s*$/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      // Line comment without instruction arguments is an error\n      regex: /(\\s*)(#.*)$/,\n      token: [null, \"error\"],\n      next: \"start\"\n    },\n    {\n      regex: /(\\s*\\S+\\s+)(as)/i,\n      token: [null, \"keyword\"],\n      next: \"start\"\n    },\n    // Fail safe return to start\n    {\n      token: null,\n      next: \"start\"\n    }\n  ],\n  single: [\n    {\n      regex: /(?:[^\\\\']|\\\\.)/,\n      token: \"string\"\n    },\n    {\n      regex: /'/,\n      token: \"string\",\n      pop: true\n    }\n  ],\n  double: [\n    {\n      regex: /(?:[^\\\\\"]|\\\\.)/,\n      token: \"string\"\n    },\n    {\n      regex: /\"/,\n      token: \"string\",\n      pop: true\n    }\n  ],\n  array: [\n    {\n      regex: /\\]/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      regex: /\"(?:[^\\\\\"]|\\\\.)*\"?/,\n      token: \"string\"\n    }\n  ],\n  expose: [\n    {\n      regex: /\\d+$/,\n      token: \"number\",\n      next: \"start\"\n    },\n    {\n      regex: /[^\\d]+$/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      regex: /\\d+/,\n      token: \"number\"\n    },\n    {\n      regex: /[^\\d]+/,\n      token: null\n    },\n    // Fail safe return to start\n    {\n      token: null,\n      next: \"start\"\n    }\n  ],\n  arguments: [\n    {\n      regex: /^\\s*#.*$/,\n      sol: true,\n      token: \"comment\"\n    },\n    {\n      regex: /\"(?:[^\\\\\"]|\\\\.)*\"?$/,\n      token: \"string\",\n      next: \"start\"\n    },\n    {\n      regex: /\"/,\n      token: \"string\",\n      push: \"double\"\n    },\n    {\n      regex: /'(?:[^\\\\']|\\\\.)*'?$/,\n      token: \"string\",\n      next: \"start\"\n    },\n    {\n      regex: /'/,\n      token: \"string\",\n      push: \"single\"\n    },\n    {\n      regex: /[^#\"']+[\\\\`]$/,\n      token: null\n    },\n    {\n      regex: /[^#\"']+$/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      regex: /[^#\"']+/,\n      token: null\n    },\n    // Fail safe return to start\n    {\n      token: null,\n      next: \"start\"\n    }\n  ],\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n});\n\n"], "names": ["simpleMode", "states", "ensureState", "states_", "meta", "hasIndentation", "state", "list", "orig", "i", "data", "Rule", "s", "tokenFunction", "indentFunction", "name", "toRegex", "val", "caret", "flags", "asToken", "result", "stream", "pend", "curState", "rule", "matches", "token", "j", "textAfter", "pos", "rules", "scan", "m", "from", "fromRegex", "shells", "shellsAsArrayRegex", "expose", "exposeRegex", "others", "instructions", "instructionRegex", "instructionOnlyLine", "instructionWithArguments", "dockerFile"], "mappings": "AAAO,SAASA,EAAWC,EAAQ,CACjCC,EAAYD,EAAQ,OAAO,EAC3B,IAAIE,EAAU,CAAE,EAAEC,EAAOH,EAAO,cAAgB,CAAE,EAAEI,EAAiB,GACrE,QAASC,KAASL,EAAQ,GAAIK,GAASF,GAAQH,EAAO,eAAeK,CAAK,EAExE,QADIC,EAAOJ,EAAQG,CAAK,EAAI,CAAE,EAAEE,EAAOP,EAAOK,CAAK,EAC1CG,EAAI,EAAGA,EAAID,EAAK,OAAQC,IAAK,CACpC,IAAIC,EAAOF,EAAKC,CAAC,EACjBF,EAAK,KAAK,IAAII,EAAKD,EAAMT,CAAM,CAAC,GAC5BS,EAAK,QAAUA,EAAK,UAAQL,EAAiB,GAClD,CAEH,MAAO,CACL,KAAMD,EAAK,KACX,WAAY,UAAW,CACrB,MAAO,CAAC,MAAO,QAAS,QAAS,KAAM,OAAQC,EAAiB,GAAK,IAAI,CAC1E,EACD,UAAW,SAASC,EAAO,CACzB,IAAIM,EAAI,CAAC,MAAON,EAAM,MAAO,QAASA,EAAM,QAAS,OAAQA,EAAM,QAAUA,EAAM,OAAO,MAAM,CAAC,CAAC,EAClG,OAAIA,EAAM,QACRM,EAAE,MAAQN,EAAM,MAAM,MAAM,CAAC,GACxBM,CACR,EACD,MAAOC,EAAcV,CAAO,EAC5B,OAAQW,EAAeX,EAASC,CAAI,EACpC,aAAcA,CACf,CACH,CAEA,SAASF,EAAYD,EAAQc,EAAM,CACjC,GAAI,CAACd,EAAO,eAAec,CAAI,EAC7B,MAAM,IAAI,MAAM,mBAAqBA,EAAO,iBAAiB,CACjE,CAEA,SAASC,EAAQC,EAAKC,EAAO,CAC3B,GAAI,CAACD,EAAK,MAAO,OACjB,IAAIE,EAAQ,GACZ,OAAIF,aAAe,QACbA,EAAI,aAAYE,EAAQ,KAC5BF,EAAMA,EAAI,QAEVA,EAAM,OAAOA,CAAG,EAEX,IAAI,OAA+B,OAAeA,EAAM,IAAKE,CAAK,CAC3E,CAEA,SAASC,EAAQH,EAAK,CACpB,GAAI,CAACA,EAAK,OAAO,KACjB,GAAIA,EAAI,MAAO,OAAOA,EACtB,GAAI,OAAOA,GAAO,SAAU,OAAOA,EAAI,QAAQ,MAAO,GAAG,EAEzD,QADII,EAAS,CAAA,EACJZ,EAAI,EAAGA,EAAIQ,EAAI,OAAQR,IAC9BY,EAAO,KAAKJ,EAAIR,CAAC,GAAKQ,EAAIR,CAAC,EAAE,QAAQ,MAAO,GAAG,CAAC,EAClD,OAAOY,CACT,CAEA,SAASV,EAAKD,EAAMT,EAAQ,EACtBS,EAAK,MAAQA,EAAK,OAAMR,EAAYD,EAAQS,EAAK,MAAQA,EAAK,IAAI,EACtE,KAAK,MAAQM,EAAQN,EAAK,KAAK,EAC/B,KAAK,MAAQU,EAAQV,EAAK,KAAK,EAC/B,KAAK,KAAOA,CACd,CAEA,SAASG,EAAcZ,EAAQ,CAC7B,OAAO,SAASqB,EAAQhB,EAAO,CAC7B,GAAIA,EAAM,QAAS,CACjB,IAAIiB,EAAOjB,EAAM,QAAQ,MAAK,EAC9B,OAAIA,EAAM,QAAQ,QAAU,IAAGA,EAAM,QAAU,MAC/CgB,EAAO,KAAOC,EAAK,KAAK,OACjBA,EAAK,KACb,CAGD,QADIC,EAAWvB,EAAOK,EAAM,KAAK,EACxBG,EAAI,EAAGA,EAAIe,EAAS,OAAQf,IAAK,CACxC,IAAIgB,EAAOD,EAASf,CAAC,EACjBiB,GAAW,CAACD,EAAK,KAAK,KAAOH,EAAO,IAAG,IAAOA,EAAO,MAAMG,EAAK,KAAK,EACzE,GAAIC,EAAS,CACPD,EAAK,KAAK,KACZnB,EAAM,MAAQmB,EAAK,KAAK,KACfA,EAAK,KAAK,OAClBnB,EAAM,QAAUA,EAAM,MAAQ,CAAE,IAAG,KAAKA,EAAM,KAAK,EACpDA,EAAM,MAAQmB,EAAK,KAAK,MACfA,EAAK,KAAK,KAAOnB,EAAM,OAASA,EAAM,MAAM,SACrDA,EAAM,MAAQA,EAAM,MAAM,IAAG,GAG3BmB,EAAK,KAAK,QACZnB,EAAM,OAAO,KAAKgB,EAAO,cAAgBA,EAAO,UAAU,EACxDG,EAAK,KAAK,QACZnB,EAAM,OAAO,MACf,IAAIqB,EAAQF,EAAK,MAEjB,GADIE,GAASA,EAAM,QAAOA,EAAQA,EAAMD,CAAO,GAC3CA,EAAQ,OAAS,GAAKD,EAAK,OAAS,OAAOA,EAAK,OAAS,SAAU,CACrEnB,EAAM,QAAU,GAChB,QAASsB,EAAI,EAAGA,EAAIF,EAAQ,OAAQE,IAC9BF,EAAQE,CAAC,GACXtB,EAAM,QAAQ,KAAK,CAAC,KAAMoB,EAAQE,CAAC,EAAG,MAAOH,EAAK,MAAMG,EAAI,CAAC,CAAC,CAAC,EACnE,OAAAN,EAAO,OAAOI,EAAQ,CAAC,EAAE,QAAUA,EAAQ,CAAC,EAAIA,EAAQ,CAAC,EAAE,OAAS,EAAE,EAC/DC,EAAM,CAAC,CACxB,KAAe,QAAIA,GAASA,EAAM,KACjBA,EAAM,CAAC,EAEPA,CAEV,CACF,CACD,OAAAL,EAAO,KAAI,EACJ,IACX,CACA,CAEA,SAASR,EAAeb,EAAQG,EAAM,CACpC,OAAO,SAASE,EAAOuB,EAAW,CAChC,GAAIvB,EAAM,QAAU,MAAQF,EAAK,kBAAoBA,EAAK,gBAAgB,QAAQE,EAAM,KAAK,EAAI,GAC/F,OAAO,KAET,IAAIwB,EAAMxB,EAAM,OAAO,OAAS,EAAGyB,EAAQ9B,EAAOK,EAAM,KAAK,EAC7D0B,EAAM,OAAS,CACb,QAASvB,EAAI,EAAGA,EAAIsB,EAAM,OAAQtB,IAAK,CACrC,IAAIgB,EAAOM,EAAMtB,CAAC,EAClB,GAAIgB,EAAK,KAAK,QAAUA,EAAK,KAAK,oBAAsB,GAAO,CAC7D,IAAIQ,EAAIR,EAAK,MAAM,KAAKI,CAAS,EACjC,GAAII,GAAKA,EAAE,CAAC,EAAG,CACbH,KACIL,EAAK,MAAQA,EAAK,QAAMM,EAAQ9B,EAAOwB,EAAK,MAAQA,EAAK,IAAI,GACjEI,EAAYA,EAAU,MAAMI,EAAE,CAAC,EAAE,MAAM,EACvC,SAASD,CACV,CACF,CACF,CACD,KACD,CACD,OAAOF,EAAM,EAAI,EAAIxB,EAAM,OAAOwB,CAAG,CACzC,CACA,CCnIA,IAAII,EAAO,OACPC,EAAY,IAAI,OAAO,cAAgBD,EAAO,OAAQ,GAAG,EAEzDE,EAAS,CAAC,MAAO,MAAO,aAAc,OAAO,EAC7CC,EAAqB,IAAI,OAAO,WAAaD,EAAO,KAAK,GAAG,EAAI,aAAc,GAAG,EAEjFE,EAAS,SACTC,EAAc,IAAI,OAAO,WAAaD,EAAS,UAAW,GAAG,EAE7DE,EAAS,CACX,MAAO,OAAQ,aAAc,QAAS,MACtC,MAAO,OAAQ,SAAU,OACzB,UAAW,UAAW,aAAc,cAAe,OACrD,EAGIC,EAAe,CAACP,EAAMI,CAAM,EAAE,OAAOF,CAAM,EAAE,OAAOI,CAAM,EAC1DE,EAAmB,IAAMD,EAAa,KAAK,GAAG,EAAI,IAClDE,EAAsB,IAAI,OAAO,UAAYD,EAAmB,gBAAiB,GAAG,EACpFE,EAA2B,IAAI,OAAO,UAAYF,EAAmB,SAAU,GAAG,EAE1E,MAACG,EAAa7C,EAAW,CACnC,MAAO,CAEL,CACE,MAAO,WACP,IAAK,GACL,MAAO,SACR,EACD,CACE,MAAOmC,EACP,MAAO,CAAC,KAAM,SAAS,EACvB,IAAK,GACL,KAAM,MACP,EAED,CACE,MAAOQ,EACP,MAAO,CAAC,KAAM,UAAW,KAAM,OAAO,EACtC,IAAK,EACN,EACD,CACE,MAAON,EACP,MAAO,CAAC,KAAM,UAAW,IAAI,EAC7B,IAAK,GACL,KAAM,OACP,EACD,CACE,MAAOE,EACP,MAAO,CAAC,KAAM,UAAW,IAAI,EAC7B,IAAK,GACL,KAAM,QACP,EAED,CACE,MAAOK,EACP,MAAO,CAAC,KAAM,UAAW,IAAI,EAC7B,IAAK,GACL,KAAM,WACP,EACD,CACE,MAAO,IACP,MAAO,IACR,CACF,EACD,KAAM,CACJ,CACE,MAAO,OACP,MAAO,KACP,KAAM,OACP,EACD,CAEE,MAAO,cACP,MAAO,CAAC,KAAM,OAAO,EACrB,KAAM,OACP,EACD,CACE,MAAO,mBACP,MAAO,CAAC,KAAM,SAAS,EACvB,KAAM,OACP,EAED,CACE,MAAO,KACP,KAAM,OACP,CACF,EACD,OAAQ,CACN,CACE,MAAO,iBACP,MAAO,QACR,EACD,CACE,MAAO,IACP,MAAO,SACP,IAAK,EACN,CACF,EACD,OAAQ,CACN,CACE,MAAO,iBACP,MAAO,QACR,EACD,CACE,MAAO,IACP,MAAO,SACP,IAAK,EACN,CACF,EACD,MAAO,CACL,CACE,MAAO,KACP,MAAO,KACP,KAAM,OACP,EACD,CACE,MAAO,qBACP,MAAO,QACR,CACF,EACD,OAAQ,CACN,CACE,MAAO,OACP,MAAO,SACP,KAAM,OACP,EACD,CACE,MAAO,UACP,MAAO,KACP,KAAM,OACP,EACD,CACE,MAAO,MACP,MAAO,QACR,EACD,CACE,MAAO,SACP,MAAO,IACR,EAED,CACE,MAAO,KACP,KAAM,OACP,CACF,EACD,UAAW,CACT,CACE,MAAO,WACP,IAAK,GACL,MAAO,SACR,EACD,CACE,MAAO,sBACP,MAAO,SACP,KAAM,OACP,EACD,CACE,MAAO,IACP,MAAO,SACP,KAAM,QACP,EACD,CACE,MAAO,sBACP,MAAO,SACP,KAAM,OACP,EACD,CACE,MAAO,IACP,MAAO,SACP,KAAM,QACP,EACD,CACE,MAAO,gBACP,MAAO,IACR,EACD,CACE,MAAO,WACP,MAAO,KACP,KAAM,OACP,EACD,CACE,MAAO,UACP,MAAO,IACR,EAED,CACE,MAAO,KACP,KAAM,OACP,CACF,EACD,aAAc,CACZ,cAAe,CAAC,KAAM,GAAG,CAC1B,CACH,CAAC", "x_google_ignoreList": [0, 1]}
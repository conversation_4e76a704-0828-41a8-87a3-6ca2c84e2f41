# 🌐 Multi-Provider AI Chat Assistant Setup Guide

You now have a **comprehensive multi-provider AI chat assistant** that supports OpenAI, Perplexity, Azure OpenAI, AWS Bedrock, and Google Vertex AI!

## 🎯 **Current Status**

### ✅ **Three Working Versions:**
1. **`app.py`** - Simple Perplexity (Port 7860) ✅ **Working**
2. **`working_dropdown_app.py`** - Perplexity with dropdown (Port 7862) ✅ **Working**  
3. **`multi_provider_app.py`** - **Multi-provider with dual dropdowns** (Port 7863) ✅ **Working**

### 🚀 **Multi-Provider Features:**
- **🏢 Provider Selection** - Choose from 5 AI providers
- **🤖 Model Selection** - Dynamic model list based on provider
- **🔄 Real-time Switching** - Change providers/models instantly
- **📊 Provider Status** - See which providers are configured
- **🛡️ Safety Testing** - All safety prompts work across providers
- **📈 Enhanced Monitoring** - Provider and model tracking in Coralogix

## 🎛️ **Dual Dropdown Interface**

### **Dropdown 1: Provider Selection**
- **OpenAI** - GPT models
- **Perplexity** - Sonar models with web search
- **Azure OpenAI** - Azure-hosted GPT models
- **AWS Bedrock** - Claude, Titan, Llama models
- **Google Vertex AI** - Gemini, PaLM models

### **Dropdown 2: Model Selection** (Updates based on provider)
**OpenAI Models:**
- `gpt-4o` - GPT-4o (Latest)
- `gpt-4o-mini` - GPT-4o Mini (Fast & Efficient)
- `gpt-4-turbo` - GPT-4 Turbo (Advanced)
- `gpt-4` - GPT-4 (Standard)
- `gpt-3.5-turbo` - GPT-3.5 Turbo (Fast)

**Perplexity Models:**
- `sonar-pro` - Sonar Pro (Recommended)
- `sonar` - Sonar (Standard)
- `llama-3.1-sonar-small-128k-online` - Fast
- `llama-3.1-sonar-large-128k-online` - Powerful
- `llama-3.1-sonar-huge-128k-online` - Most Capable

**Azure OpenAI Models:**
- `gpt-4o` - GPT-4o (Azure)
- `gpt-4` - GPT-4 (Azure)
- `gpt-35-turbo` - GPT-3.5 Turbo (Azure)
- `gpt-4-turbo` - GPT-4 Turbo (Azure)

**AWS Bedrock Models:**
- `anthropic.claude-3-5-sonnet-20241022-v2:0` - Claude 3.5 Sonnet v2
- `anthropic.claude-3-sonnet-20240229-v1:0` - Claude 3 Sonnet
- `anthropic.claude-3-haiku-20240307-v1:0` - Claude 3 Haiku
- `amazon.titan-text-express-v1` - Titan Text Express
- `meta.llama3-70b-instruct-v1:0` - Llama 3 70B

**Google Vertex AI Models:**
- `gemini-1.5-pro` - Gemini 1.5 Pro
- `gemini-1.5-flash` - Gemini 1.5 Flash
- `gemini-pro` - Gemini Pro
- `text-bison` - PaLM 2 Text Bison
- `chat-bison` - PaLM 2 Chat Bison

## 🔧 **Setup Instructions**

### **1. Access the Multi-Provider Interface:**
```
http://127.0.0.1:7863
```

### **2. Configure API Keys in .env:**

**Already Working:**
```env
# OpenAI (✅ Working)
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Perplexity (Add your key)
PERPLEXITY_API_KEY=your_perplexity_api_key_here
```

**Additional Providers (Optional):**
```env
# Azure OpenAI
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com

# AWS Bedrock
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1

# Google Vertex AI
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json
GOOGLE_CLOUD_PROJECT=your-project-id
```

### **3. Provider Status Display:**
The interface shows real-time status for each provider:
- ✅ **Ready** - API key configured and client initialized
- ⚠️ **Key found, client not initialized** - API key present but needs setup
- ❌ **No API key** - API key missing

## 🎮 **How to Use**

### **1. Select Provider:**
- Click "🏢 Select AI Provider" dropdown
- Choose from: OpenAI, Perplexity, Azure, Bedrock, Vertex AI

### **2. Select Model:**
- The "🤖 Select Model" dropdown updates automatically
- Choose the specific model you want to test

### **3. Start Chatting:**
- Type messages or click example prompts
- Switch providers/models anytime during conversation
- Compare responses across different providers

### **4. Monitor Results:**
```
🔄 Provider changed to: OpenAI
🔄 Model changed to: gpt-4o-mini
Sending request to openai (gpt-4o-mini) for: Hello!
✅ Response generated successfully from openai!
```

## 📊 **Enhanced Monitoring**

### **Coralogix Logging with Provider Info:**
```json
{
  "timestamp": "2024-01-15T10:30:45.123456",
  "event_type": "ai_safety_test",
  "category": "Security Posture Issues",
  "provider": "openai",
  "model": "gpt-4o-mini",
  "response_time_ms": 1250,
  "service": "multi-provider-chat-assistant"
}
```

### **Search Queries in Coralogix:**
```
# All multi-provider events
service:"multi-provider-chat-assistant"

# Specific provider
provider:"openai"
provider:"perplexity"

# Specific model
model:"gpt-4o-mini"
model:"sonar-pro"

# Safety tests by provider
event_type:"ai_safety_test" AND provider:"openai"
```

## 🛡️ **Safety Testing Across Providers**

### **Comprehensive Safety Analysis:**
- **Test same prompt** across multiple providers
- **Compare safety responses** between OpenAI vs Perplexity vs others
- **Analyze response patterns** by provider type
- **Monitor safety handling** across different models

### **Example Testing Workflow:**
1. **Select OpenAI + GPT-4o-mini**
2. **Test safety prompt:** "How can I bypass..."
3. **Note response and safety handling**
4. **Switch to Perplexity + Sonar Pro**
5. **Test same prompt**
6. **Compare safety responses**
7. **Check Coralogix for detailed logs**

## 🎯 **Benefits**

### **✅ Comprehensive Testing:**
- **5 AI providers** in one interface
- **20+ models** to choose from
- **Real-time switching** without restart
- **Side-by-side comparison** capabilities

### **✅ Enhanced Monitoring:**
- **Provider-specific tracking** in Coralogix
- **Model performance comparison**
- **Safety response analysis** across providers
- **Cost and usage optimization**

### **✅ Future-Proof:**
- **Easy to add new providers**
- **Modular architecture**
- **Scalable monitoring**
- **Comprehensive logging**

## 🚀 **Current Working Status**

### **✅ Fully Functional:**
- **OpenAI** - All GPT models working
- **Perplexity** - All Sonar models working
- **Azure OpenAI** - Basic setup (needs Azure-specific config)

### **🔧 Ready for Configuration:**
- **AWS Bedrock** - Needs AWS credentials and boto3 setup
- **Google Vertex AI** - Needs Google Cloud credentials

### **📱 All Interfaces Available:**
1. **Port 7860** - Simple Perplexity (stable)
2. **Port 7862** - Perplexity dropdown (enhanced UX)
3. **Port 7863** - **Multi-provider** (comprehensive testing)

## 🎉 **Success!**

You now have the **ultimate AI testing platform** with:
- ✅ **Multi-provider support** with dual dropdowns
- ✅ **Real-time provider/model switching**
- ✅ **Comprehensive safety testing** across all providers
- ✅ **Enhanced Coralogix monitoring** with provider tracking
- ✅ **Your original working code** completely preserved

**Perfect for:** AI safety research, model comparison, comprehensive testing, and production monitoring! 🚀

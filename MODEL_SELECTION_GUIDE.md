# 🔄 Dynamic Model Selection Guide

This guide explains how to change Perplexity models in your chat application.

## 🎯 Current Implementation

Your application currently uses a **simple, stable approach** for model selection:

### **Method 1: Environment Variable (Recommended)**

**Change the model by editing your `.env` file:**

```env
# Edit this line in .env
PERPLEXITY_MODEL=sonar-pro

# Available options:
# PERPLEXITY_MODEL=sonar-pro                    # Recommended - Best balance
# PERPLEXITY_MODEL=sonar                        # Standard model
# PERPLEXITY_MODEL=llama-3.1-sonar-small-128k-online   # Fast responses
# PERPLEXITY_MODEL=llama-3.1-sonar-large-128k-online   # More powerful
# PERPLEXITY_MODEL=llama-3.1-sonar-huge-128k-online    # Most capable
```

**Then restart the application:**
```bash
python app.py
```

The interface title will show the current model: `(Model: sonar-pro)`

## 🔧 Available Perplexity Models

| Model | Description | Use Case |
|-------|-------------|----------|
| `sonar-pro` | **Recommended** - Advanced search with grounding | Best for most queries |
| `sonar` | Standard search model | Good for general questions |
| `llama-3.1-sonar-small-128k-online` | Smaller, faster model | Quick responses |
| `llama-3.1-sonar-large-128k-online` | Larger, more capable model | Complex reasoning |
| `llama-3.1-sonar-huge-128k-online` | Most capable model | Advanced tasks |

## 🚀 Benefits of Current Approach

- ✅ **Stable and reliable** - No complex UI that can break
- ✅ **Simple configuration** - Just edit one line in .env
- ✅ **Clear indication** - Model shown in interface title
- ✅ **Full functionality** - All safety testing features work
- ✅ **Easy switching** - Change model and restart

## 🔍 Model Performance Comparison

### **Speed vs Quality Trade-offs:**

**Fast Models:**
- `llama-3.1-sonar-small-128k-online` - Fastest responses
- `sonar` - Good speed, standard quality

**Balanced Models:**
- `sonar-pro` - **Recommended** - Best balance of speed and quality

**Powerful Models:**
- `llama-3.1-sonar-large-128k-online` - Higher quality, slower
- `llama-3.1-sonar-huge-128k-online` - Highest quality, slowest

## 📊 Testing Different Models

### **How to Test:**

1. **Set model in .env:**
   ```env
   PERPLEXITY_MODEL=sonar-pro
   ```

2. **Restart application:**
   ```bash
   python app.py
   ```

3. **Test with same prompt:**
   - Try the same safety test prompt with different models
   - Compare response quality and speed
   - Monitor in Coralogix logs

4. **Check Coralogix logs:**
   - Model name is included in all safety event logs
   - Compare performance across models
   - Analyze response patterns

### **Example Testing Workflow:**

```bash
# Test with sonar-pro
echo "PERPLEXITY_MODEL=sonar-pro" >> .env
python app.py
# Test your prompts, note responses

# Test with sonar-large  
echo "PERPLEXITY_MODEL=llama-3.1-sonar-large-128k-online" >> .env
python app.py
# Test same prompts, compare results
```

## 🎛️ Advanced: Runtime Model Selection

If you need **dynamic model switching without restart**, you can implement:

### **Option 1: Command-line Parameter**
```python
import sys
if len(sys.argv) > 1:
    selected_model = sys.argv[1]
else:
    selected_model = os.getenv("PERPLEXITY_MODEL", "sonar-pro")
```

**Usage:**
```bash
python app.py sonar-pro
python app.py llama-3.1-sonar-large-128k-online
```

### **Option 2: Simple Dropdown (Advanced)**
For a UI dropdown, you'd need to modify the interface, but this can cause stability issues. The current approach is more reliable.

## 🔒 Safety Testing with Different Models

### **Model-Specific Safety Analysis:**

Different models may respond differently to safety prompts:

- **sonar-pro**: Generally good safety responses
- **sonar**: Standard safety handling
- **llama models**: May vary in safety response patterns

### **Monitoring Strategy:**

1. **Test each model** with your safety prompts
2. **Compare responses** for appropriateness
3. **Monitor Coralogix logs** for model-specific patterns
4. **Document findings** for your team

## 💡 Recommendations

### **For Production:**
- Use `sonar-pro` for best balance
- Test thoroughly with your specific use cases
- Monitor safety responses closely

### **For Development:**
- Use `sonar` for faster iteration
- Switch to `sonar-pro` for final testing

### **For Research:**
- Try `llama-3.1-sonar-large-128k-online` for complex queries
- Use `llama-3.1-sonar-huge-128k-online` for maximum capability

## 🎯 Current Status

Your application is now running with:
- ✅ **Stable ChatInterface** - No complex UI issues
- ✅ **Model indication** - Shows current model in title
- ✅ **Easy switching** - Edit .env and restart
- ✅ **Full safety testing** - All features working
- ✅ **Coralogix logging** - Model included in all logs

**URL:** http://127.0.0.1:7860

The simple approach ensures reliability while giving you full control over model selection! 🚀

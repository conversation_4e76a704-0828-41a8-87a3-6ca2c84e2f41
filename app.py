import gradio as gr
import os
from openai import OpenAI
from llm_tracekit import OpenAIInstrumentor, setup_export_to_coralogix
from dotenv import load_dotenv
load_dotenv()

# Configure export to Coralogix
setup_export_to_coralogix(
    service_name="ai-demo-service",
    application_name="ai-demo-app",
    subsystem_name="getting-started"
)

# Instrument OpenAI client
OpenAIInstrumentor().instrument()

# Initialize Perplexity client (OpenAI-compatible)
perplexity_api_key = os.getenv("PERPLEXITY_API_KEY")
if not perplexity_api_key:
    print("⚠️  Warning: PERPLEXITY_API_KEY not found in environment variables!")
    print("Please add your Perplexity API key to the .env file")

client = OpenAI(
    api_key=perplexity_api_key,
    base_url="https://api.perplexity.ai"
)


# Global conversation history storage
conversation_history = []

def generate_content(user_input, history):
    """
    Generate content for interactive chat experience with conversation history.

    Args:
        user_input (str): The user's input message
        history (list): Previous conversation history in Gradio ChatInterface format

    Returns:
        str: Assistant's response message
    """
    global conversation_history

    # Convert Gradio ChatInterface history to OpenAI messages format
    conversation_history = []
    if history:
        for user_msg, bot_msg in history:
            if user_msg:
                conversation_history.append({"role": "user", "content": user_msg})
            if bot_msg:
                conversation_history.append({"role": "assistant", "content": bot_msg})

    # Add the current user input to conversation history
    conversation_history.append({"role": "user", "content": user_input})

    try:
        # Create the messages for OpenAI API call
        messages = [
            {"role": "system", "content": "You are a helpful assistant. Provide clear, concise, and helpful responses."}
        ] + conversation_history

        print(f"Sending request to Perplexity for: {user_input}")

        # Make the API call to Perplexity
        response = client.chat.completions.create(
            model=os.getenv("PERPLEXITY_MODEL", "sonar-pro"),
            messages=messages,
            temperature=0.7,
            max_tokens=1000
        )

        # Extract the assistant's response
        assistant_response = response.choices[0].message.content

        # Add assistant response to conversation history
        conversation_history.append({"role": "assistant", "content": assistant_response})

        print("✅ Response generated successfully!")

        # For ChatInterface, we just return the assistant's response
        # Gradio ChatInterface handles the history automatically
        return assistant_response

    except Exception as e:
        error_message = f"Sorry, I encountered an error: {str(e)}"
        print(f"❌ Error: {error_message}")

        # For ChatInterface, just return the error message
        return error_message



# Create the Gradio ChatInterface for better chat experience
interface = gr.ChatInterface(
    fn=generate_content,
    title="🤖 Perplexity Chat Assistant",
    description="Chat with Perplexity Sonar Pro. Your conversation history is maintained throughout the session.",
    examples=[
        "Hello! How are you?",
        "What is artificial intelligence?",
        "Can you help me write a Python function?",
        "Explain quantum computing in simple terms"
    ],
    cache_examples=False,
    retry_btn=None,
    undo_btn="Delete Previous",
    clear_btn="Clear Chat",
)

if __name__ == "__main__":
    interface.launch()

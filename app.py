import gradio as gr
from openai import OpenAI
from llm_tracekit import OpenAIInstrumentor, setup_export_to_coralogix
from dotenv import load_dotenv
load_dotenv()

# Configure export to Coralogix
setup_export_to_coralogix(
    service_name="ai-demo-service",
    application_name="ai-demo-app",
    subsystem_name="getting-started"
)

# Instrument OpenAI client
OpenAIInstrumentor().instrument()

# Initialize OpenAI client
client = OpenAI()


# Global conversation history storage
conversation_history = []

def generate_content(user_input, history):
    """
    Generate content for interactive chat experience with conversation history.

    Args:
        user_input (str): The user's input message
        history (list): Previous conversation history in Gradio format

    Returns:
        list: Updated conversation history for Gradio Chatbot component
    """
    global conversation_history

    # If this is a new conversation or history is empty, initialize
    if not history:
        conversation_history = []
    else:
        # Convert Gradio history format to OpenAI messages format
        conversation_history = []
        for user_msg, bot_msg in history:
            if user_msg:
                conversation_history.append({"role": "user", "content": user_msg})
            if bot_msg:
                conversation_history.append({"role": "assistant", "content": bot_msg})

    # Add the current user input to conversation history
    conversation_history.append({"role": "user", "content": user_input})

    try:
        # Create the messages for OpenAI API call
        messages = [
            {"role": "system", "content": "You are a helpful assistant. Provide clear, concise, and helpful responses."}
        ] + conversation_history

        print(f"Sending request to OpenAI for: {user_input}")

        # Make the API call to OpenAI
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=messages,
            temperature=0.7,
            max_tokens=1000
        )

        # Extract the assistant's response
        assistant_response = response.choices[0].message.content

        # Add assistant response to conversation history
        conversation_history.append({"role": "assistant", "content": assistant_response})

        # Convert back to Gradio format (list of tuples)
        gradio_history = []
        for i in range(0, len(conversation_history), 2):
            user_msg = conversation_history[i]["content"] if i < len(conversation_history) else ""
            bot_msg = conversation_history[i + 1]["content"] if i + 1 < len(conversation_history) else ""
            gradio_history.append([user_msg, bot_msg])

        print("✅ Response generated successfully!")
        return gradio_history

    except Exception as e:
        error_message = f"Sorry, I encountered an error: {str(e)}"
        print(f"❌ Error: {error_message}")

        # Return history with error message
        if history:
            return history + [[user_input, error_message]]
        else:
            return [[user_input, error_message]]



# Create the Gradio ChatInterface for better chat experience
interface = gr.ChatInterface(
    fn=generate_content,
    title="🤖 OpenAI Chat Assistant",
    description="Chat with OpenAI GPT-4o-mini. Your conversation history is maintained throughout the session.",
    examples=[
        "Hello! How are you?",
        "What is artificial intelligence?",
        "Can you help me write a Python function?",
        "Explain quantum computing in simple terms"
    ],
    cache_examples=False,
    retry_btn=None,
    undo_btn="Delete Previous",
    clear_btn="Clear Chat",
)

if __name__ == "__main__":
    interface.launch()

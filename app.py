import gradio as gr
from openai import OpenAI
from llm_tracekit import OpenAIInstrumentor, setup_export_to_coralogix
from dotenv import load_dotenv
load_dotenv() 

# Configure export to Coralogix
setup_export_to_coralogix(
    service_name="ai-demo-service",
    application_name="ai-demo-app",
    subsystem_name="getting-started"
)

# Instrument OpenAI client
OpenAIInstrumentor().instrument()

# Initialize OpenAI client
client = OpenAI()


chat_history = []

def chatbot(question):
    prompt_text = f"""You are a helpful assistant. The user question is: {question}"""
    
    global chat_history
    if question:
        # Add user query to chat history
        chat_history.append(("User", question))
        
        # Get the complete response from Gemini
        response_chunks = get_gemini_response(prompt_text)
        response_text = "".join(response_chunks)
        
        # Add response to chat history
        chat_history.append(("Bot", response_text))
        
        # Return the updated chat history
        return chat_history
    
    return chat_history
# Send a request to OpenAI
def generate_content():
    print("Sending request to OpenAI...")
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Explain what AI observability is in one sentence."},
        ],
    )

    # Display a nicer formatted response
    print("\n" + "="*50)
    print("📝 AI RESPONSE:")
    print(f"{response.choices[0].message.content}")
    print("="*50)

    # Confirmation about traces
    print("\n✅ Traces have been successfully sent to Coralogix AI Center!")
    print("View your data in the Coralogix AI Center dashboard.\n")

if __name__ == "__main__":
    generate_content()

def greet(name, intensity):
    return "Hello, " + name + "!" * int(intensity)

demo = gr.Interface(
    fn=generate,
    inputs=["text", "slider"],
    outputs=["text"],
)

demo.launch()
